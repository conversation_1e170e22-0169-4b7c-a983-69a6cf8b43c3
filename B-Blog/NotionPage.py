class NotionPage:
    def __init__(self, title, content, tags: [], keywords: [], create_time, is_public):
        self.title = title
        self.content = content
        self.tags = tags
        self.keywords = keywords
        self.create_time = create_time
        self.is_public = is_public

    def __str__(self):
        return (f"Title: {self.title}\n"
                f"Content: {self.content}\n"
                f"Tags: {', '.join(self.tags)}\n"
                f"Keywords: {', '.join(self.keywords)}\n"
                f"Create Time: {self.create_time}\n"
                f"Is Public: {'Yes' if self.is_public else 'No'}")


# 示例使用
page = NotionPage(
    title="Sample Page",
    content="Page1",
    tags=["Tag1", "Tag2"],
    keywords=["Keyword1", "Keyword2"],
    create_time="2023-11-18",
    is_public=True
)

print(page)
