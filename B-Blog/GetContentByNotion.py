from notion_client import Client


def to_markdown(block):
    """
    将Notion块转换为Markdown格式的文本。
    """
    block_type = block["type"]
    block_content = block.get(block_type, {})
    markdown_text = ""

    if block_type in ["paragraph", "heading_1", "heading_2", "heading_3"]:
        texts = block_content.get("rich_text", [])
        for text in texts:
            content = text["plain_text"]
            if block_type == "heading_1":
                markdown_text += f"# {content}\n"
            elif block_type == "heading_2":
                markdown_text += f"## {content}\n"
                # # 标题二zhong添加截断标记
                markdown_text += "\n<!-- truncate -->\n"
                # insert_content = "\n<!-- truncate -->"
                # content = insert_string_at_position(content, 100, insert_content)
            elif block_type == "heading_3":
                markdown_text += f"### {content}\n"
            else:
                markdown_text += f"{content} "
        markdown_text += "\n"

    elif block_type == "text_with_link":
        texts = block_content.get("rich_text", [])
        for text in texts:
            if 'href' in text:
                # Assuming 'href' is the key where the link is stored
                markdown_text += f"[{text['plain_text']}]({text['href']}) "
            else:
                markdown_text += text['plain_text']
        markdown_text += "\n"  # Add a newline after processing all texts in the block

    elif block_type == "bulleted_list_item" or block_type == "numbered_list_item":
        texts = block_content.get("rich_text", [])
        for text in texts:
            markdown_text += f"- {text['plain_text']}\n"

    elif block_type == "to_do":
        checked = block_content.get("checked", False)
        checkbox = "[x]" if checked else "[ ]"
        texts = block_content.get("rich_text", [])
        for text in texts:
            markdown_text += f"{checkbox} {text['plain_text']}\n"

    elif block_type == "toggle":
        texts = block_content.get("rich_text", [])
        for text in texts:
            markdown_text += f"> {text['plain_text']}\n"

    elif block_type == "image":
        image_url = block_content.get("file", {}).get("url", "")
        if len(image_url) == 0:
            image_url = block_content.get("external").get('url')
        markdown_text += f"![Image]({image_url})\n"

    elif block_type == "code":
        code_text = block_content.get("rich_text", [])[0].get("plain_text", "")
        language = block_content.get("language", "")
        markdown_text += f"```{language}\n{code_text}\n```\n"

    # 针对video类型的处理
    elif block_type == "video":
        # 提取视频的 URL
        video_url = block_content.get("external", {}).get("url", "")

        # 将视频 URL 转换为 Markdown 格式
        if video_url:
            markdown_text += f"![Video]({video_url})\n"
        else:
            markdown_text += "Video URL not found\n"

    # 针对quote类型的处理
    elif block_type == "quote":
        texts = block_content.get("rich_text", [])
        for text in texts:
            markdown_text += f"> {text['plain_text']}\n"

    # 针对callout类型的处理
    elif block_type == "callout":
        # 获取 'callout' 块的文本内容
        texts = block_content.get("rich_text", [])

        # 假设 'callout' 块可能包含一个表情符号或图标
        icon = block_content.get("icon", {}).get("emoji", "")

        # 将表情符号或图标添加到 Markdown 文本的开始
        markdown_text += f"{icon} "

        # 添加 'callout' 块的文本内容
        for text in texts:
            markdown_text += text.get("plain_text", "")

        # 添加换行符来结束 'callout' 块
        markdown_text += "\n"

    return markdown_text


def retrieve_and_convert_to_markdown(block_id, notion):
    """
    递归检索Notion块并转换为Markdown格式。
    """
    markdown_content = ""
    blocks = notion.blocks.children.list(block_id=block_id)["results"]
    # 获取blocks不重复类型
    block_types = set()
    for block in blocks:
        block_types.add(block["type"])
    print(f'block_types类型有: {block_types}')

    for block in blocks:
        markdown_content += to_markdown(block)
        if block.get("has_children", False):
            markdown_content += retrieve_and_convert_to_markdown(block["id"], notion)

    return markdown_content


# 初始化Notion客户端
global_token = "**************************************************"
# 页面ID
page_id = "89ed96cdda4b4a0285112c1d7ea6c3ee"

if __name__ == '__main__':
    # 获取Markdown格式的页面内容
    notion = Client(auth=global_token)
    markdown_content = retrieve_and_convert_to_markdown(page_id, notion)
    print(markdown_content)
