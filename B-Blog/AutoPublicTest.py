"""
1. 自动化获取内容-Notion获取数据
1.1 Token: **************************************************
1.1 数据库ID: 4852406a7ecc4e44ad9c30a3e28c3c44
1.2 https://zhuanlan.zhihu.com/p/570140268
2. 自动更正内容格式
3. 发布git
4. 验证结果
"""

import json

import numpy as np
import pyecharts.options as opts
import requests
from pyecharts.charts import Pie


class Notion_Data:
    def __init__(self):
        print("欢迎来到Notion数据可视化分析！")

    # 获取用户数据库ID及Token密钥
    # 数据处理
    def Notion_Data_deal(self, Database_ID: str, Token_KEY: str):
        base_url = "https://api.notion.com/v1/databases/"
        """
        接口匹配
        """
        headers = {
            "Authorization": "Bearer " + Token_KEY,
            "accept": "application/json",
            "Notion-Version": "2022-06-28"  # Notion版本号
        }
        query = {"filter": {"property": "出版社", "checkbox": {"equals": True}}}
        # 获取Notion页面下的详细信息 https://developers.notion.com/reference/post-database-query
        response = requests.post(base_url + Database_ID + "/query", headers=headers, data=query)
        jst = json.loads(response.text)
        return jst

    def Json_Data_deal(self, Database_ID: str, Token_KEY: str, Type: str):  # 类型仅限为：书籍、影片
        dict = self.Notion_Data_deal(Database_ID, Token_KEY)
        """获取到数据列"""
        data_len = len(dict['results'])
        # 统计类型数量，进行后续图形比例
        # 书籍以出版社为划分，影片以类别划分
        if Type == "影片":
            """获取到数据列"""
            dic = {}
            dtc = {}
            for i in range(data_len):
                name = dict['results'][i]['properties']['片名']['title'][0]['plain_text']
                select = dict['results'][i]['properties']['类别']['multi_select']
                classify = []
                for j in range(len(select)):
                    classify.append(dict['results'][i]['properties']['类别']['multi_select'][j]['name'])
                dic[name] = classify  # 类别
            ls = list(dic.items())  # 获取数据数量
            for i in range(len(ls)):
                for j in range(len(ls[i][1])):
                    if ls[i][1][j] in "奇幻":
                        ls[i][1][j] = "科幻"
                    if ls[i][1][j] in "惊悚" or ls[i][1][j] in "悬疑":
                        ls[i][1][j] = "恐怖"
                    if ls[i][1][j] in "故事" or ls[i][1][j] in "扫黑" or ls[i][1][j] in "生活":
                        ls[i][1][j] = "剧情"
                    if ls[i][1][j] in "运动":
                        ls[i][1][j] = "冒险"
                    dtc[ls[i][1][j]] = dtc.get(ls[i][1][j], 0) + 1
            lt = list(dtc.items())
            print("有效数据:" + str(len(dic)))
            return lt

        if Type == "书籍":
            dic = {}
            for i in range(data_len):
                try:
                    name = (dict['results'][i]['properties']['出版社']['select']['name'])
                    dic[name] = dic.get(name, 0) + 1
                except Exception:
                    pass
                continue
            ls = list(dic.items())
            return ls

    def Notion_Visualization(self, Database_ID: str, Token_KEY: str, Type: str):  # 交互式可视化图表
        data = self.Json_Data_deal(Database_ID, Token_KEY, Type)
        lenght = len(data)
        sum = 0
        count_num, name = [], []
        for i in range(lenght):
            sum += int(data[i][1])
            name.append(data[i][0])
        for j in range(lenght):
            a = round(int(data[j][1]) / sum * 100, 2)  # 保留为两位小数
            count_num.append(a)
        np.set_printoptions(precision=2)
        data_pair_temp = [list(data) for data in zip(name, count_num)]
        p = (
            Pie()  # 实例化
            .add(
                series_name=Type,  # 系列名称
                data_pair=data_pair_temp,  # 馈入数据
                radius="65%",  # 饼图半径比例
                center=["50%", "50%"],  # 饼图中心坐标
                label_opts=opts.LabelOpts(is_show=False, position="center"),  # 标签位置
            )
            .set_global_opts(legend_opts=opts.LegendOpts(is_show=True))  # 不显示图示
            .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {c}"))  # 标签颜色
            .render(Type + ".html")  # 渲染文件及其名称
            # .render_notebook()
        )
        print("文件已保存在当前程序目录!")
        print(p)

        """
        # 静态可视化图表
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 显示中文标签,处理中文乱码问题
        plt.rcParams['axes.unicode_minus'] = False  # 坐标轴负号的处理
        plt.pie(x=count_num, labels=name, autopct='%.2f%%')
        plt.legend(loc='center')
        plt.savefig("./Image/Vedio.png")
        plt.show()
        # print(name, count_num)
        """


from notion_client import Client


def get_block():
    # 认证
    notion = Client(auth="**************************************************")
    # 数据库ID
    database_id = "4852406a7ecc4e44ad9c30a3e28c3c44"
    list = notion.databases.query(database_id=database_id)
    notion.pages.retrieve(page_id="c1d117f9ce6d4ec5b598006c02a2a773")
    block_content = notion.blocks.retrieve(block_id='c1d117f9-ce6d-4ec5-b598-006c02a2a773')

    # 获取子页面的block的ID
    subpage_block_id = "c1d117f9-ce6d-4ec5-b598-006c02a2a773"

    # 获取子页面的block内容
    subpage_block_content = notion.blocks.retrieve(block_id=subpage_block_id)

    # 检查是否是子页面类型
    if subpage_block_content["type"] == "child_page":
        # 获取子页面中所有block的列表
        child_blocks = notion.blocks.children.list(block_id=subpage_block_id)

        # 遍历blocks来获取它们的内容
        for block in child_blocks["results"]:
            # 根据block类型处理每个block的内容
            if block["type"] == "paragraph":
                text_contents = block["paragraph"]["rich_text"]
                for content in text_contents:
                    if content['type'] == 'text':
                        print(content['text']['content'])
            # 这里可以添加更多的条件分支来处理不同类型的blocks


    # 打印block的内容
    # Notion API返回的block结构可能包含多种类型，因此你需要根据block的类型来提取正文内容
    # 以下是假设block类型为paragraph的示例
    if block_content["type"] == "paragraph":
        text_content = block_content["paragraph"]["text"]
        for text in text_content:
            # 根据你的需求，可能需要处理不同的文本类型，例如普通文本、提及、链接等
            if text['type'] == 'text':
                print(text['text']['content'])
    # 获取页面信息
    page = notion.pages.get(page_id="c1d117f9ce6d4ec5b598006c02a2a773")
    # 获取块和子块
    block_ids = page.get("content")
    for block_id in block_ids:
        block = notion.blocks.retrieve(block_id)
        print(block)


if __name__ == '__main__':
    # get_block()
    # 测试一
    # text = Notion_Data()
    # # Database_ID = str(input("请输入数据库ID:\n"))
    # # Token_KEY = str(input("请输入Token密钥:\n"))
    # # Type = str(input("请输入类型(仅限书籍、影片):\n"))
    # Database_ID = "4852406a7ecc4e44ad9c30a3e28c3c44"
    # Token_KEY = "**************************************************"
    # Type = "影片"
    # text.Notion_Visualization(Database_ID, Token_KEY, Type)
    # # print(text.Json_Data_deal(Database_ID, Token_KEY, Type))



    # 页面ID
    page_id = ""

    # 获取页面内容
    page_content = retrieve_page_content(page_id, notion)
    print(page_content)

    # global_database_id = "4852406a7ecc4e44ad9c30a3e28c3c44"
    # 书签数据库
    global_database_id = "fe8f4f9ac8a54f73ad78e46982a9cf04"
    global_page_id = "89ed96cdda4b4a0285112c1d7ea6c3ee"

    # 初始化Notion客户端
    notion = Client(auth=global_token)

    # 页面ID
    page_id = "89ed96cdda4b4a0285112c1d7ea6c3ee"
