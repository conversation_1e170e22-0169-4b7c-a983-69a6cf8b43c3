import os
import re

from notion_client import Client
from translate import Translator

from GetContentByNotion import retrieve_and_convert_to_markdown
from GitUtil import push_git
from NotionPage import NotionPage
from TimeUtil import time_format

global_token = "**************************************************"
# 书签数据库-bookmark-data
global_database_id = "fe8f4f9ac8a54f73ad78e46982a9cf04"
global_dir = "/Users/<USER>/fuwenhao/Github/blog-Docusaurus2/blog/notion/"
global_page_limit = 100
global_notion = Client(auth=global_token)


class Notion_Data:
    def __init__(self):
        print('开始Notion自动化获取数据...')

    # 检查是否发布
    def check_ispublic(self):
        query_results = global_notion.databases.query(database_id=global_database_id)
        for page in query_results["results"]:
            if page["object"] == "page":
                page_id = page["id"]
                select_value = page["properties"]["isPublic"]["select"]
                if select_value is None:
                    property_name = "isPublic"
                    update_payload = {
                        "properties": {
                            property_name: {
                                "select": {
                                    "name": "false"
                                }
                            }
                        }
                    }
                    global_notion.pages.update(page_id=page_id, **update_payload)
                    title = page["properties"]["title"]["title"][0]["plain_text"]
                    print(f'设置默认未发布,标题:{title}')

    # 1. 获取Notion文章正文内容
    def get_block_content(self):
        notion = global_notion
        # notion = Client(auth="**************************************************")
        query_results = notion.databases.query(database_id=global_database_id)
        subpage_ids = []
        # todo-fwh过滤条件添加-关于科技文章也放在Notion中
        # 数据全部设置成为未发布,同时保存到集合- 过滤条件
        for page in query_results["results"]:
            if page["object"] == "page":
                page_id = page["id"]
                isPublic_value = page["properties"]["isPublic"]["select"]['name']
                if page["properties"]["Types"]["select"] is None:
                    if isPublic_value == "false":
                        subpage_ids.append(page_id)
                    else:
                        continue
                else:
                    types_value = page["properties"]["Types"]["select"]['name']
                    if isPublic_value == "false" and types_value != "痞浩研究所":
                        subpage_ids.append(page_id)

        result_map = {}
        print(f"待发布文章数量为:{len(subpage_ids)}")
        for index, subpage_block_id in enumerate(subpage_ids):
            if index > global_page_limit:
                continue
            # 获取标题
            subpage_block_title = notion.pages.retrieve(page_id=subpage_block_id)
            title = subpage_block_title["properties"]["title"]["title"][0]["plain_text"]
            # 假设tags是一个多选属性
            if len(subpage_block_title["properties"]["Tags"]["multi_select"]) == 0:
                property_name = "Tags"
                new_tags = [
                    {"name": "Default"}
                ]
                update_payload = {
                    "properties": {
                        property_name: {
                            "multi_select": new_tags
                        }
                    }
                }
                notion.pages.update(page_id=subpage_block_id, **update_payload)
                tags = ["Default"]
            else:
                tags = [tag["name"] for tag in subpage_block_title["properties"]["Tags"]["multi_select"]]
            # keywords
            if len(subpage_block_title["properties"]["Keywords"]["multi_select"]) == 0:
                property_name = "Keywords"
                new_keywords = [
                    {"name": "Default"}
                ]
                update_payload = {
                    "properties": {
                        property_name: {
                            "multi_select": new_keywords
                        }
                    }
                }
                notion.pages.update(page_id=subpage_block_id, **update_payload)
                keywords = ["Default"]
            else:
                keywords = [keyword["name"] for keyword in
                            subpage_block_title["properties"]["Keywords"]["multi_select"]]

            # 创建时间
            create_time = subpage_block_title["properties"]["Created time"]["created_time"]
            create_time = time_format(str(create_time))
            is_public = subpage_block_title["properties"]["isPublic"]['select']['name']
            # 获取正文
            context_markdown = retrieve_and_convert_to_markdown(subpage_block_id, notion)
            page = NotionPage(
                title=title,
                content=context_markdown,
                tags=tags,
                keywords=keywords,
                create_time=create_time,
                is_public=is_public
            )
            result_map[subpage_block_id] = page
            print(f"成功获取名称为:{title}")

        return result_map


"""
---
slug: chuanxi-travel
title: 川西旅行日记
date: 2023-09-30
authors: wenhao
tags: [Life, Travel]
keywords: [旅行]
---
"""


def build_blog_content(result_map):
    for pageId, notionPage in result_map.items():
        title = notionPage.title
        # 空格其他符号去除
        title = re.sub(r'[^\w\s]', '', title).replace(' ', '')
        slug = translate_to_english(title)
        # 保留-符号, 其他符号都去除
        slug = re.sub(r'[^a-zA-Z0-9-]', '-', slug)

        date = notionPage.create_time
        authors = "wenhao"
        tags = notionPage.tags
        keywords = notionPage.keywords
        content = notionPage.content

        # content_result = f"---\nslug: {slug}\ntitle: {title}\ndate: {date}\nauthors: {authors}\ntags: {', '.join(tags)}\nkeywords: {', '.join(keywords)}\n---"
        content_result = f"---\nslug: {slug}\ntitle: {title}\ndate: {date}\nauthors: {authors}\ntags: {str(tags)}\nkeywords: {str(keywords)}\n---"
        content_result += f"\n{content}"

        shengming = "\n\n > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。\n"
        content_result += f"\n{shengming}"

        file_name = f"{global_dir}{title}.md"
        # 检查文件是否存在
        if os.path.exists(file_name):
            os.remove(file_name)

        try:
            with open(file_name, "w", encoding="utf-8") as f:
                f.write(content_result)
            print(f'写入成功:{file_name}')

            # 写入成功的pageId,更新ispublic的状态
            is_public = notionPage.is_public
            if is_public == "false":
                property_name = "isPublic"
                update_payload = {
                    "properties": {
                        property_name: {
                            "select": {
                                "name": "true"
                            }
                        }
                    }
                }
                global_notion.pages.update(page_id=pageId, **update_payload)
                print(f'发布状态更新成功:{title}  pageID为: {pageId}')
        except Exception as e:
            print(f'写入失败:{file_name},{e.message}')
            pass


def translate_to_english(text):
    translator = Translator(from_lang="zh", to_lang="en")
    translation = translator.translate(text)
    result = translation.replace(" ", "-")
    return result


"""
1. 获取文章内容-拼接正文
    1.1 发布条件过滤
2. 优化正文
    2.1 按照Markdown中Blog标签语法格式组装正文
3. 本地生成
    3.1 本地生成后,就设置pageId为发布成功true
4. GitHub提交
"""
"""
1. 根据Notion-api获取正文数据
2. 自动生成Markdown语法博文文件
3. 自动GitHub提交发布

补充：2025-05-22
    1. 通过notion- API获取Notion的书签数据库Bookmark-data中的文章数据， 根据isPublic属性过滤
    2. 获取notion中的文章数据，生成Markdown语法博文文件，存储到blog-Docusaurus2/blog/notion/目录下
    3. 将blog目录下的文章提交到GitHub， 就完成了发布， blog-Docusaurus2/blog/notion/目录下的文章就自动发布到了博客上展示内容；
"""
if __name__ == '__main__':
    data = Notion_Data()
    data.check_ispublic()
    result_map = data.get_block_content()
    build_blog_content(result_map)
    push_git(global_dir)
