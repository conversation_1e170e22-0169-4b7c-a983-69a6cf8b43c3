import re
from translate import Translator


def translate_to_english(text):
    translator = Translator(from_lang="zh", to_lang="en")
    translation = translator.translate(text)
    result = translation.replace(" ", "-")
    return result


# slug = translate_to_english("马斯克说的 50 种认知偏差，你中了几条？")
slug = "马斯克说的 50 种认知偏差，你中了几条？"
# slug = "my-long-slug-with-some-specially-encoded-characters"

# 保留-符号, 其他符号都去除
slug = re.sub(r'[^a-zA-Z0-9-]', '-', slug)

# 保留-符号, 其他符号都去除
# slug = re.sub(r'[^a-zA-Z0-9-]', '-', slug)

import re

text = '马斯克说的 50 种认知偏差，你中了几条？'
processed_text = re.sub(r'[^\w\s]', '', text).replace(' ', '')

print(processed_text)


print(slug)
