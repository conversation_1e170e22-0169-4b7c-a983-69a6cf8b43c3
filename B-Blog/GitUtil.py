import os
import subprocess
import time


def run_command(command, timeout=300):
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True, text=True)
    try:
        stdout, stderr = process.communicate(timeout=timeout)
        print(stdout)
        if process.returncode != 0:
            print(f"Error: {stderr}")
            return False
    except subprocess.TimeoutExpired:
        process.kill()
        print("Command timed out")
        return False
    return True


def push_git(global_dir):
    # global_dir = "您的目录路径"  # 替换为您的目录路径
    # global_dir = "/Users/<USER>/fuwenhao/Github/blog-Docusaurus2/blog/notion/"

    if not os.path.exists(global_dir):
        os.mkdir(global_dir)

    # 进入文件夹目录
    os.chdir(global_dir)

    # 运行Git命令并获取输出
    if run_command("git pull") and run_command("git add .") and run_command(
            "git commit -m '自动化发布'") and run_command("git push"):
        print("代码推送成功")
    else:
        print("代码推送失败")


if __name__ == '__main__':
    global_dir = "/Users/<USER>/fuwenhao/Github/blog-Docusaurus2/blog/notion/"
    push_git(global_dir)
