def insert_string_at_position(original_content, position, string_to_insert):
    # 检查指定的位置是否在字符串的长度范围内
    if position <= len(original_content):
        # 在指定位置插入字符串
        return original_content[:position] + string_to_insert + original_content[position:]
    else:
        print("指定的位置超出了字符串的长度。")
        return original_content


if __name__ == '__main__':
    # 示例使用
    original_content = "这里是您的原始内容，长度超过100个字符..."
    position = 10  # 指定在第100个字符处插入
    string_to_insert = "张三"

    # 执行插入操作
    modified_content = insert_string_at_position(original_content, position, string_to_insert)

    print(modified_content)
