import schedule
import time
import subprocess
from github_tending import main
def job():
    print("I'm working...")
    # command="sh /Users/<USER>/fuwenhao/Github/weiboSpider/weibo_spider/test.sh"
    # command="sh /Users/<USER>/fuwenhao/Github/weiboSpider/fwh_start.sh"
    # subprocess.call(command, shell=True)
    main()


def schedule_job():
    schedule.every().day.at("08:00").do(job)
    # schedule.every().day.at("10:42").do(job)
    # schedule.every(1).seconds.do(job)
    # schedule.every(10).minutes.do(job)
    # schedule.every(1).hours.do(job)

    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    print('[程序启动...]')
    # 调用定时任务函数
    schedule_job()