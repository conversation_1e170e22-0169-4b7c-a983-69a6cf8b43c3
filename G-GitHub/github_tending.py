import requests
from bs4 import BeautifulSoup

def get_github_tending():
    # 目标网页的URL
    url = 'https://github.com/trending?since=daily'
    # url = 'https://github.com/trending?since=weekly' # 本周热门
    # url = 'https://github.com/trending?since=monthly' # 本月热门
    # url = 'https://github.com/trending/java?since=monthly' # 本月热门-java
    # url = 'https://github.com/trending/java?since=monthly&spoken_language_code=zh' # 本月热门-java-中文

    # 发送GET请求
    response = requests.get(url)
    page_list=[]
    # 检查响应状态
    if response.status_code == 200:
        
        # 解析HTML内容
        soup = BeautifulSoup(response.content, 'html.parser')

        # 提取和打印信息（根据页面结构调整选择器）
        for repo in soup.find_all('article', class_='Box-row'):
            # 查找包含标题的元素
            title_element = repo.find('h2', class_='h3 lh-condensed')
            description_element = repo.find('p', class_='col-9 color-fg-muted my-1 pr-4')
            title = title_element.get_text(strip=True) if title_element else "No title"
            title_array=str(title).split('/')
            author=title_array[0]
            title=title_array[1]
            description = description_element.get_text(strip=True) if description_element else "No description"
            print(f"Repository: {title}")
            description_zh=translate_to_chinese(description)
            # 查找包含star数的元素
            star_element = repo.find('a', href=lambda href: href and href.endswith('/stargazers'))
            stars = star_element.get_text(strip=True) if star_element else "Stars not found"
            stars=stars.replace(',', '')
            # 查找包含fork数的元素
            fork_element = repo.find('a', href=lambda href: href and href.endswith('/forks'))
            forks = fork_element.get_text(strip=True) if fork_element else "Forks not found"
            forks=forks.replace(',', '')

            # 查找包含开发语言的元素
            language_element = repo.find('span', itemprop='programmingLanguage')
            language = language_element.get_text(strip=True) if language_element else "Language not found"
            # 查找今日新增star数的元素
            # 注意：GitHub Trending 页面可能不直接显示今日新增star数，这里假设它位于特定的标签中
            today_stars_element = repo.find('span', class_='d-inline-block float-sm-right')
            today_stars = today_stars_element.get_text(strip=True) if today_stars_element else "Today's stars not found"
            today_stars = int(''.join(filter(str.isdigit, today_stars)))
            # 构建仓库的URL
            repo_url = "https://github.com" + title_element.find('a')['href'] if title_element and title_element.find('a') else "URL not found"

            api_repo_url=repo_url.replace('https://github.com','https://api.github.com/repos')
            result=request_api(api_repo_url)
            if result is None:
                continue
            created_at=result['created_at']
            updated_at=result['updated_at']
            id=result['id']
            node_id=result['node_id']
            homepage=result['homepage']
        

            page={
                "title":title,
                "description":description,
                "description_zh":description_zh,
                "star":stars,
                "fork":forks,
                "language":language,
                "today_stars":today_stars,
                "repo_url":repo_url,
                "author":author,
                "created_at":created_at,
                "updated_at":updated_at,
                "id":id,
                "node_id":node_id,
                "homepage":homepage
            }
            page_list.append(page)
    else:
        print("请求失败，状态码：", response.status_code)
    return page_list

class Page:
    def __init__(self, description,description_zh,start_count,fork_count,watch_count,author,create_time,update_time,clone_url,home_url,language,title,node_id,id):
        self.description = description
        self.description_zh = description_zh
        self.start_count = start_count
        self.fork_count = fork_count
        self.watch_count = watch_count
        self.author = author
        self.create_time = create_time
        self.update_time = update_time
        self.clone_url = clone_url
        self.home_url = home_url
        self.language = language
        self.title = title
        self.node_id = node_id
        self.id=id

from translate import Translator
def translate_to_chinese(text):
    translator = Translator(from_lang="en", to_lang="zh")
    translation = translator.translate(text)
    result = translation.replace(" ", "-")
    return result   

    
from notion_client import Client
from transaction_util import translate_text
class NotionClinet:
    def __init__(self):
        """
        初始化
        """
        global global_query_results
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        global_database_id = "770b5bd857a0448f946cf6bf041fd745"
        global_notion = Client(auth=global_token)
        global_query_results = global_notion.databases.query(database_id=global_database_id)
        print('开始Notion自动化处理数据...')
    
    """
    创建新的页面
    1. 属性名字和字段个数要对应上
    2. 不同的属性用不同的构参方式
    """
    def create_page(self, page):
        new_page = global_notion.pages.create(
            parent={
                'database_id': global_database_id
            },
            properties={
                'Name': {
                    'title': [
                        {
                            'text': {
                                'content': str(page.title)
                            }
                        }
                    ]
                },
                '正文内容': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page.description)
                            }
                        }
                    ]
                },
                '正文内容-中文': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page.description_zh)
                            }
                        }
                    ]
                },
                "Start": {
                    "number": int(page.start_count)
                },
                "Fork": {
                    "number": int(page.fork_count)
                },
                "Watch": {
                    "number": int(page.watch_count)
                },
                '作者': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page.author)
                            }
                        }
                    ]
                },
                "创建时间": {
                    "date": {
                        "start": str(page.create_time),
                    }
                },
                "更新时间": {
                    "date": {
                        "start": str(page.update_time),
                    }
                },
                "Clone_URL": {
                    'url': str(page.clone_url)
                },
                "主页地址": {
                    'url': str(page.home_url)
                },
                '开发语言': {
                    'select':{
                        'name': str(page.language)
                    }
                },
                'Node_id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page.node_id)
                            }
                        }
                    ]
                },
                'id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page.id)
                            }
                        }
                    ]
                }
            }
        )
        print(new_page)

from urllib.request import urlopen
from urllib.request import Request
import json

def request_api(api_url):
    req = requests.get(api_url)
    if 200==req.status_code:
        response=json.loads(req.content)
        return response
    else:
        return None



def save_notion(pages):
    client=NotionClinet()
    for page_objcect in pages:
        if page_objcect.get('homepage', '')=="":
            page_objcect['homepage']=page_objcect['repo_url']
        page=Page(
            description=page_objcect['description'],
            description_zh=page_objcect['description_zh'],
            start_count=page_objcect['star'],
            fork_count=page_objcect['fork'],
            watch_count=page_objcect['today_stars'],
            author=page_objcect['author'],
            create_time=page_objcect['created_at'],
            update_time=page_objcect['updated_at'],
            clone_url=page_objcect['repo_url'],
            home_url=page_objcect['homepage'],
            language=page_objcect['language'],
            title=page_objcect['title'],
            node_id=page_objcect['node_id'],
            id=page_objcect['id']
        )
        client.create_page(page)

def main():
    pages=get_github_tending()
    save_notion(pages)
    
        

if __name__ == "__main__":
    main()