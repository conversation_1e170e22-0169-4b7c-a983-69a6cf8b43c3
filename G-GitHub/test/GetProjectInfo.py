"""
1. 获取所有GitHub仓库中java的项目
2. 将项目中星星数量大于2000的筛选保存到Excel文件中
"""
import requests
import json
import pandas as pd

# 设置GitHub API的URL
url = "https://api.github.com/search/repositories?q=language:java&sort=stars&order=desc"

# 创建一个空的DataFrame
df = pd.DataFrame(columns=['项目名', '星星数', '项目链接'])

while True:
    # 发送GET请求
    response = requests.get(url)
    data = response.json()

    # 解析返回的数据
    for item in data['items']:
        if item['stargazers_count'] > 2000:
            # 将项目信息添加到DataFrame中
            df = pd.concat([df, pd.DataFrame(
                {'项目名': [item['name']], '星星数': [item['stargazers_count']], '项目链接': [item['html_url']]})],
                           ignore_index=True)
            # df = df.append({'项目名': item['name'], '星星数': item['stargazers_count'], '项目链接': item['html_url']}, ignore_index=True)

    if 'link' in response.headers:
        pages = {rel[6:-1]: url.strip('<>') for url, rel in
                 (link.split(';') for link in
                  response.headers['link'].split(','))}
        if 'next' in pages:
            url = pages['next']
        else:
            break
    else:
        break

# 将数据保存到Excel文件中
df.to_excel('github_java_projects.xlsx', index=False)
