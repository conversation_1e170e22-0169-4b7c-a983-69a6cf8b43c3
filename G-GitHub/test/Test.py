# from translate import Translator
# def translate_to_english(text):
#     # translator = Translator(from_lang="zh", to_lang="en")
#     translator = Translator(from_lang="en", to_lang="zh")
#     translation = translator.translate(text)
#     result = translation.replace(" ", "-")
#     return result

# # text_to_translate = "Hello, world!"
# text_to_translate = "Mixin and template tags to integrate django-fsm transitions into the django admin."
# # text_to_translate = "python+Django数据库监控平台  Linux/Oracle/MySQL/Redis基础监控+性能监控"
# print(translate_to_english(text_to_translate))



import requests

# def translate_with_matecat(text, source_lang, target_lang):
#     url = "https://api.matecat.com/api/v1/translate"

#     headers = {
#         "content-type": "application/json",
#     }

#     data = {
#         "text": text,
#         "source_lang": source_lang,
#         "target_lang": target_lang
#     }

#     response = requests.post(url, headers=headers, json=data)

#     if response.status_code == 200:
#         return response.json()['data']['translation']
#     else:
#         return None

# translate_with_matecat("Hello, world!", "en", "zh")



def get_request(url):
    response = requests.get(url)
    if response.status_code == 200:
        return response.json()
    else:
        return None

# 获取date提交时间
def get_date(response):
    date = response[0]['commit']['committer']['date']
    return date

# 时间格式化
def format_date(date):
    date = date.replace('T', ' ')
    date = date.replace('Z', '')
    return date

if __name__ == "__main__":
    url = "https://api.github.com/repos/datasciencemasters/go/commits"
    response = get_request(url)
    date=get_date(response)
    date=format_date(date)
    print(date)