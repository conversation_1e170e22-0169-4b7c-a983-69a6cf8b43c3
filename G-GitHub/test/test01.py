import requests
from bs4 import BeautifulSoup

# 目标网页的URL
url = 'https://github.com/trending?since=daily'
# url = 'https://github.com/trending?since=weekly' # 本周热门
# url = 'https://github.com/trending?since=monthly' # 本月热门
# url = 'https://github.com/trending/java?since=monthly' # 本月热门-java
# url = 'https://github.com/trending/java?since=monthly&spoken_language_code=zh' # 本月热门-java-中文

# 发送GET请求
response = requests.get(url)

# 检查响应状态
if response.status_code == 200:
    # 解析HTML内容
    soup = BeautifulSoup(response.content, 'html.parser')

    # 提取和打印信息（根据页面结构调整选择器）
    for repo in soup.find_all('article', class_='Box-row'):
        # 查找包含标题的元素
        title_element = repo.find('h2', class_='h3 lh-condensed')
        description_element = repo.find('p', class_='col-9 color-fg-muted my-1 pr-4')
        title = title_element.get_text(strip=True) if title_element else "No title"
        description = description_element.get_text(strip=True) if description_element else "No description"
        print(f"Repository: {title}\nDescription: {description}\n")
         # 查找包含star数的元素
        star_element = repo.find('a', href=lambda href: href and href.endswith('/stargazers'))
        stars = star_element.get_text(strip=True) if star_element else "Stars not found"
        # 查找包含fork数的元素
        fork_element = repo.find('a', href=lambda href: href and href.endswith('/forks'))
        forks = fork_element.get_text(strip=True) if fork_element else "Forks not found"
        print(f"Stars: {stars}\nForks: {forks}\n")
          # 查找包含开发语言的元素
        language_element = repo.find('span', itemprop='programmingLanguage')
        language = language_element.get_text(strip=True) if language_element else "Language not found"
        # 查找今日新增star数的元素
        # 注意：GitHub Trending 页面可能不直接显示今日新增star数，这里假设它位于特定的标签中
        today_stars_element = repo.find('span', class_='d-inline-block float-sm-right')
        today_stars = today_stars_element.get_text(strip=True) if today_stars_element else "Today's stars not found"
        print(f"Language: {language}\nToday's stars: {today_stars}\n")
        # 构建仓库的URL
        repo_url = "https://github.com" + title_element.find('a')['href'] if title_element and title_element.find('a') else "URL not found"
        print(f"URL: {repo_url}\n")
else:
    print("请求失败，状态码：", response.status_code)
