import requests

def translate_text(text, target_language):
    # 替换为您自己的Google Cloud API密钥
    api_key = "AIzaSyDZWvUHN80JIMOb3qDKmeEpiS_FcUeqBVE"  #YouTube-API
    base_url = "https://translation.googleapis.com/language/translate/v2"
    
    params = {
        "key": api_key,
        "q": text,
        "target": target_language
    }
    
    response = requests.post(base_url, params=params)
    
    if response.status_code == 200:
        translation = response.json()["data"]["translations"][0]["translatedText"]
        return translation
    else:
        print("翻译失败")
        print(response.text)
        return None

# 示例用法
text_to_translate = "Hello, world!"
# text_to_translate = "Production-Grade Container Scheduling and Management"
# # target_language = "fr"  # 目标语言，例如法语
target_language = "zh"  # 目标语言，例如法语

# translated_text = translate_text(text_to_translate, target_language)
translated_text = translate_text(text_to_translate, "zh")
if translated_text:
    print(f"原文: {text_to_translate}")
    print(f"翻译结果: {translated_text}")
else:
    print("翻译失败")
