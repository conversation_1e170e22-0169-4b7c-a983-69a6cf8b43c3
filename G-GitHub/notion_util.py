from notion_client import Client
from transaction_util import translate_text

class Page:
    def __init__(self, description,start_count,fork_count,watch_count,author,create_time,update_time,clone_url,home_url,language,title,node_id):
        self.description = description
        self.start_count = start_count
        self.fork_count = fork_count
        self.watch_count = watch_count
        self.author = author
        self.create_time = create_time
        self.update_time = update_time
        self.clone_url = clone_url
        self.home_url = home_url
        self.language = language
        self.title = title
        self.node_id = node_id
    
    def __str__(self):
        return f"page_id:{self.page_id},description:{self.description},start_count:{self.start_count},fork_count:{self.fork_count},watch_count:{self.watch_count},author:{self.author},create_time:{self.create_time},update_time:{self.update_time},clone_url:{self.clone_url},home_url:{self.home_url},language:{self.language},title:{self.title},node_id:{self.node_id}"

from translate import Translator
def translate_to_english(text):
    # translator = Translator(from_lang="zh", to_lang="en")
    translator = Translator(from_lang="en", to_lang="zh")
    translation = translator.translate(text)
    result = translation.replace(" ", "-")
    return result   


class NotionClinet:
    def __init__(self):
        """
        初始化
        """
        global global_query_results
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        global_database_id = "fe02fa55ad604d93b11b6de367e11dec"
        global_notion = Client(auth=global_token)
        global_query_results = global_notion.databases.query(database_id=global_database_id)
        print('开始Notion自动化处理数据...')
    
    """
    创建新的页面
    1. 属性名字和字段个数要对应上
    2. 不同的属性用不同的构参方式
    """
    def create_page(self, page):
        new_page = global_notion.pages.create(
            parent={
                'database_id': global_database_id
            },
            properties={
                'Name': {
                    'title': [
                        {
                            'text': {
                                'content': "标题"
                            }
                        }
                    ]
                },
                '正文内容': {
                    'rich_text': [
                        {
                            'text': {
                                'content': '描述内容'
                            }
                        }
                    ]
                },
                "Start": {
                    "number": 123
                },
                "Fork": {
                    "number": 123
                },
                "Watch": {
                    "number": 123
                },
                '作者': {
                    'rich_text': [
                        {
                            'text': {
                                'content': '作者'
                            }
                        }
                    ]
                },
                "创建时间": {
                    "date": {
                        "start": "2022-01-01",
                    }
                },
                "更新时间": {
                    "date": {
                        "start": "2022-01-01",
                    }
                },
                "Clone_URL": {
                    'url': 'www.baidu.com'
                },
                "主页地址": {
                    'url': 'www.baidu.com'
                },
                '开发语言': {
                    'select':{
                        'name': 'go'
                    }
                },
                'Node_id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': 'Node_id'
                            }
                        }
                    ]
                }
            }
        )
        print(new_page)

    """
    1. 将页面的属性转换成中文
    """
    def get_page_properties_chinese(self,results):
        properties={}
        if len(results)==0:
            results=global_query_results["results"]
        # 获取正文属性
        print(f'更新长度：{results.__len__()}')
        for page in results:
            page_id = page["id"]
            page_content = page["properties"]["正文内容"]["rich_text"][0]["text"]["content"]
            if page["properties"]["正文内容-中文"].get("rich_text") is not None:
                if len(page["properties"]["正文内容-中文"]["rich_text"])>0:
                    chinese_content=page["properties"]["正文内容-中文"]["rich_text"][0]["text"]["content"]
                    if "MYMEMORY-WARNING" not in chinese_content:
                        continue
            try:
                print(page_content)
                # chinese_content=translate_to_english(page_content)
                # if "MYMEMORY-WARNING" in chinese_content:
                #     print(f'翻译失败，原文为：{page_content}  翻译上限：{chinese_content}')
                #     return
                chinese_content=translate_text(page_content,"zh")
                if chinese_content is None:
                    continue
                print(chinese_content)
                
                properties[page_id]=chinese_content
                # self.update_notion_properties_by_rich_text(page_id, "正文内容-中文", chinese_content)
            except Exception as e:
                print(f'翻译失败，原文为：{page_content}  异常：{e}')
                pass 
        if len(properties)>0:
            for page_id,chinese_content in properties.items():
                print(f'更新页面：{page_id}  内容：{chinese_content}')
                try:
                    self.update_notion_properties_by_rich_text(page_id, "正文内容-中文", chinese_content)
                except Exception as e:
                    print(f'更新Notion异常,原文为:{chinese_content}  异常：{e}')
                    pass

    """
    富文本形式更新
    """
    def update_notion_properties_by_rich_text(self,page_id, property_name, content):
        update_payload = {
            "properties": {
                property_name: {
                    "rich_text": [
                        {
                            "text": {
                                "content": str(content)
                            }
                        }
                    ]
                }
            }
        }
        # 对Notion的富文本更新
        update_page=global_notion.pages.update(page_id=page_id, **update_payload)
        print(update_page)

    """
    获取所有页面
    """
    def get_all_pages(self,database_id):
        results = []
        start_cursor = None

        while True:
            response = global_notion.databases.query(
                database_id=database_id,
                start_cursor=start_cursor,
                page_size=100,  # Maximum page size
            )
            results.extend(response['results'])
            
            print(f'获取页面数量：{results.__len__()}')

            # # temp 处理重复数据
            self.delete_duplicate_page(results,"Node_id")

            # 更新中文字段
            # self.get_page_properties_chinese(results)

            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results
    """
    删除重复的页面-保留最新的页面
    """
    def delete_duplicate_page(self,page_list,property_name):
        property_name_set=set()
        for page in page_list:
            if page["object"] == "page":
                for key, value in page["properties"].items():
                    if key == property_name:
                        # 获取富文本类型的值
                        text_value=value['rich_text'][0]['text']['content']
                        if text_value in property_name_set:
                            print(f'删除页面：{text_value}')
                            self.delete_page_content(page["id"])
                        else:
                            property_name_set.add(text_value)
    """
    删除页面内容
    """
    def delete_page_content(self, page_id):
        del_block = global_notion.blocks.delete(block_id=page_id)
        print(del_block)


def main():
    client = NotionClinet()
    client.get_all_pages(global_database_id)     


"""
1. 清理重复数据
2. 描述翻译成中文,用Google翻译
"""
def test():
    client = NotionClinet()
    # client.get_page_properties_chinese()
    client.get_all_pages(global_database_id)     

    # page="test"
    # client.create_page(page)

    # client.get_page_properties_chinese()
        


"""
API官网地址:https://developers.notion.com/reference/patch-page
1. 链接
2. 增加
3. 删除
4. 修改
5. 查询
"""
if __name__ == '__main__':
    test()
