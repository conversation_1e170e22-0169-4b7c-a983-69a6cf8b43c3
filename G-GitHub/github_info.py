import time
from urllib.request import urlopen
from urllib.request import Request
import json
from notion_client import Client
import os

class Page:
    def __init__(self, description,start_count,fork_count,watch_count,author,create_time,update_time,clone_url,home_url,language,title,node_id,id):
        self.description = description
        self.start_count = start_count
        self.fork_count = fork_count
        self.watch_count = watch_count
        self.author = author
        self.create_time = create_time
        self.update_time = update_time
        self.clone_url = clone_url
        self.home_url = home_url
        self.language = language
        self.title = title
        self.node_id = node_id
        self.id = id
    
    def __str__(self):
        return f"page_id:{self.page_id},description:{self.description},start_count:{self.start_count},fork_count:{self.fork_count},watch_count:{self.watch_count},author:{self.author},create_time:{self.create_time},update_time:{self.update_time},clone_url:{self.clone_url},home_url:{self.home_url},language:{self.language},title:{self.title},node_id:{self.node_id}"

    
class NotionClinet:
    def __init__(self):
        """
        初始化
        """
        global global_query_results
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        global_database_id = "fe02fa55ad604d93b11b6de367e11dec"
        global_notion = Client(auth=global_token)
        global_query_results = global_notion.databases.query(database_id=global_database_id)
        print('开始Notion自动化处理数据...')
    
    """
    创建新的页面
    1. 属性名字和字段个数要对应上
    2. 不同的属性用不同的构参方式
    """
    def create_page(self, page):
        try:
            new_page = global_notion.pages.create(
                parent={
                    'database_id': global_database_id
                },
                properties={
                    'Name': {
                        'title': [
                            {
                                'text': {
                                    'content': page.title if page.title else 'Unknown'
                                }
                            }
                        ]
                    },
                    '正文内容': {
                        'rich_text': [
                            {
                                'text': {
                                    'content': page.description
                                }
                            }
                        ]
                    },
                    "Start": {
                        "number": page.start_count
                    },
                    "Fork": {
                        "number": page.fork_count
                    },
                    "Watch": {
                        "number": page.watch_count
                    },
                    '作者': {
                        'rich_text': [
                            {
                                'text': {
                                    'content': page.author
                                }
                            }
                        ]
                    },
                    "创建时间": {
                        "date": {
                            "start": page.create_time
                        }
                    },
                    "更新时间": {
                        "date": {
                            "start": page.update_time
                        }
                    },
                    "Clone_URL": {
                        'url': page.clone_url
                    },
                    "主页地址": {
                        'url': page.home_url if page.home_url else 'Unknown'
                    },
                    '开发语言': {
                        'select':{
                            'name': page.language if page.language else str(search)
                        }
                    },
                    'Node_id': {
                        'rich_text': [
                            {
                                'text': {
                                    'content': page.node_id
                                }
                            }
                        ]
                    },
                    'id': {
                        'rich_text': [
                            {
                                'text': {
                                    'content': page.id
                                }
                            }
                        ]
                    }
                }
            )
            print(new_page)
            return new_page
        except Exception as e:
            print(f'插入Notion异常信息:{e}')
            pass
        return None




"""
api接口
"""
def get_results(search, headers, page, stars):
    try:
        url = 'https://api.github.com/search/repositories?q={search}%20stars:<={stars}&page={num}&per_page=100&sort=stars' \
              '&order=desc'.format(search=search, num=page, stars=stars)

        # url = 'https://api.github.com/search/repositories?q={search}%20stars:<={stars}&page={num}&per_page=100' \
        #     .format(search=search, num=page, stars=stars)
        req = Request(url, headers=headers)
        response = urlopen(req).read()
        result = json.loads(response.decode())
        return result
    except Exception as e:
        print(f'异常信息:{e}')
        pass
    return None



def main():
    client=NotionClinet()
    global search
    # search = 'language:javascript'
    # search = 'language:java'
    # search = 'language:python'
    search = 'openai'
    # search = 'go'
    # search = 'Java'
    # search = 'Typescript'
    # search = 'next.js'
    # search = 'awesome'
    stars = 421701
    headers = {'User-Agent': 'Mozilla/5.0',
               'Authorization': 'token ****************************************',
               'Content-Type': 'application/json',
               'Accept': 'application/json'
               }
    print('开始爬取github数据...')
    count = 1
    for i in range(0, 2):
        repos_list = []
        stars_list = []
        # for page in range(1, 11):
        for page in range(1, 5):
            results = get_results(search, headers, page, stars)
            if results is None:
                continue
            for item in results['items']:
                # 获取最后更新时间
                commits_url=item["commits_url"]
                new_commits_url = commits_url.replace("{/sha}", "")
                response = get_request(new_commits_url)
                if response is None:
                    last_commit_data="2021-01-01 00:00:00"
                else:
                    date=get_date(response)
                    if date is None:
                        last_commit_data="2021-01-01 00:00:00"
                    else:
                        last_commit_data=format_date(date)

                page =Page(
                    description=item["description"],
                    start_count=item["watchers_count"],
                    fork_count=item["forks_count"],
                    watch_count=0,
                    author=item["owner"]["login"],
                    create_time=item["created_at"],
                    update_time=last_commit_data,
                    clone_url=item["clone_url"],
                    home_url=item["homepage"],
                    language=item["language"],
                    title=item["name"],
                    node_id=item["node_id"],
                    id=item["id"]
                )
                repos_list.append(page)
                stars_list.append(item["stargazers_count"])
                count += 1
            print(f'get projects count: {len(repos_list)}')
        stars = stars_list[-1]
        print(f'start count:{stars}')
        
        # 打开已经保存的文件
        message_ids = {}
        file_name = f'{search}-noiton.json'
        file_path = os.path.join(os.path.dirname(__file__), file_name)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        json_data = json.loads(line)
                        node_id = json_data['node_id']
                        page_id = json_data['page_id']
                        message_ids[node_id] = page_id
            except Exception as e:
                print(f'更新异常:{e}')
                pass
        # 保存Notion
        insert_notion_list = []
        for i in range(len(repos_list)):
            node_id=repos_list[i].node_id
            # todo-fwh-如果id存在就不更新
            if node_id in message_ids:
                print(f'node_id:{node_id} 已存在')
                continue
            # 插入Notion数据
            new_page=client.create_page(repos_list[i])
            if new_page is None:
                continue
            page_id = new_page['id']
            node_id = new_page['properties']['Node_id']['rich_text'][0]['plain_text']
            insert_notion_data = {'page_id': page_id, 'node_id': node_id}
            insert_notion_list.append(insert_notion_data)

        # 插入notion的数据保存文件中
        with open(file_path, 'a') as f:
            for i in insert_notion_list:
                json.dump(i, f)
                f.write('\n')
            f.close()
            print(f'[notion.json保存:{len(insert_notion_list)}条数据完成]')
        time.sleep(60)
    print("Done")

# 定义Get请求
import requests
def get_request(url):
    time.sleep(1)
    response = requests.get(url)
    if response.status_code == 200:
        return response.json()
    else:
        return None

# 获取date提交时间
def get_date(response):
    try: 
        date = response[0]['commit']['committer']['date']
        return date
    except Exception as e:
        print(f'获取时间异常:{e}')
        print(f'异常信息:{response}')
        pass
    return None

# 时间格式化
def format_date(date):
    date = date.replace('T', ' ')
    date = date.replace('Z', '')
    return date


"""
目标:
1. 获取所有GitHub仓库中java的项目
2. 将项目中星星数量大于2000的筛选保存到Excel文件中

官网: https://docs.github.com/en/rest/search/search?apiVersion=2022-11-28#search-repositories
参考代码: https://blog.csdn.net/weixin_39132520/article/details/114925354

配置说明:
1. 修改GitHub token值
2. 修改搜索条件: search语言, stars小于的数量,page页数, 
3. api按照搜索条件, start排序
"""
if __name__ == '__main__':
    main()
