import requests
import json

def wtt_list(currentPage:int):
    # 全部动态数据
    # url=f'https://baijiahao.baidu.com/pcui/pcpublisher/listdynamic?currentPage={currentPage}&pageSize=10&search=&type=moment&collection='
    # 全部已发布数据:
    url=f'https://baijiahao.baidu.com/pcui/article/lists?currentPage={currentPage}&search=&pageSize=10&type=&collection=publish&dynamic=1'
    cookie_value = 'gray=1; canary=0; theme=bjh; H_WISE_SIDS=39996_40020_40125; H_WISE_SIDS_BFESS=39996_40020_40125; BDUSS=jZSaE9LZDNIWDFQcn5hNnRDd3NjWUNVYnYtNlg5MktES2JGdm5FeVRGMXhUUkZtRUFBQUFBJCQAAAAAAAAAAAEAAABqz8IRZnV3ZW5oYW81NAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHHA6WVxwOllN; BDUSS_BFESS=jZSaE9LZDNIWDFQcn5hNnRDd3NjWUNVYnYtNlg5MktES2JGdm5FeVRGMXhUUkZtRUFBQUFBJCQAAAAAAAAAAAEAAABqz8IRZnV3ZW5oYW81NAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHHA6WVxwOllN; BAIDUID=6E3F995D96E91B31CE714B5CC8B3E136:FG=1; newlogin=1; openTabIndex=tab_hot_quest; BIDUPSID=6E3F995D96E91B31CE714B5CC8B3E136; PSTM=1714381996; H_PS_PSSID=40298_40445_40080_60141_40463; BAIDUID_BFESS=6E3F995D96E91B31CE714B5CC8B3E136:FG=1; ZFY=2P:BDcYJsoaS:ALpzq:BsIldNxihpcHEGCe0Di7K6S7S1M:C; __bid_n=18dca6cbd5868644b98db5; MCITY=-149%3A; BA_HECTOR=a1a08ga52k81000001aha525ft23jt1j344lg1s; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; Hm_lvt_f7b8c775c6c8b6a716a75df506fb72df=1713682888,1713789099,1714303615,1714557618; PHPSESSID=3bhjttdud6a16bh4eolnjj7t34; Hm_lpvt_f7b8c775c6c8b6a716a75df506fb72df=1714558464; devStoken=d0920f8bf650a8e7a794e02fee1acac55d2c94a1eed6a946a6a46f491e014f1c; bjhStoken=5d6822e02e27b96d965a6e153cd934025d2c94a1eed6a946a6a46f491e014f1c; ab_sr=1.0.1_MThiMjE3N2JiMTkwZmJkZWMyYTc2NDc0NmYzZjYwNjk1ZDU5ZmQwNjk5N2IyODhiNDU5OGE2NzkyN2E1YmE5MmFmMTJlZTExMmUyOGJhM2MyNGQ4OWJkZGIzYWY1ZTJlMTcwMGNjNDAwYzM2OGIzM2VkMjQ0NjQzNTRlOTM5MmYyM2NkNDEwNzY5Y2E5MTJkZTBhZTBiZGY0YmIxMDAxMjY1MjNlNmE4MGM5OWZhZjkxNjcyYTc4OWQxNGQwMzY4; RT="z=1&dm=baidu.com&si=7a77f68b-dddb-4b30-908b-3cd4e81a19ee&ss=lvnnc4hz&sl=d&tt=q9m&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=lcfq&nu=4744o7tm&cl=ite2"'

    headers = {
        'Cookie': cookie_value,
        'Token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9iYWlqaWFoYW8uYmFpZHUuY29tIiwiYXVkIjoiaHR0cDpcL1wvYmFpamlhaGFvLmJhaWR1LmNvbSIsImlhdCI6MTcxNTA0Mjc0MiwibmJmIjoxNzE0OTk5NTQ3LCJleHAiOjE3MTUwODU5NDd9.AQc9QfeIkqmdk5NNHJY7W1eCwWlDB1rcJi5YWlnG6Cs'
    }

    response = requests.get(url, headers=headers)

    parsed_data = json.loads(response.text)
    # print(parsed_data)
    if len(parsed_data["data"])==0:
        return
    for item in parsed_data["data"]['list']:
        feed_id = item["feed_id"]
        id = item["id"]
        article_del(id,feed_id)
        print(f'已删除{id}--{feed_id}--{item["title"]}')

# def wtt_del(gid:str):
#     # gid='1794161796601856'
#     url = f'https://baijiahao.baidu.com/pcui/pcpublisher/deletedynamic'
#     cookie_value = 'gray=1; canary=0; theme=bjh; H_WISE_SIDS=39996_40020_40125; H_WISE_SIDS_BFESS=39996_40020_40125; BDUSS=jZSaE9LZDNIWDFQcn5hNnRDd3NjWUNVYnYtNlg5MktES2JGdm5FeVRGMXhUUkZtRUFBQUFBJCQAAAAAAAAAAAEAAABqz8IRZnV3ZW5oYW81NAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHHA6WVxwOllN; BDUSS_BFESS=jZSaE9LZDNIWDFQcn5hNnRDd3NjWUNVYnYtNlg5MktES2JGdm5FeVRGMXhUUkZtRUFBQUFBJCQAAAAAAAAAAAEAAABqz8IRZnV3ZW5oYW81NAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHHA6WVxwOllN; BAIDUID=6E3F995D96E91B31CE714B5CC8B3E136:FG=1; newlogin=1; openTabIndex=tab_hot_quest; BIDUPSID=6E3F995D96E91B31CE714B5CC8B3E136; PSTM=1714381996; H_PS_PSSID=40298_40445_40080_60141_40463; BAIDUID_BFESS=6E3F995D96E91B31CE714B5CC8B3E136:FG=1; ZFY=2P:BDcYJsoaS:ALpzq:BsIldNxihpcHEGCe0Di7K6S7S1M:C; __bid_n=18dca6cbd5868644b98db5; MCITY=-149%3A; BA_HECTOR=a1a08ga52k81000001aha525ft23jt1j344lg1s; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; Hm_lvt_f7b8c775c6c8b6a716a75df506fb72df=1713682888,1713789099,1714303615,1714557618; PHPSESSID=3bhjttdud6a16bh4eolnjj7t34; Hm_lpvt_f7b8c775c6c8b6a716a75df506fb72df=1714569737; devStoken=d0920f8bf650a8e7a794e02fee1acac50bb04781cf372e302f0ad4103e614642; bjhStoken=5d6822e02e27b96d965a6e153cd934020bb04781cf372e302f0ad4103e614642; ab_sr=1.0.1_N2JlODVlM2RhYzZmZDc4ZGMxNjdmYWEzZDkyNDNmMTU5NzEyY2M3YjFjZjc1NDA0MmRjY2QxZjI3OTBjNTJmYzU2YjJkYWVkMTNmY2E1MzA1OTYyMjZlZjIxZjExYjE1NmUxMzhlYzA1NjE4MDZjYTI5ZTk4NjczYmU2YWNmMWMxZjMwMDJhNDVmNmEwOGVhYWUwMGE0ODI1M2I4NzE5MTdkYmY0YzBjNzk2OTE4NDA3MWZiMzdkODVlOGEwYWEy; RT="z=1&dm=baidu.com&si=7a77f68b-dddb-4b30-908b-3cd4e81a19ee&ss=lvnuf8vd&sl=1&tt=75g&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=4v68&nu=9y8m6cy&cl=4xyu"'
#     headers = {
#         'Cookie': cookie_value
#     }
#     # todo 增加body
#     response = requests.post(url, headers=headers)
#     parsed_data = json.loads(response.text)
#     print(parsed_data)

def article_del(id:str,feed_id:str):
    url='https://baijiahao.baidu.com/pcui/pcpublisher/deletedynamic'
    cookie_value = 'gray=1; canary=0; theme=bjh; H_WISE_SIDS=39996_40020_40125; H_WISE_SIDS_BFESS=39996_40020_40125; BDUSS=jZSaE9LZDNIWDFQcn5hNnRDd3NjWUNVYnYtNlg5MktES2JGdm5FeVRGMXhUUkZtRUFBQUFBJCQAAAAAAAAAAAEAAABqz8IRZnV3ZW5oYW81NAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHHA6WVxwOllN; BDUSS_BFESS=jZSaE9LZDNIWDFQcn5hNnRDd3NjWUNVYnYtNlg5MktES2JGdm5FeVRGMXhUUkZtRUFBQUFBJCQAAAAAAAAAAAEAAABqz8IRZnV3ZW5oYW81NAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHHA6WVxwOllN; BAIDUID=6E3F995D96E91B31CE714B5CC8B3E136:FG=1; newlogin=1; openTabIndex=tab_hot_quest; BIDUPSID=6E3F995D96E91B31CE714B5CC8B3E136; PSTM=1714381996; H_PS_PSSID=40298_40445_40080_60141_40463; BAIDUID_BFESS=6E3F995D96E91B31CE714B5CC8B3E136:FG=1; ZFY=2P:BDcYJsoaS:ALpzq:BsIldNxihpcHEGCe0Di7K6S7S1M:C; __bid_n=18dca6cbd5868644b98db5; MCITY=-149%3A; BA_HECTOR=a1a08ga52k81000001aha525ft23jt1j344lg1s; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; Hm_lvt_f7b8c775c6c8b6a716a75df506fb72df=1713682888,1713789099,1714303615,1714557618; PHPSESSID=3bhjttdud6a16bh4eolnjj7t34; Hm_lpvt_f7b8c775c6c8b6a716a75df506fb72df=1714569737; devStoken=d0920f8bf650a8e7a794e02fee1acac50bb04781cf372e302f0ad4103e614642; bjhStoken=5d6822e02e27b96d965a6e153cd934020bb04781cf372e302f0ad4103e614642; ab_sr=1.0.1_N2JlODVlM2RhYzZmZDc4ZGMxNjdmYWEzZDkyNDNmMTU5NzEyY2M3YjFjZjc1NDA0MmRjY2QxZjI3OTBjNTJmYzU2YjJkYWVkMTNmY2E1MzA1OTYyMjZlZjIxZjExYjE1NmUxMzhlYzA1NjE4MDZjYTI5ZTk4NjczYmU2YWNmMWMxZjMwMDJhNDVmNmEwOGVhYWUwMGE0ODI1M2I4NzE5MTdkYmY0YzBjNzk2OTE4NDA3MWZiMzdkODVlOGEwYWEy; RT="z=1&dm=baidu.com&si=7a77f68b-dddb-4b30-908b-3cd4e81a19ee&ss=lvnuf8vd&sl=3&tt=9ac&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=5gnt&nu=9y8m6cy&cl=4xyu"'
    headers = {
        'Cookie': cookie_value,
        'Token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9iYWlqaWFoYW8uYmFpZHUuY29tIiwiYXVkIjoiaHR0cDpcL1wvYmFpamlhaGFvLmJhaWR1LmNvbSIsImlhdCI6MTcxNDU2OTc2OSwibmJmIjoxNzE0NTI2NTc0LCJleHAiOjE3MTQ2MTI5NzR9.zq5ol7BsvMohUBX7UXPyWyl7ZMPVR4tdOBTXKwFTdPk'
    }
    form_data = {
        'content': '删除不可恢复，确认删除该条动态吗？',
        'action': 'recall',
        'id': id,
        'feed_id': feed_id,
    }
    response = requests.post(url, headers=headers,data=form_data)
    parsed_data = json.loads(response.text)
    print(parsed_data)


'''
1. 获取头条列表
2. 删除头条,根据ID
3. 文章删除是不一样的.
'''
import time
for i in range(1, 20):
    wtt_list(i)
    time.sleep(5)

# wtt_list()
# article_del()