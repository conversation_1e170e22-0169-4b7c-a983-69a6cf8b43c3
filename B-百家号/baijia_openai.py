import requests
import json

def call_openai_api(user_input):
    url = 'https://api.openai.com/v1/chat/completions'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ***************************************************'
    }
    # prompt="Please act as master copywriter. When writing, keep the themes of theoriginal text and use more colloquial and intense emotional expression, especially inthe first sentence. Do not print anything other than the text. Output in Chinese,theword limit should be 100 words or less."
    prompt="Please act as master copywriter. When writing, keep the themes of theoriginal text and use more colloquial and intense emotional expression, especially inthe first sentence. Do not print anything other than the text. Output in Chinese,theword limit should be 100 words or less.    Channel your inner copywriting genius! Stay true to the original text's essence but crank up the colloquial charm and emotional punch, especially right off the bat. No extra words, just pure power. Keep it under 100 words, in Chinese, and break lines for impact. The content of the article is wrapped in the appropriate position"
    msg = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": user_input
        }
    ]
    data = {
        "model": "gpt-3.5-turbo-0613",
        "messages":msg
      #   "messages": [
      #       {"role": "user", "content": user_input}
      #   ]
    }

    response = requests.post(url, headers=headers, json=data)
    result = response.json()

    return result['choices'][0]['message']['content']

# print(call_openai_api("想象一下，当时的情景该是怎样的呢？南沙区沥心沙大桥被船只撞击，一辆公交车砸在船上，司机梁金华却不见了踪影。根据报道，这位不幸的司机事发时，正在执行9路短线的任务，一切还在正常进行中。但就在5时31分，当他驾驶着粤A08386D的公交车行驶至沥心沙大桥时，桥面突然被撞断，车辆不幸掉入桥下。（图片来源于网络）我们可以想象当时的情景，公交车随着桥面断裂的巨响迅速失去支撑，半空中下坠的一刹那，司机和车厢内的一切都变得紧张起来。梁金华可能正在专注地驾驶，瞬间感受到了前方的巨大冲击力，车辆的失重感必然会让他内心充满恐惧。身处高空的车厢颠簸不已，可想而知，那时司机的心脏一定扑通扑通地狂跳，更不用说紧紧握住方向盘的双手了。（图片来源于网络）而现场照片显示，公交车落在了一艘集装箱船的船舱上。司机梁金华现处于失联状态，但我隐隐感觉情况不太简单！目前救援工作正在进行中，希望能尽快找到梁金华，祈祷他平安无事。这样一位在平凡岗位上默默付出的司机，此刻需要我们的关注和祝福。让我们一起为梁金华加油，期待他能够度过这段艰难的时刻，并希望最终能有好消息传来。加油，梁师傅！我们相信你能平安回来！"))