import requests

def download_image(url, save_path):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            print("图片已成功下载到:", save_path)
        else:
            print("下载失败")
    except Exception as e:
        print("下载出错:", str(e))


from imgdl import download
import os
def img_download(url_string,img_path):
    # url_string = "https://p3-sign.toutiaoimg.com/tos-cn-i-axegupay5k/d6311e663288499ba20245222d15b63e~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1709258648&x-signature=jH6%2FWf3OkjExU5NZt2T%2BsVdaOBw%3D, https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/4425bf1dd1574dd6848f27c9b35dec2a~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1709258648&x-signature=PbYtOcEXM6AhbuZ%2BFvaOa476EVQ%3D, https://p26-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/6329257aabf64285811fd3abbd07ff9c~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1709258648&x-signature=IfthyB3adUB7%2BlV53L7DTftBK4A%3D"
    # 使用split方法按逗号分割字符串，并去除空格
    url_array = [url.strip() for url in url_string.split(',')]
    # 指定存储路径为 'path_to_your_folder'
    # filename = os.getcwd()+'/result/image/'
    paths = download(url_array, store_path=img_path, n_workers=10)
    print(paths)

def img_download_array(url_array,img_path):
    paths = download(url_array, store_path=img_path, n_workers=10)
    print(paths)
    return paths

# 根据用户名查询图片
def search_uns_img(search_query:str):
    # 设置你的 Unsplash Access Key
    access_key = "I6rdXk0HLJv48y_a45Mf_8x1ToetCYW-ZK61wiJh48w"  # 替换成你的 Unsplash Access Key
    # 用户随机图片
    url = f"https://api.unsplash.com/photos/random/?username={search_query}&client_id={access_key}"
    # 发起API请求
    response = requests.get(url)
    # 搜所图片解析
    url=''
    # 检查请求是否成功
    if response.status_code == 200:
        # 解析响应数据
        data = response.json()
        # 提取图片链接
        image_url = data["urls"]["regular"]
        print("随机一张图片链接:", image_url)
        url=image_url
    else:
        print("请求失败:", response.status_code)
    return url

# 搜索图片随机返回图片
def random_uns_img(search_query:str):
    # 设置你的 Unsplash Access Key
    access_key = "I6rdXk0HLJv48y_a45Mf_8x1ToetCYW-ZK61wiJh48w"  # 替换成你的 Unsplash Access Key
    url = f"https://api.unsplash.com/photos/random?query={search_query}&client_id={access_key}"
    # 发起API请求
    response = requests.get(url)
    # 检查请求是否成功
    if response.status_code == 200:
        # 解析响应数据
        data = response.json()
        # 输出搜索结果中的图片链接
        image_url = data['urls']['regular']
        print(image_url)
        return image_url
    else:
        print("请求失败:", response.status_code)
def main(search_query=None):
    urls=[]
    # url=search_uns_img('marcus_ganahl')
    url=random_uns_img(search_query)
    urls.append(url)
    save_folder = "/Users/<USER>/temp/images/"  # 替换成你想要保存图片的文件夹路径
    paths=img_download_array(url_array=urls,img_path=save_folder)
    return paths[0] 

if __name__ == '__main__':
    main()