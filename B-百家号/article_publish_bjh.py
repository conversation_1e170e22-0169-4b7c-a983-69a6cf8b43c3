from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
import shutil
import os
import json
import random
import time

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options

from docx import Document


def check_for_images(doc_path):
    doc = Document(doc_path)
    has_images = False

    for paragraph in doc.paragraphs:
        if 'graphicData' in paragraph._p.xml:
            has_images = True
            break

    if has_images:
        print("The document contains images.")
        return True
    else:
        print("No images found in the document.")
        return False

def get_os():
    if os.name == 'nt':
        return 'Windows'
    elif os.name == 'posix':
        return 'macOS'
    else:
        return 'Unknown'

def bjh_publish(file_path: str, port: int):
    try:
        if 'Windows'==get_os():
            service = Service('C:\\Users\\<USER>\\AppData\\Local\\chromedriver-win64\\chromedriver.exe')  # 如果已经添加到PATH，只需提供可执行文件名
            # 设置Chrome选项
            options = Options()
            options.add_argument('--headless')  # 无头模式

            # 如果需要远程调试，可以添加以下参数
            ip = f'127.0.0.1:{port}'
            options.add_experimental_option("debuggerAddress", ip)  # 替换为你的调试端口

            # 初始化webdriver
            driver = webdriver.Chrome(service=service, options=options)
            driver.get('https://baijiahao.baidu.com/builder/rc/edit?type=news')
            time.sleep(5)
            driver.maximize_window()

        else:
            # 1.内容输入
            print('[开始百家号发布: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
            service = Service('/usr/local/bin/chromedriver')
            options = webdriver.ChromeOptions()
            ip = f'127.0.0.1:{port}'
            options.add_experimental_option("debuggerAddress", ip)
            options.add_argument('--headless')
            driver = webdriver.Chrome(service=service, options=options)
            driver.get('https://baijiahao.baidu.com/builder/rc/edit?type=news')
            time.sleep(5)

        # 2. 导入文章
        wait = WebDriverWait(driver, 10)  # 设置最大等待时间为10秒
        button = wait.until(EC.element_to_be_clickable((By.ID, 'edui43')))
        button.click()
        time.sleep(1)
        inputs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "input[type='file']")))
        second_input = inputs[1]
        second_input.send_keys(file_path)  # 替换成你本地图片的路径
        time.sleep(2)


        # 定位文本区域并输入内容
        try:
            file_name, extension = os.path.splitext(file_path)
            title_name = os.path.basename(file_name)
            textarea = driver.find_element(By.CSS_SELECTOR, '.cheetah-input.css-gixifq.cheetah-input-outlined.cheetah-input-status-success.cheetah-public.cheetah-public-nomalTextArea')
            # 清除现有内容 -TODO: 无法清空标题内容
            textarea.clear()
            # 输入新内容
            textarea.send_keys(title_name)
        except Exception as e:
            print(f"Error: {e}")

        # todo:fwh 滚到一半找不到图片 2/3
        # 滑动页面到页面内容的2/3处
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 2 / 3);")    
        # 滚动一半
        # driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 2);")
        # 滚动最后
        # driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        # 3. 选择单图
        # driver.find_element(By.XPATH, '//input[@type="radio" and @value="one"]').click()
        radio_button = driver.find_element(By.CSS_SELECTOR,
                                           'input[name="cover"][type="radio"][class="cheetah-radio-input"][value="one"]')
        radio_button.click()

        driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 2 / 3);")    

        # driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 2);")
        # 3.1 选择封面
        driver.find_element(By.XPATH, '//span[@class="placehold"]').click()
        time.sleep(1)
        driver.find_element(By.XPATH, '//div[@class="cheetah-ui-pro-base-image "]/div[1]').click()
        time.sleep(1)
        # 确认
        button = driver.find_element(By.CSS_SELECTOR,
                                     'button.cheetah-btn.cheetah-btn-primary.cheetah-public[style*="margin-left: 16px;"]')

        button.click()
        time.sleep(8)

        # 4. 发布
        button = driver.find_element(By.XPATH, "//div[contains(text(), '发布')]/preceding-sibling::button")
        button.click()
        # driver.find_element(By.XPATH,
        #                     '//button[@class="cheetah-btn cheetah-btn-primary cheetah-btn-circle cheetah-btn-icon-only cheetah-public QAqFnAdXxZX3P0PJEbj3 always-blue"]').click()
        time.sleep(1)
        # 保存草稿
        return True
    except Exception as e:
        print("无法找到元素", e)
        # 保存草稿-避免导致下次无法导入文档
        element=driver.find_elements(By.XPATH,
                            '//button[@class="cheetah-btn cheetah-btn-primary cheetah-btn-circle cheetah-btn-icon-only cheetah-public QAqFnAdXxZX3P0PJEbj3"]')
        
        for i in range(0, len(element)):
            if element[i].text()=='存草稿':
                element[i].click()
        time.sleep(1)
        return False
    finally:
        driver.quit()


# 移动文件
def move_article(source_file: str, destination_folder: str):
    # 检查目标文件是否存在
    destination_file = os.path.join(destination_folder, os.path.basename(source_file))
    if os.path.exists(destination_file):os.remove(destination_file)
    shutil.move(source_file, destination_folder)
    print("文件移动成功", source_file)


'''
头条号文章发布
1. 登录网址
2. 选择内容
3. 操作定时发布
4. TODO: 重点问题是如何正文可以生成正确的签名,然后发布.
接口形式,需要a_bogus的正确生成方式,相对于来说比较复杂.试错成本高.
'''


def main(file_path: str, port: int):
    # 2.发布文章
    return bjh_publish(file_path=file_path, port=port)


'''
TODO: 检查如下
1. 文章标题: 不要超过30字,不要有特殊符号,不能有emoji, 不能回车
2. 正文内容: 保持三张图片,注意段落换行和格式, 单标题和多标题的问题
3. 
'''
if __name__ == "__main__":
    file_path = '/Users/<USER>/temp/article/9223/伊lya离职后第一个动作：点赞了这篇论文.docx'
    file_path = 'D:\\fuwenhao\\collection\\article\\9223\\Google Pixel 8A 评测：精打细算的消费者的明智选择.docx'

    main(file_path,9223)
