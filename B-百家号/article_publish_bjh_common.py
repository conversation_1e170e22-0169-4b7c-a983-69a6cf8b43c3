import datetime
import os
import subprocess
import tempfile

import subprocess
import shlex
import time
import schedule
import os
import time
import system_util
os_type = system_util.get_os()




# 1. 生成文档
def general_article(client, port: int, params: str):
    results, next_cursor = client.query_results_by_condication(params, start_cursor=None)
    for page in results:
        if page["object"] == "page":
            # 1. 过滤
            page_tags = page["properties"]["Tags"]["multi_select"]
            tag_flag = False
            for item in page_tags:
                if item['name'] in ['百家号发布成功', '百家号发布失败']:
                    tag_flag = True
                    break
            if tag_flag:
                continue
            page_id = page["id"]
            page_title = page["properties"]['标题']['title'][0]['plain_text']
            try:
                markdown_content = client.retrieve_and_convert_to_markdown(page_id, client)
                markdown_content = markdown_content.replace("![Image]", "![ ]")
                # github的图片地址更换
                markdown_content = markdown_content.replace(
                    "https://raw.githubusercontent.com/wenhaofree/Image/master/blog",
                    "https://cdn.wenhaofree.com/gh/wenhaofree/Image/blog")

                # 校验内容长度
                print(f'内容长度:{len(markdown_content)}')
                if len(markdown_content) < 800:
                    print(f"内容太短，不生成文章.标题:{page_title}")
                    continue
                # 保存docx文件
                dirpath = f'/Users/<USER>/temp/article/{port}'
                if not os.path.exists(dirpath):
                    os.makedirs(dirpath)
                output_docx_file = f'{dirpath}/{page_title}.docx'
                # 文件是否存在
                if os.path.exists(output_docx_file):
                    print(f"文件已存在，不生成文章.标题:{page_title}")
                    return output_docx_file, page_id
                else:
                    markdown_to_docx_from_string(markdown_content, output_docx_file)
                    print(f'{output_docx_file} Done')
                    return output_docx_file, page_id
            except Exception as e:
                print(f'{page_id} {e}')
                return False, page_id
    return None, None


import sys

sys.path.append(os.path.dirname(__file__))
import article_publish_bjh
import shutil

def move_article(source_file: str, destination_folder: str):
    # 检查目标文件是否存在
    destination_file = os.path.join(destination_folder, os.path.basename(source_file))
    if os.path.exists(destination_file):os.remove(destination_file)
    shutil.move(source_file, destination_folder)
    print("文件移动成功", source_file)


def find_latest_docx_file(directory):
    # 初始化最新文件路径和其修改时间
    latest_file_path = None
    latest_mtime = 0

    # 遍历目录中的所有文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(".docx"):
                filepath = os.path.join(root, file)
                mtime = os.path.getmtime(filepath)
                
                file_name, extension = os.path.splitext(filepath)
                title_name = os.path.basename(file_name)
                if len(title_name)>30:
                    # 移动到  文章标题过长
                    parent_dir = os.path.abspath(os.path.join(directory, os.pardir))
                    parent_dir=f'{parent_dir}/文章标题过长/'
                    # parent_dir=parent_dir.join('/文章标题过长/')
                    move_article(source_file=filepath,destination_folder=parent_dir)
                    continue
                    
                # 如果当前文件的修改时间比已知的最新文件还要新，则更新最新文件信息
                if mtime > latest_mtime:
                    latest_mtime = mtime
                    latest_file_path = filepath

    return latest_file_path

def kill_process_by_port(port):
    try:
        # Get the PID of the process running on the specified port
        cmd = shlex.split(f"lsof -i :{port} -t")
        pid = subprocess.check_output(cmd).decode().strip()

        if pid:
            print(f"Killing process with PID {pid} on port {port}")
            subprocess.run(["kill", pid])
        else:
            print(f"No process found on port {port}")

    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")




def main(area: str, port: int):
    # 3. 发布文档到头条
    file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\历史文章\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/历史文章/'
    # 遍历最新的文件  修改时间为最新的
    latest_file = find_latest_docx_file(file_path)
    if latest_file:
        # 执行你的发布操作
        if article_publish_bjh.main(file_path=latest_file,port=port):
            print(f"{area}-百家号发布成功")
            # 移动文件到指定目录
            article_publish_bjh.move_article(latest_file, rf'/Volumes/文章存档/{area}/已发布头条/')
        else:
            print(f"{area}-百家号发布失败")
    else:
        print(f"没有找到{area}的 .docx 文件")


def run():
    function_to_area_mapping = {
        # '': 9227,
        # '新鲜事': 9226,
        # '职场': 9225,
        # '体育': 9224,
        '科技': 9223,
        # '情感': 9222,
    }
    for area, port in function_to_area_mapping.items():
        command = f'cd /Users/<USER>/fuwenhao/Bak/chromeDir && Google\\ Chrome --remote-debugging-port={port} --user-data-dir="./{port}/ChromeProfile"'
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        main(area, port)
        time.sleep(10)
        kill_process_by_port(port=port)


def job():
    print('[程序运行时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    run()
    print('[程序结束时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


def schedule_job():
    schedule.every().day.at("07:20").do(job)  # 指定时间触发
    schedule.every().day.at("11:20").do(job)  # 指定时间触发
    schedule.every().day.at("18:20").do(job)  # 指定时间触发
    schedule.every().day.at("20:20").do(job)  # 指定时间触发
    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    print('[百家号:程序启动...]')
    # schedule_job()
    run()