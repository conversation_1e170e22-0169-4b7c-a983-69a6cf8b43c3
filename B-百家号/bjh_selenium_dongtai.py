'''
百家号动态
1. 动态内容发布
2. 
'''

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import re
from notion_client import Client
class notion_client:
    def __init__(self):
        """
        初始化
        """
        global global_query_results
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        # global_database_id = "03661ee1554c4f43a2d583059ae8b282" #即刻-金句
        global_database_id = "d208c9d03e694d358ca03297d7d19b2c"#即刻-Data
        global_notion = Client(auth=global_token)
        global_query_results = global_notion.databases.query(database_id=global_database_id)
        print('开始Notion自动化获取数据...')
    # 获取数据
    def get_content(self):
        # 读取内容- 根据条件去查询数据,然后发布,然后更新.  需要pageID,正文内容.
        # 金句一小时发布一条.  一小时触发一次,选择一条数据
        # subpage_content = []
        # TODO-根据指定圈子查询内容.  金句一个账号. AI一个账号
        for page in global_query_results["results"]:
            if page["object"] == "page":
                # page_content = page["正文内容"]
                page_content=page["properties"]['正文内容']['rich_text'][0]['plain_text']
                page_id = page["id"]
                page_tags = page["properties"]["Tags"]["multi_select"]
                if len(page_tags)!=0:
                    tag=page_tags[0]['name']
                    print('发布状态:',tag)
                    if '发布失败'.__eq__(tag):
                        continue
                    elif '发布成功'.__eq__(tag):
                        continue
                if len(page_content)==0:
                    continue
                # if self.has_emoji(page_content):
                #     continue
                content={
                    page_id:page_content
                }
                return content
    def query_results_by_condication(self,quanzi_param:str,start_cursor=None):
        if start_cursor:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "start_cursor": start_cursor,
                    "filter": {
                        "and": [
                            {
                                "property": '圈子名称',
                                "select": {
                                    "equals": quanzi_param
                                }
                            },
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "is_empty": True
                                }
                            }
                        ]
                    }
                }
            )
        else:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "filter": {
                        "and": [
                            {
                                "property": '圈子名称',
                                "select": {
                                    "equals": quanzi_param
                                }
                            },
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "is_empty": True
                                }
                            }
                        ]
                    }
                }
            )
        # 获取结果和下一页的cursor
        results = response['results']
        next_cursor = response.get('next_cursor')
        return results,next_cursor

    def get_content_by_condication(self,quanzi_param:str,start_cursor=None):
        # TODO-根据指定圈子查询内容.  金句一个账号. AI一个账号
        results, next_cursor = self.query_results_by_condication(quanzi_param,start_cursor=start_cursor)
        for page in results:
            if page["object"] == "page":
                quanzi_value = page["properties"]["圈子名称"]["select"]['name']
                if quanzi_param!=quanzi_value:
                    continue
                else:
                    page_content=page["properties"]['正文内容']['rich_text'][0]['plain_text']
                    page_id = page["id"]
                    page_tags = page["properties"]["Tags"]["multi_select"]
                    if len(page_tags)!=0:
                        tag=page_tags[0]['name']
                        print('发布状态:',tag)
                        if '发布失败'.__eq__(tag):
                            continue
                        elif '发布成功'.__eq__(tag):
                            continue
                    # 长度小于100不要; 800可以转换文章
                    if len(page_content)<100:
                        continue
                    # 表情符号
                    # if self.has_emoji(page_content):
                    #     continue
                    content={
                        page_id:page_content
                    }
                    return content
        # 如果有下一页数据，则继续查询
        if next_cursor:
            print('下一页数据:',next_cursor)
            content=self.get_content_by_condication(quanzi_param, next_cursor)
            if content:
                return content
            # print(next_results)
                

    def has_emoji(self,s):
        emoji_pattern = re.compile("["
                            u"\U0001F600-\U0001F64F"  # emoticons
                            u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                            u"\U0001F680-\U0001F6FF"  # transport & map symbols
                            u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                            u"\U00002500-\U00002BEF"  # chinese char
                            u"\U00002702-\U000027B0"
                            u"\U00002702-\U000027B0"
                            u"\U000024C2-\U0001F251"
                            u"\U0001f926-\U0001f937"
                            u"\U00010000-\U0010ffff"
                            u"\u2640-\u2642"
                            u"\u2600-\u2B55"
                            u"\u200d"
                            u"\u23cf"
                            u"\u23e9"
                            u"\u231a"
                            u"\ufe0f"  # dingbats
                            u"\u3030"
                            "]+", flags=re.UNICODE)
        print('包含表情符号',bool(emoji_pattern.search(s)))
        return bool(emoji_pattern.search(s))

    def update_page_content_test(self, page_id, properties_params):
        page = global_notion.pages.retrieve(page_id)
        #更新属性是URL内容
        # 更新页面的属性
        update_payload = {
            "properties": {
                "Tags": {
                    "multi_select": [
                        {
                            "name": properties_params
                        }
                    ]
                },
                # 其他属性更新
            }
        }
        # 执行更新操作
        update_page=global_notion.pages.update(page_id=page_id, **update_payload)
        print("更新状态",properties_params)
    


    
from utils import img_down_util
from baijia_openai import call_openai_api
def dongtai_publish(content:str,port:int):
    try:
        print("开始百家号动态发布:",content)
        print("初始化Chrome WebDriver")
        service = Service('/usr/local/bin/chromedriver')
        options = webdriver.ChromeOptions()
        # TODO -不同的账号用不同的端口浏览器. 9222-158-金句; 9223-176-科技;
        # options.add_experimental_option("debuggerAddress", "127.0.0.1:9223")
        ip=f'127.0.0.1:{port}'
        options.add_experimental_option("debuggerAddress", ip)
        # options.add_argument('--headless')
        driver = webdriver.Chrome(service=service, options=options)
        driver.switch_to
        print("chrome 初始化完成")
        url="https://baijiahao.baidu.com/builder/rc/edit?type=events"
        driver.get(url)
        time.sleep(2)

        #实现正文输入
        wait = WebDriverWait(driver, 10)  # 设置最大等待时间为10秒
        # editor = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '.ProseMirror')))
         # 等待元素加载完成
        editor = wait.until(
            EC.presence_of_element_located((By.ID, "content"))
        )
        time.sleep(2)
        # 输入文本内容
        editor.clear()  # 清空文本框中的内容
        editor.send_keys(content)
        editor.send_keys(Keys.ENTER)
        editor.send_keys("#科技#")
        editor.send_keys(Keys.ENTER)
        
        # 选择地理位置
        local_button=driver.find_element(By.XPATH,"//span[@data-urlkey='CMS-发布动态-位置']")
        local_button.click()
        time.sleep(2)
        input_button=driver.find_element(By.XPATH,"//input[@class='cheetah-input header-input']")
        input_button.send_keys("北京市朝阳区中国科学技术馆")
        driver.find_element(By.XPATH,"//button[@class='cheetah-btn cheetah-btn-primary cheetah-public QAqFnAdXxZX3P0PJEbj3 header-search-button']").click()
        time.sleep(2)
        driver.find_element(By.XPATH,"//button[@class='cheetah-btn cheetah-btn-primary cheetah-public']").click()


        # 随机选择图片下载到指定文件夹
        try:
            if port==9222:
                search_name="coffee with book"
            elif port==9223:
                search_name="technology"
            elif port==9224:
                search_name="mood"
            elif port==9225:
                search_name="works"
            save_path=img_down_util.main(search_query=search_name)
        except Exception as e :
            print('下载图片异常,',e)
            save_path='/Users/<USER>/temp/images/ef5575a47ea1710811da726697ab4440f588402d.jpg'
        time.sleep(4)
        # #选择图片
        # 定位上传按钮并点击
        upload_button = driver.find_element(By.XPATH,"//div[@class='uploader-plus']")
        upload_button.click()
        time.sleep(2)
        upload_button = driver.find_element(By.XPATH,"//input[@type='file']")
        upload_button.send_keys(save_path)  # 替换成你本地图片的路径
        time.sleep(2)
        # 确定
        sure_button=driver.find_element(By.XPATH,"//button[@class='cheetah-btn cheetah-btn-primary cheetah-public QAqFnAdXxZX3P0PJEbj3']")
        sure_button.click()
        time.sleep(2)
        
        
        # 选择分类
        cagitor_button=driver.find_element(By.XPATH,"//span[@class='cheetah-select-selection-search']")
        cagitor_button.click()
        time.sleep(1)
        # 不同账号不同的分类-TODO
        if port==9222:
            select_button=driver.find_element(By.XPATH,"//span[@class='forum-list-item-text' and text()='可不可以给我说一句人生语录']")
        elif port==9223:
                cagitor_button=driver.find_element(By.XPATH,"//div[text()='科技']")
                cagitor_button.click()
                time.sleep(1)
                cagitor_button=driver.find_element(By.XPATH,"//div[@class='cheetah-cascader-menu-item-content' and text()='互联网']")
                cagitor_button.click()
                time.sleep(1)
        elif port==9224:
            select_button=driver.find_element(By.XPATH,"//span[@class='forum-list-item-text' and text()='情感']")
        
        # #最近话题
        # # select_button=driver.find_element(By.XPATH,"//span[@class='forum-list-item-text' and text()='可不可以给我说一句人生语录']")
        # select_button.click()
        # time.sleep(1)

        #发布
        element_view_publish = wait.until(EC.presence_of_element_located((By.XPATH,"//button[@class='cheetah-btn cheetah-btn-primary cheetah-btn-circle cheetah-btn-icon-only cheetah-public QAqFnAdXxZX3P0PJEbj3 events-op-bar-pub-btn events-op-bar-pub-btn-blue']")))
        element_view_publish.click()
        time.sleep(1)
        return True
    except Exception as e :
        print("无法找到元素",e)
        # driver.close()
        driver.quit()
        return False

    
    
def main(index:int):
    try:
        #1. 读取文字内容
        client=notion_client()
        #1.1 循环两次,查询金句一次发布-158; 查询AI一次发布-176; 不同端口号; 数据库采用一个相同的
        for i in range(1, 2): 
            if i==1:
                i=index
            if i==1:
                #158-金句-端口9222
                port=9222
                content = client.get_content_by_condication(quanzi_param='今日金句')
            elif i==2:
                #176-AI探索-端口9223
                port=9223
                content = client.get_content_by_condication(quanzi_param='AI探索站')
                # content = client.get_content_by_condication(quanzi_param='科技圈大小事')
            elif i==3:
                #176-心理学研究小组-端口9224-/Users/<USER>/fuwenhao/Bak/chromeDir
                port=9224
                content = client.get_content_by_condication(quanzi_param='心理学研究小组')

            if content is None:
                print('无法得到Notion数据')
                return
            page_id = list(content.keys())[0]
            page_content = list(content.values())[0]
        #1.2 AI洗稿
            try: 
                print("开始AI洗稿,原文:-----\n",page_content)
                page_content=call_openai_api(page_content)
                print('洗稿内容:-----\n',page_content)
            except Exception as e:
                print('AI接口调用失败发布原文',e)
            #1.3 内容长度必须800才能同步文章- 800以上内容较少.
            # if len(page_content)<800:
            #     print('内容长度小于800,跳过发布')
            #     continue
            #2. 发布微头条
            print('发布端口号为:',port)
            print('发布内容长度为:',len(page_content))
            flag=dongtai_publish(content=page_content,port=port)
            if flag:
                #3. 更新内容状态
                client.update_page_content_test(page_id=page_id,properties_params="发布成功")
            else:
                client.update_page_content_test(page_id=page_id,properties_params="发布失败")
                #4. 失败重新发布
                main(i)
    except Exception as e :
        print("百家发布异常",e)
    
'''
TODO:
1. 循环会单账户发布多条数据,需要处理,控制在一条或者两条
2. 176-阿宝-申请了微认证,需要保证原创规则
3. 
'''
if __name__ == "__main__":
    main(2)