'''
1. 定时发布一小时一篇. 需要百粉才有收益
2. 目前内容针对是一个账号的. 后续调整自动切换账号发布
3. 内容后续多平台发布
4. 目前账号158-领域金句-一小时一发
'''



import schedule
import time
from bjh_selenium_dongtai import main
def job():
    main(2)
    # 当前时间
    print('[程序完成...百家号动态发布完成]')
    print('[程序运行时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


def schedule_job():
    # schedule.every(1).hours.do(job)
    schedule.every(30).minutes.do(job)

    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    print('[程序启动...百家号动态发布]')
    # 调用定时任务函数
    schedule_job()