"""
https://www.cnblogs.com/lijiejoy/p/15842284.html
https://blog.csdn.net/qq_38161040/article/details/108161221
配置Chrome启动
https://blog.csdn.net/w5688414/article/details/106032555

终端启动：是新的浏览器，需要配置登录
Google\ Chrome --remote-debugging-port=5003 --user-data-dir="~/ChromeProfile"
Google\ Chrome --remote-debugging-port=5003 --user-data-dir="/Users/<USER>/dev/chrome_data/"

启动方式：
cd /Applications/Google Chrome.app/Contents/MacOS/
./Google\ Chrome --remote-debugging-port=5003

"""
from selenium import webdriver

# 另一个导入chrome参数的方法
# from selenium.webdriver.chrome.options import Options
# options = Options()
import os
os.environ["webdriver.Chrome.driver"] = "/usr/local/bin/chromedriver"
options = webdriver.ChromeOptions()
options.add_experimental_option("debuggerAddress", "127.0.0.1:5003")
driver = webdriver.Chrome(options = options)
print(driver.title)

