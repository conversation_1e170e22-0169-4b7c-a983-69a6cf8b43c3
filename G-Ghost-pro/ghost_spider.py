import requests
from bs4 import BeautifulSoup
import os
import json
import util_image
import kuake_api
from llm_siliconflow_http_test import call_content_common
from ghost_publish import TestGhostAPI
import os
from dotenv import load_dotenv
load_dotenv()
LOCAL_FILE_PATH=os.environ.get("LOCAL_FILE_PATH")



global_save_json='ahhhhf.json'
headers = {
    'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.20 SP-engine/2.16.0 baiduboxapp/********* (Baidu; P1 9)'
}

def save_ids(title: str, hmctdocid: str, reseaon: str,file_path_json:str):
    file_path = os.path.join(os.path.dirname(__file__), 'data', file_path_json)

    with open(file_path, 'a', encoding='utf-8') as f:
        try:
            insert_notion_data = {'id': hmctdocid, 'title': title, 'msg': reseaon}
            json.dump(insert_notion_data, f, ensure_ascii=False)
            f.write('\n')
            f.close()
        except Exception as e:
            print(f'保存Notion异常:{e}')
            f.close()

def get_exist_ids(file_path_json:str):
    # 根据ID校验是否已经保存过
    message_ids = set()
    file_path = os.path.join(os.path.dirname(__file__), 'data', file_path_json)
    if os.path.exists(file_path) is False:
        print(f'文件不存在:{file_path}')
        return message_ids
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                json_data = json.loads(line)
                message_ids.add(json_data['id'])
            f.close()
    except Exception as e:
        print(f'更新异常:{e}')
        pass
    return message_ids


def get_directory():
    for page in range(1, 30):
        print(f'page:{page}')
        url = f'https://www.ahhhhfs.com/recourse/page/{page}/'
        try:
            response = requests.get(url, headers=headers, timeout=30)
            soup = BeautifulSoup(response.text, 'html.parser')
            article_list = soup.find_all('article', class_='post-item item-list')
            
            for p_tag in article_list:
                tags=[]
                div_tag = p_tag.find('div', class_='entry-cat-dot')
                if div_tag:
                    categories = div_tag.find_all('a')
                    for category in categories:
                        # print(category.text) #标签
                        tags.append(category.text)

                a_tag = p_tag.find('a')
                if a_tag:
                    href = a_tag.get('href')
                    path = href.rstrip('/')
                    index = path.rfind('/')
                    id = path[index + 1:]
                    ids = get_exist_ids(global_save_json)
                    if id in ids: continue

                    title = a_tag.get('title')
                    # 图片下载到本地
                    img_bg = a_tag.get('data-bg')
                    local_img_bg=util_image.image_download(img_bg, f'{LOCAL_FILE_PATH}{id}/')
                    # print(f"href: {href}, title: {title}, id:{id}, img_bg:{img_bg}")
                    
                    get_detail(href,local_img_bg,title,id,tags)
        except Exception as e:
            print(f"Error in get_book_list for page {page}: {str(e)}")


def get_detail(url:str,local_img_bg:str,title:str,id:str,tags:list):
    """文章详情解析，文章URL，文章背景图"""
    try:
        response = requests.get(url, headers=headers, timeout=30)
        soup = BeautifulSoup(response.text, 'html.parser')
        # 1. 获取夸克下载链接
        # 查找所有 p 标签
        p_tags = soup.find_all('p')
        # 遍历所有 p 标签，查找包含“下载地址”文本的 p 标签
        href_link=''
        for p_tag in p_tags:
            if '下载地址' in p_tag.get_text():
                # 查找 p 标签中的 a 标签
                a_tag = p_tag.find('a')
                if a_tag:
                    # 获取 a 标签的 href 属性
                    if '夸克网盘'==a_tag.text.strip():
                        href_link = a_tag['href']
                        print(f'标题:{title}, 夸克网盘:{href_link}')
        if len(href_link)==0:
            print(f'没有夸克链接: 标题: {title}, URL: {url}')
            return
        
        #TODO 网盘转存
        new_share_url=kuake_api.new_share(href_link)
        if new_share_url is None: return


        # 2. 获取正文内容
        # 查找第一个 article 标签
        article_tag = soup.find('article')
        # 获取 article 标签中的文本内容
        article_text = article_tag.get_text(strip=True)
        # print(f'原文:\n {article_text}')

        
        article_text=call_content_common(user_content=article_text)
        # print(f'译文:\n {article_text}')
        
        article_text=f'{article_text} \n\n 下载地址：[夸克网盘]({new_share_url})'
        
        # 3. 发布文章
        test=TestGhostAPI()
        test.setUp()
        feature_image=test.test_upload_image(local_img_bg)
        status='published'
        result=test.test_create_post(title=title, markdown_content=article_text, status=status, feature_image=feature_image,tags=tags)
        if 'errors' in result:
            error=result['errors'][0]['context']
            print(f'发布失败:{error}')
            return 
        else:
            title=result['posts'][0]['title']
            print(f'发布成功:{title}')
            save_ids(title, id, 'success', global_save_json)

            # 4. 更新文章链接
            url=result['posts'][0]['url']
            post_id = result['posts'][0]['id']
            article_text=f'{article_text} \n\n 本文链接：{url}'
            update_result=test.test_update_post(post_id,updated_title=None,updated_content=article_text)
            # print(update_result)


    except Exception as e:
        print(f"Error in get_detail for {url}: {str(e)}")


def main():
    """采集ahhhhf的资源数据"""
    get_directory()

if __name__ == '__main__':
    main()

