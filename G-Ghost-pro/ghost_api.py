import requests
import jwt
from datetime import datetime

class GhostAPI:
    def __init__(self, api_url, admin_api_key):
        self.api_url = api_url
        self.id, self.secret = admin_api_key.split(':')

    def _get_token(self):
        iat = int(datetime.now().timestamp())
        header = {'alg': 'HS256', 'typ': 'JWT', 'kid': self.id}
        payload = {
            'iat': iat,
            'exp': iat + 5 * 60,
            'aud': '/admin/'
        }
        return jwt.encode(payload, bytes.fromhex(self.secret), algorithm='HS256', headers=header)

    def _get_headers(self):
        return {'Authorization': f'Ghost {self._get_token()}'}

    def get_posts(self, limit=10, page=1):
        url = f"{self.api_url}/posts/?limit={limit}&page={page}"
        response = requests.get(url, headers=self._get_headers())
        return response.json()

    def upload_image(self, image_path):
        url = f"{self.api_url}/images/upload/"
        files = {'file': open(image_path, 'rb')}
        # response = requests.post(url, headers=self._get_headers(), files=files)
        # return response.json()
        # 打开图片文件
        with open(image_path, 'rb') as image_file:
            # 构建请求体
            files = {
                'file': (image_path, image_file, 'image/jpeg')
            }
            response = requests.post(url, headers=self._get_headers(), files=files)
            return response.json()

    
    def create_post(self, title, mobiledoc, status='draft', feature_image=None):
        endpoint = f"{self.api_url}/posts/"
        data = {
            "posts": [{
                "title": title,
                "mobiledoc": mobiledoc,
                "status": status,
                "feature_image": feature_image
            }]
        }
        response = requests.post(endpoint, json=data, headers=self._get_headers())
        return response.json()
    
    def create_post_tag(self, title, mobiledoc, tags=None, status='draft', feature_image=None):
        endpoint = f"{self.api_url}/posts/"
        data = {
            "posts": [{
                "title": title,
                "mobiledoc": mobiledoc,
                "status": status,
                "feature_image": feature_image
            }]
        }
        if tags:
            data["posts"][0]["tags"] = [{"name": tag} for tag in tags]
    
        response = requests.post(endpoint, json=data, headers=self._get_headers())
        return response.json()
    
    def update_post(self, post_id, title=None, mobiledoc=None, status=None, feature_image=None):
        # First, get the current post data
        current_post = self.get_post(post_id)
        if not current_post:
            return None

        endpoint = f"{self.api_url}/posts/{post_id}/"
        
        # Use the server's current updated_at time
        current_time = current_post['updated_at']
        
        # Prepare the update data
        update_data = {"posts": [{
            "updated_at": current_time,
            "previous_published_at": current_post.get('published_at')
        }]}
        if title is not None:
            update_data["posts"][0]["title"] = title
        if mobiledoc is not None:
            update_data["posts"][0]["mobiledoc"] = mobiledoc
        if status is not None:
            update_data["posts"][0]["status"] = status
        if feature_image is not None:
            update_data["posts"][0]["feature_image"] = feature_image
        
        response = requests.put(endpoint, json=update_data, headers=self._get_headers())
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Failed to update post. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None

    def delete_post(self, post_id):
        url = f"{self.api_url}/posts/{post_id}/"
        response = requests.delete(url, headers=self._get_headers())
        return response.status_code == 204  # Returns True if successfully deleted
    
    def get_tags(self):
        url = f"{self.api_url}/tags/"
        response = requests.get(url, headers=self._get_headers())
        return response.json()

    def delete_tag(self, tag_id):
        url = f"{self.api_url}/tags/{tag_id}/"
        response = requests.delete(url, headers=self._get_headers())
        return response.status_code == 204  # Returns True if successfully deleted

    def delete_all_tags(self):
        tags = self.get_tags()
        if 'tags' in tags:
            for tag in tags['tags']:
                self.delete_tag(tag['id'])
        return True

    def get_post(self, post_id):
        endpoint = f"{self.api_url}/posts/{post_id}/?formats=mobiledoc"
        response = requests.get(endpoint, headers=self._get_headers())
        if response.status_code == 200:
            return response.json()['posts'][0]
        else:
            print(f"Failed to get post. Status code: {response.status_code}")
            return None
