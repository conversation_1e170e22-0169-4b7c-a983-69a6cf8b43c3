import os
from dotenv import load_dotenv
load_dotenv()
api_key=os.environ.get("SILICONFLOW_API_KEY")


import requests

url = "https://api.siliconflow.cn/v1/chat/completions"

content_prompt="""
请根据提供的任何内容，生成一篇固定格式的 Markdown 文章。文章应包含以下部分：

1. 标题（使用一级标题 #）
2. 简介（简要概述内容，使用普通段落）
3. 主要内容（使用二级标题 ## 和段落组织）
4. 总结（使用二级标题 ## 和段落）
5. 注意事项（使用二级标题 ## 和无序列表）

请确保文章逻辑连贯，段落通顺。即使输入内容不完整或不相关，也要尽力创造合理的内容填充每个部分。

#限制:
1. 不要出现任何的链接URL内容
2. 不要出现任何的网盘下载链接
3. 不要出现任何下载的内容字样

对于注意事项，请始终包含以下内容（使用无序列表）：

- 本文内容源于互联网，仅供参考和学习交流使用。
- 如有侵权问题，请联系我们，我们将及时处理。
- 用户在使用过程中请自行判断内容的真实性和可靠性。
- 如需商业用途，请购买正版授权。
- 联系方式：<EMAIL>

请以 Markdown 格式输出整个文章，确保包含所有必要的标记和结构。
"""

def call_content_common(user_content:str):
    payload = {
        "model": "deepseek-ai/DeepSeek-V2-Chat",   #alibaba/Qwen2-7B-Instruct   #deepseek-ai/deepseek-v2-chat
        "stream": False,
        "messages": [
            {
                "role": "system",
                "content": content_prompt,
            },
            {
                "role": "user",
                "content": user_content,
            }
        ],
        "max_tokens": 4096,
        "temperature": 0.8
    }
    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "authorization": f"Bearer {api_key}"
    }

    response = requests.post(url, json=payload, headers=headers)

    # print(response.text)
    result=response.json()
    content=result['choices'][0]['message']['content']
    # print(content)
    return content


# content_user="""
# 文章目录显示1课程信息2课程介绍3课程大纲4课程下载课程信息人像审美精修调色摄影学习课程，夸克网盘资源下载。课程介绍课程将导图、基础修饰、调色技法、光影认识等常用到的工具方法结合实例进行一一详解，对如何捕捉美姿、掌握光线运用、选取合适的拍摄背景等内容进行教学。课程大纲课程下载下载地址：夸克网盘👍更多夸克网盘资源集合👍更多阿里云盘资源集合👍更多PikPak网盘资源合集本文链接：https://www.ahhhhfs.com/62303/相关文章转载请保留原文链接谢谢！本站所有资源文章出自互联网收集整理，本站不参与制作，如果侵犯了您的合法权益，请联系本站我们会及时删除。本站发布资源来源于互联网，可能存在水印或者引流等信息，请用户擦亮眼睛自行鉴别，做一个有主见和判断力的用户。本站资源仅供研究、学习交流之用，若使用商业用途，请购买正版授权，否则产生的一切后果将由下载用户自行承担。联系方式（#替换成@）：feedback#abskoop.com
# """
