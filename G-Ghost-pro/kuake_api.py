import requests
import json
import re
import time
import hashlib
from typing import Dict, Union
import os
from dotenv import load_dotenv
load_dotenv()
cookie=os.environ.get("QUARK_COOKIE")



class QuarkAPI:
    def __init__(self, cookie):
        self.base_url = "https://drive.quark.cn/1/clouddrive"
        self.headers = {
            "Cookie": cookie,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.cookie=cookie



    def _extract_share_id(self, share_url):
        # 使用正则表达式从分享URL中提取分享ID
        match = re.search(r'/s/([a-zA-Z0-9]+)', share_url)
        if match:
            return match.group(1)
        else:
            raise ValueError("Invalid share URL format")


    def save_shared_file(self, share_url):
        # Extract share_id from the URL
        share_id = self._extract_share_id(share_url)
        
        # Get stoken first
        time.sleep(2)
        stoken = self._get_stoken(share_id)
        
        # Get share info using stoken
        time.sleep(2)
        file_info = self._get_share_info(share_id, stoken)
        
        # Save file to drive
        time.sleep(2)
        save_result = self._save_to_my_drive(file_info['data'], stoken)
        
        title = file_info['data']['share']['title']
        return title

    def _get_stoken(self, share_id, passcode=""):
        url = "https://drive-h.quark.cn/1/clouddrive/share/sharepage/token"
        data = {
            "pwd_id": share_id,
            "passcode": passcode
        }
        params = {
            "pr": "ucpro",
            "fr": "pc",
            "__dt": 912,
            "__t": int(time.time() * 1000)
        }
        response = requests.post(url, headers=self.headers, json=data, params=params)
        if response.status_code == 200:
            return response.json().get('data').get('stoken')
        else:
            raise Exception(f"Failed to get stoken. Status code: {response.status_code}, Response: {response.text}")

    def _get_share_info(self, share_id, stoken):
        url = "https://drive-h.quark.cn/1/clouddrive/share/sharepage/detail"
        params = {
            "pr": "ucpro",
            "fr": "pc",
            "pwd_id": share_id,
            "stoken": stoken,
            "force": 0,
            "_page": 1,
            "_size": 50,
            "_fetch_banner": 1,
            "_fetch_share": 1,
            "_fetch_total": 1,
            "_sort": "file_type:asc,updated_at:desc",
            "__dt": 1147,
            "__t": int(time.time() * 1000)
        }
        response = requests.get(url, headers=self.headers, params=params)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to get share info. Status code: {response.status_code}, Response: {response.text}")

    def _save_to_my_drive(self, file_info, stoken):
        url = "https://drive-pc.quark.cn/1/clouddrive/share/sharepage/save"
        params = {
            "pr": "ucpro",
            "fr": "pc",
            "__dt": 44591,
            "__t": int(time.time() * 1000)
        }
        
        # 检查必要的键是否存在
        if 'share' not in file_info or 'list' not in file_info or not file_info['list']:
            raise ValueError("Invalid file_info structure")

        try:
            data = {
                "fid_list": [file_info['share']['first_file']['fid']],
                "fid_token_list": [file_info['list'][0]['share_fid_token']],
                "to_pdir_fid": "16c5b4b0be404615afd51279c7418998",  # 网盘目录: 资源合集
                "pwd_id": file_info['share']['pwd_id'],
                "stoken": stoken,
                "pdir_fid": "0",
                "scene": "link"
            }
        except KeyError as e:
            raise ValueError(f"Missing required key in file_info: {e}")

        try:
            response = requests.post(url, headers=self.headers, params=params, json=data)
            response.raise_for_status()  # 这会在非200状态码时抛出异常
            return response.json()
        except requests.RequestException as e:
            error_msg = f"Failed to save file to drive. Error: {str(e)}"
            if response.status_code == 400:
                try:
                    error_details = response.json()
                    error_msg += f" Details: {error_details.get('message', 'Unknown error')}"
                except ValueError:
                    pass  # 如果响应不是有效的JSON，就忽略它
            raise Exception(error_msg)

    def get_curl(self,url: str, post=0, cookie=0, headers=None, nobody=0, method='GET'):
        if headers is None:
            headers = {}

        if cookie:
            headers['Cookie'] = cookie

        if post:
            if method != 'PUT':
                method = 'POST'
            response = requests.request(method, url, headers=headers, data=post, timeout=15, verify=False)
        else:
            response = requests.get(url, headers=headers, timeout=15, verify=False)

        if nobody:
            return response.headers

        return response.text

    def _get_search_fileid(self, title):
        page = 1
        page_size = 100
        new_file_id = None

        while True:
            search_url = f"https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q={title}&_page={page}&_size={page_size}&_fetch_total=1&_sort=file_type:desc,updated_at:desc&_is_hl=1"
            search_response = self.get_curl(search_url, 0, self.cookie, self.headers)
            search_data = json.loads(search_response)
            
            total = search_data['metadata']['_total']
            
            for item in search_data['data']['list']:
                if item['pdir_fid'] == "16c5b4b0be404615afd51279c7418998":  # 网盘目录:资源合集
                    new_file_id = item['fid']
                    return new_file_id  # 找到匹配项，立即返回

            # 检查是否已经遍历完所有结果
            if page * page_size >= total:
                break

            page += 1
            time.sleep(1)  # 添加短暂延迟，避免频繁请求

        return new_file_id  # 如果遍历完所有结果仍未找到，返回 None

    def create_new_share(self, new_file_id: str, title: str) -> Union[Dict, bool]:
        post_url = "https://drive-pc.quark.cn/1/clouddrive/share?pr=ucpro&fr=pc&uc_param_str="
        post_data = {"fid_list": [new_file_id], "title": title, "url_type": 1, "expired_type": 1}

        response = self.get_curl(post_url, json.dumps(post_data), self.cookie, self.headers)
        data = json.loads(response)

        if data['status'] == 200 and data['data'].get('task_id'):
            time.sleep(1)
            task_id = data['data']['task_id']
            task_url = f"https://drive-pc.quark.cn/1/clouddrive/task?pr=ucpro&fr=pc&uc_param_str=&task_id={task_id}&retry_index=1"
            
            task_response = self.get_curl(task_url, 0, self.cookie, self.headers)
            task_data = json.loads(task_response)

            if task_data['status'] == 200 and task_data['data'].get('share_id'):
                share_id = task_data['data']['share_id']
                password_url = "https://drive-pc.quark.cn/1/clouddrive/share/password?pr=ucpro&fr=pc&uc_param_str="
                password_data = {"share_id": share_id}

                password_response = self.get_curl(password_url, json.dumps(password_data), self.cookie, self.headers)
                password_result = json.loads(password_response)

                if password_result['status'] == 200 and password_result['data'].get('share_url'):
                    share_url = password_result['data']['share_url']
                    new_title = password_result['data']['title']
                    new_code = password_result['data']['pwd_id']

                    db_data = {
                        'title': title,
                        'code': new_code,  # Using new_code instead of code as it's not defined in this scope
                        'url': share_url,
                        'md5': hashlib.md5((new_code + 'Kuakewangpan&^*').encode()).hexdigest(),
                        'new_code': new_code,
                        'new_title': new_title,
                        'new_url': share_url,
                        'file_id': new_file_id,
                        'type': 1,
                    }
                    return db_data


def new_share(share_url:str):
    try:
        # 1. 检索网盘是否存在文件夹
        # cookie = "_UP_A4A_11_=wb9661e17cbd43689c42b592fda0842a; b-user-id=7d8b339a-ea5f-92d4-3024-dc7bf859f8bb; _UP_30C_6A_=st9686201b5b8aypaujhx2084edyqifo; _UP_TS_=sg1e6c9710ff77bbe8c2e111c6af9a9e725; _UP_E37_B7_=sg1e6c9710ff77bbe8c2e111c6af9a9e725; _UP_TG_=st9686201b5b8aypaujhx2084edyqifo; _UP_335_2B_=1; __pus=351f56d22ed7fbea9f5f5c827245a1e1AARduGo1xUg0izbYj5QBxeujeud2B0z4KpNAXlOU51zdPIa+bnLUg8QBY/UCEFz1ww34mNSRRJzGYpIXwchzEPS/; __kp=86a2f250-65b0-11ef-a052-c123c8c134d8; __kps=AAQp5PKmU+OVxDsUAli1xKZT; __ktd=o828Q0uPIkC1qXexmvVezw==; __uid=AAQp5PKmU+OVxDsUAli1xKZT; xlly_s=1; tfstk=fwErHY4yqDVj8kIBAYoE3xEo2li-80fsZkGIKJ2nFbcoAaUUuJwNevN7KXzEiJhkEbDkYEDIQ9BpqHmEgjV0F3GIqXvUOJa7TJB-KXV3TXa5lGw8eDnh1WS1f8KOqzeU4DAQn3Il9S11fGwlrYnQn16pHwDisvmnE4xontkIQLmnKYXqivk9-QV3t-XqKvDoKbDom-DnUDc3tDX4nUN8EJxqC4XNa0sYREMqzf2PkHxU0hgrs8c2x1qqUDhgEj-Hxbd34k23t6-IjPF4YVV1Ynn7QrmmUrWHob0ar7MYTG-oZoyuqbZlG3Gu2-4_ASXHxXz0SVzgEsTq3r270qZG_Hu8o7Ut8k1fX0a_Ck3gqgRtGVHU_04Vi3PG4xdKnwqYvzRH84Dx3f6V3P93cO7siqnpJe3mXxl1FYTpJ4cs3f6VVeLKyQMq1TI5.; isg=BPb2Dwx1jJzGf3hupSdtHMGPRyr4FzpRCUQ4P2Df4ll0o5Y9yKeKYVzRu3fPCzJp; __puus=be7babb72e6223986b56fabb6c5f83ceAAQ71asxqBVtDMFfNWMF4m9m214wRmwK3T1R3uPxLttxjLTNMGc+mTNgq+w8xRIa64QY/2gdBJqhvyLIJhTcbS2jWehcpW4gr+BZue6idTS5Wr8lnscIKHQRqUJJgJxI0K6SrYDiVlA1HrAjeQ3ueN2JnHS5qPkDkso3eWZ2B8+/fcLXq7CT5gr+rc9x0OmJV00XTyccnnvhqQSjOZ4tP2UZ"  # 替换为你的夸克网盘cookie
        quark_api = QuarkAPI(cookie)

        share_id = quark_api._extract_share_id(share_url)
        stoken = quark_api._get_stoken(share_id)
        time.sleep(1)
        file_info = quark_api._get_share_info(share_id, stoken)
        title=file_info['data']['share']['title']

        fileid = quark_api._get_search_fileid(title=title)
        print(f"Search result file ID: {fileid}")


        if fileid:
            # 2. 存在. 直接创建分享
            print(f"File found. Creating new share for file ID: {fileid}")
            data = quark_api.create_new_share(fileid, title)
            if data is None:
                print("create_new_share returned None")
                return None
            new_share_url = data.get('url')
            if new_share_url:
                print(f"New share URL created: {new_share_url}")
                return new_share_url
            else:
                print(f"No URL found in data: {data}")
                return None
        else:
            # 3. 不存在. 保存网盘后再创建分享
            print(f"File not found. Saving shared file from URL: {share_url}")
            title = quark_api.save_shared_file(share_url)
            print(f"File saved with title: {title}")
            time.sleep(30)
            print(f"Searching for newly saved file with title: {title}")
            fileid = quark_api._get_search_fileid(title=title)
            print(f"Search result file ID: {fileid}")
            
            if fileid:
                print(f"Creating new share for file ID: {fileid}")
                data = quark_api.create_new_share(fileid, title)
                if data is None:
                    print("create_new_share returned None")
                    return None
                new_share_url = data.get('url')
                if new_share_url:
                    print(f"New share URL created: {new_share_url}")
                    return new_share_url
                else:
                    print(f"No URL found in data: {data}")
                    return None
            else:
                print("Failed to find the newly saved file")
                return None
    except Exception as e:
        print(f'生成新夸克链接异常: {e}')
        import traceback
        print(traceback.format_exc())
    return None


def main():
    # 使用示例
    # cookie = "_UP_A4A_11_=wb9661e17cbd43689c42b592fda0842a; b-user-id=7d8b339a-ea5f-92d4-3024-dc7bf859f8bb; _UP_30C_6A_=st9686201b5b8aypaujhx2084edyqifo; _UP_TS_=sg1e6c9710ff77bbe8c2e111c6af9a9e725; _UP_E37_B7_=sg1e6c9710ff77bbe8c2e111c6af9a9e725; _UP_TG_=st9686201b5b8aypaujhx2084edyqifo; _UP_335_2B_=1; __pus=351f56d22ed7fbea9f5f5c827245a1e1AARduGo1xUg0izbYj5QBxeujeud2B0z4KpNAXlOU51zdPIa+bnLUg8QBY/UCEFz1ww34mNSRRJzGYpIXwchzEPS/; __kp=86a2f250-65b0-11ef-a052-c123c8c134d8; __kps=AAQp5PKmU+OVxDsUAli1xKZT; __ktd=o828Q0uPIkC1qXexmvVezw==; __uid=AAQp5PKmU+OVxDsUAli1xKZT; xlly_s=1; tfstk=fwErHY4yqDVj8kIBAYoE3xEo2li-80fsZkGIKJ2nFbcoAaUUuJwNevN7KXzEiJhkEbDkYEDIQ9BpqHmEgjV0F3GIqXvUOJa7TJB-KXV3TXa5lGw8eDnh1WS1f8KOqzeU4DAQn3Il9S11fGwlrYnQn16pHwDisvmnE4xontkIQLmnKYXqivk9-QV3t-XqKvDoKbDom-DnUDc3tDX4nUN8EJxqC4XNa0sYREMqzf2PkHxU0hgrs8c2x1qqUDhgEj-Hxbd34k23t6-IjPF4YVV1Ynn7QrmmUrWHob0ar7MYTG-oZoyuqbZlG3Gu2-4_ASXHxXz0SVzgEsTq3r270qZG_Hu8o7Ut8k1fX0a_Ck3gqgRtGVHU_04Vi3PG4xdKnwqYvzRH84Dx3f6V3P93cO7siqnpJe3mXxl1FYTpJ4cs3f6VVeLKyQMq1TI5.; isg=BPb2Dwx1jJzGf3hupSdtHMGPRyr4FzpRCUQ4P2Df4ll0o5Y9yKeKYVzRu3fPCzJp; __puus=be7babb72e6223986b56fabb6c5f83ceAAQ71asxqBVtDMFfNWMF4m9m214wRmwK3T1R3uPxLttxjLTNMGc+mTNgq+w8xRIa64QY/2gdBJqhvyLIJhTcbS2jWehcpW4gr+BZue6idTS5Wr8lnscIKHQRqUJJgJxI0K6SrYDiVlA1HrAjeQ3ueN2JnHS5qPkDkso3eWZ2B8+/fcLXq7CT5gr+rc9x0OmJV00XTyccnnvhqQSjOZ4tP2UZ"  # 替换为你的夸克网盘cookie
    quark_api = QuarkAPI(cookie)

    # 保存分享的文件
    share_url = "https://pan.quark.cn/s/935e615bb78a"  # 替换为实际的分享链接
    title = quark_api.save_shared_file(share_url)
    
    # 生成新的分享保存的文件
    fileid=quark_api._get_search_fileid(title=title)
    data=quark_api.create_new_share(fileid,title)
    share_url=data['url']
    print("New share URL:", share_url)

if __name__ == "__main__":
    main()
