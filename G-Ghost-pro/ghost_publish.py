import unittest
from ghost_api import GhostAPI
import json
import os
from dotenv import load_dotenv
load_dotenv()
api_url=os.environ.get("GHOST_API_URL")
admin_api_key=os.environ.get("GHOST_ADMIN_API_KEY")


class TestGhostAPI(unittest.TestCase):
    def setUp(self):
        self.api = GhostAPI(api_url, admin_api_key)

    def test_get_posts(self):
        posts = self.api.get_posts()
        self.assertIn('posts', posts)
        self.assertIsInstance(posts['posts'], list)

    def test_upload_image(self,image_path:str):
        result = self.api.upload_image(image_path)
        self.assertIn('images', result)
        self.assertIsInstance(result['images'], list)
        self.assertIn('url', result['images'][0])
        return result['images'][0]['url']

    def test_delete_all_post(self):
        posts = self.api.get_posts()
        for post in posts['posts']:
            if 'draft' in post['status']:
                self.api.delete_post(post['id'])
            if 'marvin' in post['authors'][0]['slug']:
                self.api.delete_post(post['id'])
            
            self.api.delete_post(post['id'])

    def test_delete_draft_post(self):
        """删除所有草稿"""
        posts = self.api.get_posts()
        for post in posts['posts']:
            if 'draft' in post['status']:
                self.api.delete_post(post['id'])


    def test_update_post(self,post_id,updated_title=None, updated_content=None, status=None, feature_image=None):
        """更新文章内容"""
        mobiledoc = self.markdown_to_mobiledoc(updated_content)
        updated_result = self.api.update_post(
            post_id,
            title=updated_title,
            mobiledoc=mobiledoc
        )
        return updated_result
            
    def test_create_post(self, title, markdown_content, status, tags, feature_image=None):
        mobiledoc = self.markdown_to_mobiledoc(markdown_content)
        # result = self.api.create_post(title, mobiledoc, status, feature_image)
        result = self.api.create_post_tag(title, mobiledoc, tags, status, feature_image)
        return result
    
        
    def markdown_to_mobiledoc(self, markdown_content):
        mobiledoc = {
            "version": "0.3.1",
            "markups": [],
            "atoms": [],
            "cards": [
                ["markdown", {
                    "markdown": markdown_content
                }]
            ],
            "sections": [[10, 0]]
        }
        return json.dumps(mobiledoc)

    def test_create_and_delete_post(self):
        # Create a post
        title = "正文内容测试"
        html_content = "<p>This is a test post.</p>"
        result = self.api.create_post(title, html_content,'published')
        self.assertIn('posts', result)
        self.assertEqual(result['posts'][0]['title'], title)
        
        # Get the created post ID
        post_id = result['posts'][0]['id']
        
        # Delete the post
        deleted = self.api.delete_post(post_id)
        self.assertTrue(deleted)

    def test_delete_all_tags(self):
        # Get all tags
        tags = self.api.get_tags()
        self.assertIn('tags', tags)
        self.assertIsInstance(tags['tags'], list)

        # Delete each tag
        for tag in tags['tags']:
            deleted = self.api.delete_tag(tag['id'])
            self.assertTrue(deleted)

        # Verify all tags are deleted
        tags_after = self.api.get_tags()
        self.assertEqual(len(tags_after['tags']), 0)

def main():
    """
    1. 上传图片
    2. 创建文章
    """
    test=TestGhostAPI()
    test.setUp()
    # feature_image=test.test_upload_image('/Volumes/文章存档/Images/ahhhhfs/62299/606054463121.png')
    feature_image='https://digitalpress.fra1.cdn.digitaloceanspaces.com/ju9xotp/2024/09/606054463121.png'
    status='published'
    markdown_content="""#测试 ##真的测试  真的"""
    test.test_create_post(title='Markdown格式测试02,AI时代', markdown_content=markdown_content, status=status, feature_image=feature_image)
if __name__ == '__main__':
    main()


