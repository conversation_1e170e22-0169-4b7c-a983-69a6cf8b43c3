from concurrent.futures import ThreadPoolExecutor
'''
1. 适合多线程处理循环的数据,一个线程处理一个数据. 
'''

import time
# 定义一个函数，用于处理数据
def process_data(data):
    print('开始处理', data)
    time.sleee (1)
    # 在这里放入你的复杂逻辑。
    print('完成处理', data)

def main():
    # 假设这是你的数据列表
    data_list = [1, 2, 3, 4, 5]

    # 使用ThreadPoolExecutor来运行5个工作线程
    with ThreadPoolExecutor(5) as executor:
        # 循环内调用多线程处理数据
        for data in data_list:
            executor.submit(process_data, data)
import pandas as pd

def read_file(fileName):
    try:
        df = pd.read_excel(fileName)
    except UnicodeDecodeError:
        df = pd.read_excel(fileName, encoding='latin1')
    
    url_list=[]
    # 条件筛选
    for index, row in df.iterrows():
        # 打印每d一行的第三列数据值
        if int(row.iloc[3])>30:
            url_list.append(row.iloc[2])
            # if 'video_movie' in str(row.iloc[4]):
            #     continue
            # elif isinstance(row.iloc[4], float) and math.isnan(row.iloc[4]):
            #     continue
            # elif 'toutiao.com' not in row.iloc[2]:
            #     continue
            # else:
            #     url_list.append(row.iloc[2])
    print('共计数量:',len(url_list))
    return url_list
import os 
import re
def test():
    file_name=os.getcwd()+'/result/toutiao-selenium-2024-02-23-16-01-54.xlsx'
    url_list=read_file(fileName=file_name)
         # 使用ThreadPoolExecutor来运行5个工作线程
    with ThreadPoolExecutor(5) as executor:
        # 循环内调用多线程处理数据
        for url in url_list:
            groupId = re.findall(r'\d+', url)[-1]
            executor.submit(process_data, groupId)
    print('完成000000')
if __name__ == '__main__':
    test()