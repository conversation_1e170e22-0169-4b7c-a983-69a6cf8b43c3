from concurrent.futures import ThreadPoolExecutor
'''
1. 适合多线程处理循环的数据,一个线程处理一个数据. 
'''

# 定义一个函数，用于处理数据
def process_data(data):
    print('开始处理', data)
    # 在这里放入你的复杂逻辑。
    print('完成处理', data)

def main():
    # 假设这是你的数据列表
    data_list = [1, 2, 3, 4, 5]

    # 使用ThreadPoolExecutor来运行5个工作线程
    with ThreadPoolExecutor(5) as executor:
        # 循环内调用多线程处理数据
        for data in data_list:
            executor.submit(process_data, data)

if __name__ == '__main__':
    main()