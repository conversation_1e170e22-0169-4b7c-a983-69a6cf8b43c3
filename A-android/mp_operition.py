import os
import time
import subprocess
import logging
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AndroidController:
    def __init__(self):
        # 设置ADB路径，根据实际安装路径修改
        self.adb_path = os.path.expanduser('/Users/<USER>/dev/adb-platform-tools/adb')
        self.check_adb_connection()
    
    def check_adb_connection(self):
        """检查ADB连接状态"""
        try:
            devices = subprocess.check_output([self.adb_path, 'devices']).decode('utf-8')
            if 'device' not in devices:
                raise Exception("没有找到已连接的设备")
            logger.info("ADB连接成功")
        except Exception as e:
            logger.error(f"ADB连接失败: {str(e)}")
            raise

    def tap(self, x, y):
        """点击屏幕指定坐标"""
        try:
            # 使用非root方式点击
            cmd = [self.adb_path, 'shell', 'input', 'tap', str(x), str(y)]
            subprocess.run(cmd, check=True)
            logger.info(f"点击坐标: ({x}, {y})")
            time.sleep(1)
        except Exception as e:
            logger.error(f"点击操作失败: {str(e)}")
            # 尝试备用方法
            try:
                cmd = [self.adb_path, 'shell', 'input', 'touchscreen', 'tap', str(x), str(y)]
                subprocess.run(cmd, check=True)
                logger.info(f"使用备用方法点击坐标: ({x}, {y})")
            except Exception as e2:
                logger.error(f"备用点击方法也失败: {str(e2)}")

    def swipe(self, x1, y1, x2, y2, duration=500):
        """滑动屏幕"""
        try:
            # 使用非root方式滑动
            cmd = [self.adb_path, 'shell', 'input', 'swipe', 
                  str(x1), str(y1), str(x2), str(y2), str(duration)]
            subprocess.run(cmd, check=True)
            logger.info(f"滑动操作: 从({x1}, {y1})到({x2}, {y2})")
            time.sleep(1)
        except Exception as e:
            logger.error(f"滑动操作失败: {str(e)}")

    def get_screen_size(self):
        """获取屏幕分辨率"""
        try:
            output = subprocess.check_output([self.adb_path, 'shell', 'wm', 'size']).decode('utf-8')
            width, height = map(int, output.split()[-1].split('x'))
            logger.info(f"屏幕分辨率: {width}x{height}")
            return width, height
        except Exception as e:
            logger.error(f"获取屏幕分辨率失败: {str(e)}")
            return None

    def get_page_buttons(self):
        """获取页面上所有可点击元素的坐标"""
        try:
            # 获取当前界面的XML布局
            result = subprocess.check_output([self.adb_path, 'shell', 'uiautomator', 'dump', '/sdcard/window_dump.xml'])
            result = subprocess.check_output([self.adb_path, 'pull', '/sdcard/window_dump.xml', '.'])
            
            with open('window_dump.xml', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有可点击元素
            clickable_elements = re.finditer(r'clickable="true"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"', content)
            buttons = []
            
            for match in clickable_elements:
                x1, y1, x2, y2 = map(int, match.groups())
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                buttons.append((center_x, center_y))
                logger.info(f"找到可点击元素: 中心点({center_x}, {center_y})")
            
            return buttons
            
        except Exception as e:
            logger.error(f"获取页面按钮失败: {str(e)}")
            return []

    def click_all_buttons(self):
        """点击页面上所有按钮"""
        try:
            buttons = self.get_page_buttons()
            logger.info(f"总共找到 {len(buttons)} 个可点击元素")
            
            for i, (x, y) in enumerate(buttons, 1):
                logger.info(f"点击第 {i} 个按钮")
                self.tap(x, y)
                time.sleep(1)  # 等待可能的页面变化

          
                
        except Exception as e:
            logger.error(f"点击按钮操作失败: {str(e)}")

    def open_mp_helper(self):
        """打开公众号助手"""
        try:
            subprocess.run([self.adb_path, 'shell', 'monkey', '-p', 'com.tencent.mp',
                          '-c', 'android.intent.category.LAUNCHER', '1'])
            logger.info("启动公众号助手")
            time.sleep(3)
        except Exception as e:
            logger.error(f"打开公众号助手失败: {str(e)}")

    def get_text_content(self, x, y, search_down=True):
        """获取指定坐标的文本内容，可选择是否搜索下方文本"""
        try:
            # 导出当前界面布局
            subprocess.run([self.adb_path, 'shell', 'uiautomator', 'dump', '/sdcard/window_dump.xml'])
            subprocess.run([self.adb_path, 'pull', '/sdcard/window_dump.xml', '.'])
            
            with open('window_dump.xml', 'r', encoding='utf-8') as f:
                content = f.read()
            
            elements = re.finditer(r'<node[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*text="([^"]*)"[^>]*?/>', content)
            
            nearby_texts = []
            below_texts = []  # 存储下方的文本
            
            for match in elements:
                x1, y1, x2, y2, text = match.groups()
                x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
                
                if text.strip():
                    if search_down and y1 > y and abs(x - ((x1 + x2) // 2)) < 300:
                        # 存储下方文本及其坐标
                        below_texts.append({
                            'y': y1,
                            'text': text,
                            'coords': (x1, y1, x2, y2),
                            'center': ((x1 + x2) // 2, (y1 + y2) // 2)
                        })
                    elif abs(y - y1) < 100 and abs(x - ((x1 + x2) // 2)) < 200:
                        nearby_texts.append(f"坐标范围[{x1},{y1}][{x2},{y2}] 文本内容: {text}")
            
            if nearby_texts:
                logger.info(f"\n在坐标({x}, {y})附近找到以下文本:")
                for text in nearby_texts:
                    logger.info(text)
            
            if below_texts:
                below_texts.sort(key=lambda x: x['y'])  # 按y坐标排序
                logger.info(f"\n在坐标({x}, {y})下方找到以下文本:")
                for item in below_texts[:5]:
                    logger.info(f"坐标范围[{item['coords'][0]},{item['coords'][1]}][{item['coords'][2]},{item['coords'][3]}] 文本内容: {item['text']}")
                return below_texts  # 返回下方文本列表
            
            if not nearby_texts and not below_texts:
                logger.info(f"在坐标({x}, {y})附近和下方未找到文本内容")
            return []
            
        except Exception as e:
            logger.error(f"获取文本内容失败: {str(e)}")
            return []

    def find_and_click_delete_button(self, start_x, start_y):
        """查找并点击删除按钮"""
        try:
            # 获取下方的所有文本元素
            below_texts = self.get_text_content(start_x, start_y, search_down=True)
            
            # 查找包含"删除"的按钮
            for item in below_texts:
                if "删除" in item['text']:
                    click_x, click_y = item['center']
                    logger.info(f"找到删除按钮，位置: ({click_x}, {click_y})")
                    self.tap(click_x, click_y)
                    return True
            
            logger.info("未找到删除按钮")
            return False
            
        except Exception as e:
            logger.error(f"查找删除按钮失败: {str(e)}")
            return False

    def get_text_position(self, target_text):
        """获取指定文本在界面上的位置"""
        try:
            # 导出当前界面布局
            subprocess.run([self.adb_path, 'shell', 'uiautomator', 'dump', '/sdcard/window_dump.xml'])
            subprocess.run([self.adb_path, 'pull', '/sdcard/window_dump.xml', '.'])
            
            with open('window_dump.xml', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找特定文本的元素
            pattern = f'<node[^>]*text="{target_text}"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*?/>'
            matches = re.finditer(pattern, content)
            
            elements = []
            for match in matches:
                x1, y1, x2, y2 = map(int, match.groups())
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                elements.append({
                    'text': target_text,
                    'bounds': (x1, y1, x2, y2),
                    'center': (center_x, center_y)
                })
                logger.info(f"找到文本'{target_text}'的元素: 中心点({center_x}, {center_y}), 范围[{x1},{y1}][{x2},{y2}]")
            
            return elements
            
        except Exception as e:
            logger.error(f"获取文本'{target_text}'位置失败: {str(e)}")
            return []

    def operate_subscription(self):
        """操作公众号订阅号"""
        try:
            # 记住: 手机先打开公众号助手的全部列表页面:
            for i in range(140):
                logger.info(f"操作公众号第 {i+1} 个文章")
                # 点击三点按钮
                self.tap(1112, 898)
                # time.sleep(1)
                
                # 查找"删除文章"按钮
                delete_elements = self.get_text_position("删除文章")
                if delete_elements:
                    # 点击第一个找到的"删除文章"按钮
                    x, y = delete_elements[0]['center']
                    self.tap(x, y)
                    # time.sleep(1)
                    
                    # 查找确认删除按钮
                    confirm_elements = self.get_text_position("删除")
                    if confirm_elements:
                        x, y = confirm_elements[0]['center']
                        self.tap(x, y)
                        time.sleep(3)
                    else:
                        logger.error("未找到确认删除按钮")
                else:
                    logger.error("未找到删除文章按钮")

        except Exception as e:
            logger.error(f"操作公众号失败: {str(e)}")

def main():
    """公众号助手操作-批量删除已发表文章"""
    """
    1. 手机开发者模式打开: https://blog.csdn.net/xiangjiu99/article/details/143975581
    """
    try:
        controller = AndroidController()
        # controller.open_mp_helper()
        controller.operate_subscription()
        logger.info("公众号操作任务完成")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()
