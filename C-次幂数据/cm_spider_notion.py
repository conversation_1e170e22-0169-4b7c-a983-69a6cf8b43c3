


import requests,json,os,time
from datetime import datetime, timedelta
from notion_client import Client

class notion_client:
    def __init__(self):
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        global_database_id = "c495d8a81ed54703b333d2404e1c625a"  # 易撰-Data
        global_notion = Client(auth=global_token)
        print('开始Notion自动化获取数据...')

    def get_yesterday(self):
        # 获取当前时间
        current_time = datetime.now()
        # 计算前一天的时间
        # yesterday = current_time - datetime.timedelta(days=2)
        yesterday = current_time - timedelta(days=2)

        formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
        print(formatted_time)
        return formatted_time

    def update_page_content(self, page_id, properties_params):
        # 更新页面的属性
        update_payload = {
            "properties": {
                "注册时间": {
                    "date": {"start": properties_params['register_at']}  # 使用 ISO 8601 格式
                },
                "粉丝数": {
                    "number":  properties_params['follower_count']  # 直接使用数字
                },
                "正文内容": {
                    "rich_text": [
                        {
                            "text": {
                                "content": properties_params['description']
                            }
                        }
                    ]
                },
                # 其他属性更新
            },
        }
        # 执行更新操作
        update_page = global_notion.pages.update(page_id=page_id, **update_payload)
        print("更新状态", properties_params)

    def create_page(self, page):
        new_page = global_notion.pages.create(
            parent={
                'database_id': global_database_id
            },
            properties={
                '标题': {
                    'title': [
                        {
                            'text': {
                                'content': page['title']
                            }
                        }
                    ]
                },
                '作者': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['account_nickname']
                            }
                        }
                    ]
                },
                '正文内容': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['description']
                            }
                        }
                    ]
                },
                'hmctdocid': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page['id'])
                            }
                        }
                    ]
                },
                'username': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['username']
                            }
                        }
                    ]
                },
                '领域': {
                    'select': {
                        'name': page['area']
                    }
                },
                '来源': {
                    'select': {
                        'name': page['datasource']
                    }
                },
                "Tags": {
                    "multi_select": [
                        {
                            "name": "初始化"
                        }
                    ]
                },
                "阅读量": {
                    "number": page['read_num']
                },
                "评论数": {
                    "number": page['old_like_num']
                },
                "粉丝数": {
                    "number": page['follower_count']
                },
                "hmcturl": {
                    'url': page['hmcturl']
                },
                "发布时间": {
                    "date": {
                        "start": page['published_at']
                    }
                },
                "注册时间": {
                    "date": {
                        "start": page['register_at']
                    }
                },
            }
        )
        hmcttitle=page['title']
        print(f'save notion: {hmcttitle}')
        return new_page


    # 获取数据
    def query_results_by_condication(self, params: str, start_cursor=None):
        if start_cursor:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "start_cursor": start_cursor,
                    "filter": {
                        "and": [
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "contains": '初始化'
                                }
                            }
                        ]
                    }
                }
            )
        else:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "filter": {
                        "and": [
                            {
                                "property": '领域',
                                "select": {
                                    "equals": 'tiyu'
                                }
                            },
                            {
                                "property": '来源',
                                "select": {
                                    "equals": '公众号'
                                }
                            },
                            {
                                "property": '作者',
                                "rich_text": {
                                    "equals": params['author']
                                }
                            },
                           
                        ]
                    }
                }
            )
        # 获取结果和下一页的cursor
        results = response['results']
        next_cursor = response.get('next_cursor')
        return results, next_cursor

    def get_content_by_condication(self, params, start_cursor=None):
        results, next_cursor = self.query_results_by_condication(params, start_cursor=start_cursor)
        contents=[]
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                if len(page["properties"]['正文内容']['rich_text'])==0:
                    page_id = page["id"]
                    hmctdocid = page["properties"]['hmctdocid']['rich_text'][0]['plain_text']
                    content = {
                        "page_id": page_id,
                        "hmctdocid":hmctdocid
                    }
                    contents.append(content)
        return contents
        # 如果有下一页数据，则继续查询
        if next_cursor:
            content = self.get_content_by_condication(params, next_cursor)
            if content:
                return content
            

def get_exist_ids():
    # 根据ID校验是否已经保存过
    message_ids = set()
    file_path = os.path.join(os.path.dirname(__file__), 'data', f'notion-cm.json')
    if os.path.exists(file_path) is False:
        print(f'文件不存在:{file_path}')
        return message_ids
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                json_data = json.loads(line)
                message_ids.add(json_data['id'])
            f.close()
    except Exception as e:
        print(f'更新异常:{e}')
        pass
    return message_ids

def get_author_ids(key:str):
    filename = os.path.join(os.path.dirname(__file__), 'data_author',f'{key}.json')
    try:
        result_map = {}
        with open(filename, 'r', encoding='utf-8') as json_file:
            # 逐行读取文件
            for line in json_file:
                try:
                    # 去除行尾的换行符，并解析JSON对象
                    data_item = json.loads(line.rstrip('\n'))
                    bid=data_item['account']['bid']
                    register_at=data_item['account']['register_at']
                    description=data_item['account']['description']
                    username=data_item['account']['username']
                    nickname=data_item['account']['nickname']
                    follower_count=data_item['follower_count']
                    content={
                        "register_at":register_at,
                        "description":description,
                        "username":username,
                        "nickname":nickname,
                        "follower_count":follower_count
                    }
                    result_map[bid] = content
                    # 保存到Notion中
                except json.JSONDecodeError:
                    print("解析JSON对象时发生错误，跳过该行。")
                    continue
        return result_map
    except FileNotFoundError:
        print(f"文件 {filename} 不存在。")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

def save_fail_ids(file_path:str,hmctdocid:str,page_id:str,reseaon:str):
    with open(file_path, 'a') as f:
        try:
            insert_notion_data = {'page_id': page_id, 'id': hmctdocid, 'msg': reseaon}
            json.dump(insert_notion_data, f)
            f.write('\n')
            f.close()
        except Exception as e:
            print(f'保存Notion异常:{e}')
            f.close()
"""
1. 解析JSON数据,保存到Notion中
"""
def get_data():
    notion=notion_client()
    file_path = os.path.join(os.path.dirname(__file__), 'data', f'notion-cm.json')
    ids=get_exist_ids()
    data = [
        {'keji': '3512'},
        {'qinggan': '3507'},
        {'tiyu': '3524'},
        {'shehui': '3511'},
        {'yule': '3505'},
        {'lishi': '3540'},
        {'junshi': '3514'},
        {'zhichang': '3546'},
        {'sannong': '3509'},
        {'youxi': '3519'},
        {'shenghuo': '3580'},
    ]
    # 遍历数据
    for item in data:
        for key, value in item.items():
            print(f"键: {key}, 值: {value}")
            filename = os.path.join(os.path.dirname(__file__), 'data',f'{key}.json')
            author_result_map=get_author_ids(key=key)
            try:
                with open(filename, 'r', encoding='utf-8') as json_file:
                    # 逐行读取文件
                    for line in json_file:
                        try:
                            # 去除行尾的换行符，并解析JSON对象
                            data_item = json.loads(line.rstrip('\n'))
                            hmctdocid=data_item['id'] #唯一ID
                            if hmctdocid in ids:
                                continue
                            # hmcttitle=data_item['hmcttitle']#标题
                            data_item['datasource']='公众号'
                            data_item['hmcturl']=data_item['content_url']
                            data_item['area']=str(key)#领域
                            # 更新作者信息
                            account_id=data_item['account_id']
                            authorInfo=author_result_map[account_id]
                            data_item['username']=authorInfo['username']
                            data_item['register_at']=authorInfo['register_at']
                            data_item['follower_count']=authorInfo['follower_count']
                            data_item['description']=authorInfo['description']
                            new_page=notion.create_page(data_item)
                            save_fail_ids(file_path=file_path,hmctdocid=hmctdocid,page_id=new_page['id'],reseaon='success')
                            # 保存到Notion中
                        except json.JSONDecodeError:
                            print("解析JSON对象时发生错误，跳过该行。")
            except FileNotFoundError:
                print(f"文件 {filename} 不存在。")
            except Exception as e:
                print(f"读取文件时发生错误: {e}")


"""
1. 更新文章作者的数据, 注册时间等信息
"""
def update_author_info():
    '''1. 查询数据'''
    notion = notion_client()
    data = [
        # {'keji': '3512'},
        # {'qinggan': '3507'},
        # {'tiyu': '3524'},
        {'yule': '3505'},
        {'lishi': '3540'},
        {'junshi': '3514'},
        {'zhichang': '3546'},
        {'sannong': '3509'},
        {'youxi': '3519'},
        {'shenghuo': '3580'},
    ]
    # 遍历数据
    for item in data:
        for key, value in item.items():
            print(f"键: {key}, 值: {value}")
            authors=get_author_ids(key=key)
            if authors is None: continue
            for item in authors.values():
                try:
                    nickname=item['nickname']
                    params={
                        'author':nickname
                    }
                    contents=notion.get_content_by_condication(params=params,start_cursor=None)
                    for content in contents:
                        page_id=content['page_id']
                        # page_id='9f3005f1-201c-4e01-912c-96f48dd629ac'
                        properties_params={
                            "register_at":item['register_at'],
                            "description":item['description'],
                            "follower_count":item['follower_count']
                        }
                        notion.update_page_content(page_id=page_id,properties_params=properties_params)
                except Exception as e:
                    print(f'更新作者信息失败:{e}')
                    continue
            print(f'结束:{key}')


def main():
    '''1. 统一将从JSON文件解析数据存储到Notion'''
    get_data()

    # update_author_info()

   
main()