import time
from typing import List, Dict, Any

class NotionCleaner:
    def __init__(self, notion, database_id, filter_config):
        self.notion = notion
        self.database_id = database_id
        self.filter_config = filter_config

    def get_filtered_pages(self) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None
        max_retries = 3
        retry_delay = 5  # 重试等待秒数

        while True:
            try:
                filters = self.get_base_filters()
                response = self.notion.databases.query(
                    database_id=self.database_id,
                    filter={
                        "and": filters
                    },
                    sorts=[{"property": "Created time", "direction": "ascending"}],
                    start_cursor=start_cursor,
                    page_size=100
                )
                results.extend(response['results'])
                start_cursor = response.get('next_cursor')
                if not start_cursor:
                    break
            except Exception as e:
                if max_retries > 0:
                    print(f"Error querying database: {e}. Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    max_retries -= 1
                    continue
                else:
                    print(f"Max retries reached. Error: {e}")
                    break

        return results

    def get_filtered_pages_since(self, since_time: float) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None
        max_retries = 3
        retry_delay = 5

        while True:
            try:
                filters = self.get_base_filters()
                filters.append({
                    "property": "Created time",
                    "created_time": {
                        "after": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(since_time))
                    }
                })

                response = self.notion.databases.query(
                    database_id=self.database_id,
                    filter={
                        "and": filters
                    },
                    sorts=[{"property": "Created time", "direction": "ascending"}],
                    start_cursor=start_cursor,
                    page_size=100
                )
                results.extend(response['results'])
                start_cursor = response.get('next_cursor')
                if not start_cursor:
                    break
            except Exception as e:
                if max_retries > 0:
                    print(f"Error querying database: {e}. Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    max_retries -= 1
                    continue
                else:
                    print(f"Max retries reached. Error: {e}")
                    break

        return results

    def check_duplicate_and_delete(self, page: Dict[str, Any]) -> None:
        """
        检查单个页面是否重复，如果是重复数据则立即删除
        """
        try:
            id_value = self.get_property_value(page, self.filter_config["id_property"])
            if not id_value:
                return

            # 查询是否已存在相同ID的页面
            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "property": self.filter_config["id_property"],
                    "rich_text": {
                        "equals": id_value
                    }
                }
            )

            existing_pages = response.get('results', [])
            if len(existing_pages) > 1:  # 找到重复页面
                # 按创建时间排序
                sorted_pages = sorted(existing_pages, key=lambda x: x['created_time'])
                # 保留最早的页面，删除当前页面
                if page['id'] != sorted_pages[0]['id']:
                    print(f"Found duplicate for ID {id_value}. Deleting page {page['id']}")
                    self.delete_page(page['id'])
        except Exception as e:
            print(f"Error checking duplicates for page {page.get('id', 'unknown')}: {e}")

    def monitor_and_clean(self) -> None:
        print(f"Starting monitoring for new pages with configured filters")
        last_check_time = None
        error_count = 0
        max_errors = 5  # 最大连续错误次数
        retry_delay = 10  # 错误后等待时间（秒）

        while True:
            try:
                current_time = time.time()
                
                if last_check_time is None:
                    # 首次运行：获取所有页面并逐个检查
                    pages = self.get_filtered_pages()
                    print(f"First run: checking {len(pages)} pages")
                    for page in pages:
                        self.check_duplicate_and_delete(page)
                else:
                    # 后续运行：检查新页面
                    new_pages = self.get_filtered_pages_since(last_check_time)
                    if new_pages:
                        print(f"Found {len(new_pages)} new pages. Checking for duplicates...")
                        for page in new_pages:
                            self.check_duplicate_and_delete(page)
                    else:
                        print("No new pages found.")

                last_check_time = current_time
                error_count = 0  # 重置错误计数
                time.sleep(self.filter_config.get("interval", 60))

            except Exception as e:
                error_count += 1
                print(f"Error in monitoring loop: {e}")
                if error_count >= max_errors:
                    print("Too many consecutive errors. Restarting monitoring...")
                    last_check_time = None  # 重置检查时间，下次将重新检查所有页面
                    error_count = 0
                time.sleep(retry_delay) 