from request_util import GetRequestUtils
from datetime_util import get_last_monday,get_today
import json,os,time
from notion_client import Client
from datetime import datetime, timedelta


class notion_client:
    def __init__(self):
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        global_database_id = "c495d8a81ed54703b333d2404e1c625a"  # 易撰-Data
        global_notion = Client(auth=global_token)
        print('开始Notion自动化获取数据...')

    def get_yesterday(self):
        # 获取当前时间
        current_time = datetime.now()
        # 计算前一天的时间
        # yesterday = current_time - datetime.timedelta(days=2)
        yesterday = current_time - timedelta(days=2)

        formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
        print(formatted_time)
        return formatted_time

    def update_page_content(self, page_id, properties_params):
        # 更新页面的属性
        update_payload = {
            "properties": {
                "注册时间": {
                    "date": {"start": properties_params['register_at']}  # 使用 ISO 8601 格式
                },
                "粉丝数": {
                    "number":  properties_params['follower_count']  # 直接使用数字
                },
                "正文内容": {
                    "rich_text": [
                        {
                            "text": {
                                "content": properties_params['description']
                            }
                        }
                    ]
                },
                # 其他属性更新
            },
        }
        # 执行更新操作
        update_page = global_notion.pages.update(page_id=page_id, **update_payload)
        print("更新状态", properties_params)

    def create_page(self, page):
        new_page = global_notion.pages.create(
            parent={
                'database_id': global_database_id
            },
            properties={
                '标题': {
                    'title': [
                        {
                            'text': {
                                'content': page['title']
                            }
                        }
                    ]
                },
                '作者': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['account_nickname']
                            }
                        }
                    ]
                },
                '正文内容': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['description']
                            }
                        }
                    ]
                },
                'hmctdocid': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page['id'])
                            }
                        }
                    ]
                },
                'username': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['username']
                            }
                        }
                    ]
                },
                '领域': {
                    'select': {
                        'name': page['area']
                    }
                },
                '来源': {
                    'select': {
                        'name': page['datasource']
                    }
                },
                "Tags": {
                    "multi_select": [
                        {
                            "name": "初始化"
                        }
                    ]
                },
                "阅读量": {
                    "number": page['read_num']
                },
                "评论数": {
                    "number": page['old_like_num']
                },
                "粉丝数": {
                    "number": page['follower_count']
                },
                "hmcturl": {
                    'url': page['hmcturl']
                },
                "发布时间": {
                    "date": {
                        "start": page['published_at']
                    }
                },
                "注册时间": {
                    "date": {
                        "start": page['register_at']
                    }
                },
            }
        )
        hmcttitle=page['title']
        print(f'save notion: {hmcttitle}')
        return new_page


    # 获取数据
    def query_results_by_condication(self, params: str, start_cursor=None):
        if start_cursor:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "start_cursor": start_cursor,
                    "filter": {
                        "and": [
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "contains": '初始化'
                                }
                            }
                        ]
                    }
                }
            )
        else:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "filter": {
                        "and": [
                            {
                                "property": '领域',
                                "select": {
                                    "equals": 'tiyu'
                                }
                            },
                            {
                                "property": '来源',
                                "select": {
                                    "equals": '公众号'
                                }
                            },
                            {
                                "property": '作者',
                                "rich_text": {
                                    "equals": params['author']
                                }
                            },
                           
                        ]
                    }
                }
            )
        # 获取结果和下一页的cursor
        results = response['results']
        next_cursor = response.get('next_cursor')
        return results, next_cursor

    def get_content_by_condication(self, params, start_cursor=None):
        results, next_cursor = self.query_results_by_condication(params, start_cursor=start_cursor)
        contents=[]
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                if len(page["properties"]['正文内容']['rich_text'])==0:
                    page_id = page["id"]
                    hmctdocid = page["properties"]['hmctdocid']['rich_text'][0]['plain_text']
                    content = {
                        "page_id": page_id,
                        "hmctdocid":hmctdocid
                    }
                    contents.append(content)
        return contents
        # 如果有下一页数据，则继续查询
        if next_cursor:
            content = self.get_content_by_condication(params, next_cursor)
            if content:
                return content
            



def get_exist_ids():
    # 根据ID校验是否已经保存过
    message_ids = set()
    file_path = os.path.join(os.path.dirname(__file__), 'data', f'notion-cm.json')
    if os.path.exists(file_path) is False:
        print(f'文件不存在:{file_path}')
        return message_ids
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                json_data = json.loads(line)
                message_ids.add(json_data['id'])
            f.close()
    except Exception as e:
        print(f'更新异常:{e}')
        pass
    return message_ids
def save_fail_ids(file_path:str,hmctdocid:str,page_id:str,reseaon:str):
    with open(file_path, 'a') as f:
        try:
            insert_notion_data = {'page_id': page_id, 'id': hmctdocid, 'msg': reseaon}
            json.dump(insert_notion_data, f)
            f.write('\n')
            f.close()
        except Exception as e:
            print(f'保存Notion异常:{e}')
            f.close()

def get_author_ids(key:str):
    filename = os.path.join(os.path.dirname(__file__), 'data_author',f'{key}.json')
    try:
        result_map = {}
        with open(filename, 'r', encoding='utf-8') as json_file:
            # 逐行读取文件
            for line in json_file:
                try:
                    # 去除行尾的换行符，并解析JSON对象
                    data_item = json.loads(line.rstrip('\n'))
                    bid=data_item['account']['bid']
                    register_at=data_item['account']['register_at']
                    description=data_item['account']['description']
                    username=data_item['account']['username']
                    nickname=data_item['account']['nickname']
                    follower_count=data_item['follower_count']
                    content={
                        "register_at":register_at,
                        "description":description,
                        "username":username,
                        "nickname":nickname,
                        "follower_count":follower_count
                    }
                    result_map[bid] = content
                    # 保存到Notion中
                except json.JSONDecodeError:
                    print("解析JSON对象时发生错误，跳过该行。")
                    continue
        return result_map
    except FileNotFoundError:
        print(f"文件 {filename} 不存在。")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

# 爆文数据保存
def save_data(key:str,value:str,Cookie:str):
    ids=get_exist_ids()
    # Cookie='Hm_lvt_10af5e9c5efe07e21c76a574dad9cede=**********; remember_token=31ba48380b6a43a2b564122ad4dcaab8|62f1b8e609bdfa1c08feed16c2d7d902709950be59faed4beeadb7a0b4edaf9788fb6bc819efe971e6422ab4dbab2ee3bca537f365cccf4be41e0cd77315cf8a; session=.eJwdj8FqAzEMRP_F51K0kizL-ZlFsiVaShLYTU6h_16n13nD8OZV9jzi_CqXx_GMj7J_z3IpIGbQbKBNAsyahl69yhY6MieZtg4tMkER1XuYd8GmwpY-64ghsQ2FUXPMyVtvRCRMK-6ONGlyQ3KeU4aBKDoP49oTgBXUyhI54hpXj2M_Y9xv8ywXeUP4hAWf5wL_qrS5sZKCizG9PYU3XOI8h5nrmhrnkfvj_hO31W-dVz-FNuZwQoHaKrWlhusfZlSJSKXy-wfe11El.ZnN9SA.9HZM1KpHKk1pYslQ8sy7qC9wYds; Hm_lpvt_10af5e9c5efe07e21c76a574dad9cede=1718844745'
    # url = "https://www.cimidata.com/api/articles/hit/9?page=1&limit=20"
    total_pages=3
    for page in range(1,total_pages):
        # 低粉爆文
        url = f'https://www.cimidata.com/api/articles/hit/{value}'
        # 热门文章
        # url = f'https://www.cimidata.com/api/articles/hot/{value}'
        params = {"page": page,"limit":20}  # Example parameters
        headers = {"User-Agent": "Mozilla/5.0"}
        headers = {
            "User-Agent": "Mozilla/5.0",
            'Cookie':Cookie
        }
        timeout = 50
        time.sleep(6)
        json_response = GetRequestUtils.get_json(url, params, headers, timeout)
        if json_response:
            if json_response['code'] == 200:
                # total_records=json_response['total']
                # total_pages = (total_records + 20 - 1) // 20 #计算分页数
                try:
                    data_list=json_response['data']
                    filename = os.path.join(os.path.dirname(__file__), 'data',f'{key}.json')
                    with open(filename, 'a', encoding='utf-8') as json_file:
                        for item in data_list:
                            id=item['id']
                            if id in ids:
                                continue
                            read_num=item['read_num']
                            # 将数据项转换为JSON字符串，并在末尾添加换行符
                            json_string = json.dumps(item, ensure_ascii=False) + '\n'
                            # 将JSON字符串写入文件
                            json_file.write(json_string)
                    print(f"数据已追加到 {filename} 文件中。")
                except Exception as e:
                    print(f"写入文件时发生错误: {e}")
            else:
                print(f"Error: {json_response['msg']}")
# 公众号作者数据
def save_data_author(key:str,value:str,Cookie:str):
    total_pages=10
    week=get_last_monday()
    for page in range(1,total_pages):
        # 综合
        url = f'https://www.cimidata.com/api/rank'
        #week 上周一 每周一, kind: 1-综合 2-原创 3-赞赏 4-评论 
        params = {"page": page,"limit":20,"cid":value,"week":week,"kind":1}  # Example parameters
        headers = {"User-Agent": "Mozilla/5.0"}
        headers = {
            "User-Agent": "Mozilla/5.0",
            'Cookie':Cookie
        }
        timeout = 50
        time.sleep(6)
        json_response = GetRequestUtils.get_json(url, params, headers, timeout)
        if json_response:
            if json_response['code'] == 200:
                total_records=json_response['total']
                total_pages = (total_records + 20 - 1) // 20 #计算分页数
                try:
                    data_list=json_response['data']
                    filename = os.path.join(os.path.dirname(__file__), 'data_author',f'{key}.json')
                    with open(filename, 'a', encoding='utf-8') as json_file:
                        for item in data_list:
                            # id=item['id']
                            # read_num=item['read_num']
                            # 将数据项转换为JSON字符串，并在末尾添加换行符
                            json_string = json.dumps(item, ensure_ascii=False) + '\n'
                            # 将JSON字符串写入文件
                            json_file.write(json_string)
                    print(f"数据已追加到 {filename} 文件中。")
                except Exception as e:
                    print(f"写入文件时发生错误: {e}")
            else:
                print(f"Error: {json_response['msg']}")
        else:
            break


# 公众号作者历史文章数据
def save_data_author_article(author:dict,bid:str,Cookie:str):
    total_pages=5
    today=get_today()
    for page in range(1,total_pages):
        # 综合
        url = f'https://www.cimidata.com/api/stats/articles'
        params = {"page": page,"page_size":20,"bid":bid,"end_at":today}  # Example parameters
        headers = {"User-Agent": "Mozilla/5.0"}
        headers = {
            "User-Agent": "Mozilla/5.0",
            'Cookie':Cookie
        }
        timeout = 50
        time.sleep(6)
        json_response = GetRequestUtils.get_json(url, params, headers, timeout)
        if json_response:
            if json_response['code'] == 200:
                datas=json_response['data']
                total_records=datas['total']
                total_pages = (total_records + 20 - 1) // 20 #计算分页数
                try:
                    data_list=datas['articles']
                    filename = os.path.join(os.path.dirname(__file__), 'data_article',f'article.json')
                    with open(filename, 'a', encoding='utf-8') as json_file:
                        for item in data_list:
                            # id=item['id']
                            # read_num=item['read_num']
                            # 将数据项转换为JSON字符串，并在末尾添加换行符
                            item['register_at']=author['register_at']
                            item['description']=author['description']
                            item['username']=author['username']
                            item['nickname']=author['nickname']
                            item['follower_count']=author['follower_count']

                            json_string = json.dumps(item, ensure_ascii=False) + '\n'
                            # 将JSON字符串写入文件
                            json_file.write(json_string)
                    print(f"数据已追加到 {filename} 文件中。")
                except Exception as e:
                    print(f"写入文件时发生错误: {e}")
            else:
                print(f"Error: {json_response['msg']}")
        else:
            break


def save_data_author_article_to_notion(key:str,author:dict,bid:str,Cookie:str):
    ids=get_exist_ids()
    file_path = os.path.join(os.path.dirname(__file__), 'data', f'notion-cm.json')
    notion=notion_client()

    total_pages=5
    today=get_today()
    for page in range(1,total_pages):
        # 综合
        url = f'https://www.cimidata.com/api/stats/articles'
        params = {"page": page,"page_size":20,"bid":bid,"end_at":today}  # Example parameters
        headers = {"User-Agent": "Mozilla/5.0"}
        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
            'Cookie':Cookie
        }
        timeout = 50
        time.sleep(3)
        json_response = GetRequestUtils.get_json(url, params, headers, timeout)
        if json_response:
            if json_response['code'] == 200:
                datas=json_response['data']
                total_records=datas['total']
                total_pages = (total_records + 20 - 1) // 20 #计算分页数
                try:
                    data_list=datas['articles']
                    filename = os.path.join(os.path.dirname(__file__), 'data_article',f'article.json')
                    for data_item in data_list:
                        hmctdocid=data_item['id'] #唯一ID
                        if hmctdocid in ids:
                            continue
                        
                        # 将数据项转换为JSON字符串，并在末尾添加换行符
                        data_item['register_at']=author['register_at']
                        data_item['description']=author['description']
                        data_item['username']=author['username']
                        data_item['nickname']=author['nickname']
                        data_item['follower_count']=author['follower_count']


                        # 存储Notion
                        # hmcttitle=data_item['hmcttitle']#标题
                        data_item['datasource']='公众号'
                        data_item['hmcturl']=data_item['content_url']
                        data_item['area']=str(key)#领域
                        data_item['account_nickname']=data_item['nickname']
                        new_page=notion.create_page(data_item)
                        save_fail_ids(file_path=file_path,hmctdocid=hmctdocid,page_id=new_page['id'],reseaon='success')

                except Exception as e:
                    print(f"写入文件时发生错误: {e}")
            else:
                print(f"Error: {json_response['msg']}")
        else:
            break


def get_data_by_author(key:str,Cookie:str):
    author_bids=[
        # 体育
        'VyOzkje9',# 杨毅侃球
        'KQ1EW8QJ', #篮球教学论坛 
        '4Qk4Jv3a',# 苏群
        # 头像
        'KQ1MyP8Q',# 茶茶头像
        'NjY9xo0j', #南风头像阁   批量生成图片, 分享发布, 程序自动化,保证15M以内, 单位小时内发布. 
        'Rjlr78w3', #头像主   认证公司
        # 文案
        'N3N70ApO', #悦尔句子
        '4QkW5X5Q', #文案纸条
        'N3W9PgRj', #拾句书生
        # 情感
        'N3W9d5Lj', #伴句诗
        'N3W9dEej', #醒姐姐 
        'KQ2yMDjL', #遇见柚柚
        #科技
        'l3wMXKbj', #硅星人Pro 
        '9aj6dN34', #量子位
    ]
    for author_bid in author_bids:
        authors=get_author_ids(key=key)
        if authors is None: continue
        if authors.get(author_bid) is None: continue
        
        author=authors[author_bid]
        save_data_author_article_to_notion(key=key,author=author,bid=author_bid,Cookie=Cookie) #作者的历史文章



'''1. 数据采集存储JSON文件'''
def main():
    data = [
        {'bizhitouxiang': '50'},
        {'wenan': '53'},
        {'tiyu': '4'},
        {'keji': '2'},
        {'qinggan': '9'},
        {'chuangye': '47'},
        {'yule': '8'},
        {'zhichang': '18'},
        {'sannong': '33'},
        {'yingshi': '23'},
        {'junshi': '11'},
        {'wenzhai': '24'},
        {'shenghuo': '28'},
        {'lishi': '10'},
        {'wenhua': '13'},
        {'zixunredian': '20'},
        {'minsheng': '52'},
        {'jiaoyu': '21'},
        {'youxi': '14'},
        {'kexue': '42'},
        {'qiche': '3'},
        {'falv': '51'},
    ]
    # 遍历数据
    Cookie='Hm_lvt_10af5e9c5efe07e21c76a574dad9cede=**********; gr_user_id=cfbe1228-cdc6-4e4c-b2ac-5c15b2e2346c; _ga_6SMN3XPXDW=GS1.1.1719215140.1.1.1719215494.55.0.0; _ga=GA1.1.1499637713.1719215140; remember_token=1e146ab50a0d4d98af497f5054aec6d8|1c5aa025772c88675ca7fcc9d6e3272414cdc08a6ded61e4ac5b922020bdd8dc16bb8c06aa69e989d5cdc46183c058d1ad86aa33dde432b23344f4d8692e5180; session=.eJwdjkFqBDEMBP_icwiSJVn2fmaQZYmEsLswszmF_D0mx-5qmvopR55xfZTb6_yOt3J8rnIrPV0p0dYIJeJuoiGMcw33AbPnoGpLdCckzYnae4amBgSKtEDUKuasA2KYe1Zf0wloaTCMiKDA3O1gbEvn0mZtuEIaNa9li5xxj_uM87jCn491lVsD7gDvsOH3tcG_KgZysylgsHiNbslDU0DYwtvq-8qvM4_X8ysee6-DjXs2QuaYVBuICil5q9KwZmz7yE7l9w__vFGk.Znp9pQ.ZVgjc2l00FD0Oqg4sf8p5up2t08; Hm_lpvt_10af5e9c5efe07e21c76a574dad9cede=1719303989'
    for item in data:
        for key, value in item.items():
            print(f"键: {key}, 值: {value}")

            # 独立: 获取文章列表(低粉和热门)
            save_data(key=key,value=value,Cookie=Cookie)#公众号文章榜单

            # 独立: 获取作者信息(公众号榜单)
            # save_data_author(key=key,value=value,Cookie=Cookie) #公众号榜单作者

            # 独立: 指定作者文章获取
            # get_data_by_author(key=key,Cookie=Cookie)

            # 独立: 获取作者的文章-所有(遍历作者)
            # authors=get_author_ids(key=key)
            # if authors is None: continue
            # for item in authors.keys():
            #     author=authors[item]
            #     save_data_author_article(author=author,bid=item,Cookie=Cookie) #作者的历史文章
            
if __name__ == '__main__':
    main()