from notion_client import Client
from typing import List, Dict, Any
from collections import defaultdict
import time


class NotionClient:
    def __init__(self, token: str, database_id: str):
        self.notion = Client(auth=token)
        self.database_id = database_id
        print('Notion client initialized.')

    """
    获取所有页面
    """

    def get_all_pages(self) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            response = self.notion.databases.query(
                database_id=self.database_id,
                start_cursor=start_cursor,
                page_size=100,  # Maximum page size
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    """
    删除页面内容
    """

    def delete_page(self, page_id: str) -> None:
        try:
            self.notion.pages.update(page_id=page_id, archived=True)
            print(f"Page {page_id} has been archived.")
        except Exception as e:
            print(f"Error archiving page {page_id}: {e}")

    """
    删除重复的页面-保留最新的页面
    """

    def remove_duplicates(self, pages: List[Dict[str, Any]], id_property: str) -> None:
        id_to_pages = defaultdict(list)

        for page in pages:
            page_id = page['id']
            id_value = self.get_property_value(page, id_property)
            if id_value:
                id_to_pages[id_value].append((page_id, page['created_time']))

        for id_value, page_info_list in id_to_pages.items():
            if len(page_info_list) > 1:
                # Sort by created_time in descending order and keep the most recent
                sorted_pages = sorted(page_info_list, key=lambda x: x[1], reverse=True)
                for page_id, _ in sorted_pages[1:]:
                    self.delete_page(page_id)

    def get_property_value(self, page: Dict[str, Any], property_name: str) -> str:
        property_value = page['properties'].get(property_name)
        if property_value:
            if property_value['type'] == 'rich_text':
                rich_text = property_value['rich_text']
                if rich_text:
                    return rich_text[0]['plain_text']
            elif property_value['type'] == 'select':
                select = property_value['select']
                if select:
                    return select['name']
        return None

    def get_filtered_pages(self, filter_property: str, filter_value: str, quantity_property: str, quantity_threshold: int) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "and": [
                        {
                            "property": filter_property,
                            "select": {
                                "equals": filter_value
                            }
                        },
                        {
                            "property": quantity_property,
                            "number": {
                                "greater_than": quantity_threshold
                            }
                        }
                    ]
                },
                sorts=[{"property": "Created time", "direction": "ascending"}],
                start_cursor=start_cursor,
                page_size=100
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    def get_filtered_pages_since(self, filter_property: str, filter_value: str, quantity_property: str, quantity_threshold: int, since_time: float) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "and": [
                        {
                            "property": filter_property,
                            "select": {
                                "equals": filter_value
                            }
                        },
                        {
                            "property": quantity_property,
                            "number": {
                                "greater_than": quantity_threshold
                            }
                        },
                        {
                            "property": "created_time",
                            "created_time": {
                                "after": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(since_time))
                            }
                        }
                    ]
                },
                sorts=[{"property": "created_time", "direction": "ascending"}],
                start_cursor=start_cursor,
                page_size=100
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    def remove_duplicates_keep_oldest(self, pages: List[Dict[str, Any]], id_property: str) -> None:
        id_to_pages = defaultdict(list)

        for page in pages:
            page_id = page['id']
            id_value = self.get_property_value(page, id_property)
            created_time = self.get_property_value(page, 'created_time')
            if id_value and created_time:
                id_to_pages[id_value].append((page_id, created_time))

        for id_value, page_info_list in id_to_pages.items():
            if len(page_info_list) > 1:
                # Sort by created_time in ascending order and keep the oldest
                sorted_pages = sorted(page_info_list, key=lambda x: x[1])
                for page_id, _ in sorted_pages[1:]:
                    self.delete_page(page_id)

    def monitor_and_clean(self, filter_property: str, filter_value: str, quantity_property: str, quantity_threshold: int, id_property: str, interval: int = 60):
        print(f"Starting monitoring for new pages with {filter_property} = {filter_value} and {quantity_property} > {quantity_threshold}")
        last_check_time = None

        while True:
            current_time = time.time()
            
            if last_check_time is None:
                # First run: get all pages and remove duplicates
                pages = self.get_filtered_pages(filter_property, filter_value, quantity_property, quantity_threshold)
                self.remove_duplicates(pages, id_property)
            else:
                # Subsequent runs: check for new pages since last check
                new_pages = self.get_filtered_pages_since(filter_property, filter_value, quantity_property, quantity_threshold, last_check_time)
                if new_pages:
                    print(f"Found {len(new_pages)} new pages. Checking for duplicates...")
                    self.remove_duplicates(new_pages, id_property)
                else:
                    print("No new pages found.")

            last_check_time = current_time
            time.sleep(interval)

    def remove_duplicates_keep_oldest(self, pages: List[Dict[str, Any]], id_property: str) -> None:
        id_to_pages = defaultdict(list)

        for page in pages:
            page_id = page['id']
            id_value = self.get_property_value(page, id_property)
            created_time = self.get_property_value(page, 'created_time')
            if id_value and created_time:
                id_to_pages[id_value].append((page_id, created_time))

        for id_value, page_info_list in id_to_pages.items():
            if len(page_info_list) > 1:
                # Sort by created_time in ascending order and keep the oldest
                sorted_pages = sorted(page_info_list, key=lambda x: x[1])
                for page_id, _ in sorted_pages[1:]:
                    self.delete_page(page_id)

    def monitor_and_clean(self, filter_property: str, filter_value: str, quantity_property: str, quantity_threshold: int, id_property: str, interval: int = 60):
        print(f"Starting monitoring for new pages with {filter_property} = {filter_value} and {quantity_property} > {quantity_threshold}")
        last_check_time = None

        while True:
            current_time = time.time()
            
            if last_check_time is None:
                # First run: get all pages and remove duplicates
                pages = self.get_filtered_pages(filter_property, filter_value, quantity_property, quantity_threshold)
                self.remove_duplicates(pages, id_property)
            else:
                # Subsequent runs: check for new pages since last check
                new_pages = self.get_filtered_pages_since(filter_property, filter_value, quantity_property, quantity_threshold, last_check_time)
                if new_pages:
                    print(f"Found {len(new_pages)} new pages. Checking for duplicates...")
                    self.remove_duplicates(new_pages, id_property)
                else:
                    print("No new pages found.")

            last_check_time = current_time
            time.sleep(interval)

    def get_filtered_pages_since(self, filter_property: str, filter_value: str, quantity_property: str, quantity_threshold: int, since_time: float) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "and": [
                        {
                            "property": filter_property,
                            "select": {
                                "equals": filter_value
                            }
                        },
                        {
                            "property": quantity_property,
                            "number": {
                                "greater_than": quantity_threshold
                            }
                        },
                        {
                            "property": "Created time",
                            "created_time": {
                                "after": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(since_time))
                            }
                        }
                    ]
                },
                sorts=[{"property": "Created time", "direction": "ascending"}],
                start_cursor=start_cursor,
                page_size=100
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results


def main():
    """
      1. 清理冗余重复数据 头条数据  根据唯一ID处理
      """
    token = "**************************************************"
    database_id = 'c495d8a81ed54703b333d2404e1c625a'  # yizhuang-data
    filter_property = "领域"  # 替换为您要筛选的属性名
    filter_value = "tiyu"  # 替换为您要筛选的属性值
    quantity_property = "阅读量"  # 替换为您的数量属性名
    quantity_threshold = 10000  # 设置数量阈值
    id_property = "hmctdocid"  # 用于判断重复的属性名

    notion_client = NotionClient(token=token, database_id=database_id)
    
    # 开始监控和清理过程
    notion_client.monitor_and_clean(filter_property, filter_value, quantity_property, quantity_threshold, id_property)

if __name__ == '__main__':
    main()
