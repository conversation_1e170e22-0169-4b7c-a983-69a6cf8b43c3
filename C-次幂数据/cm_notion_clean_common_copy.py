from notion_client import Client
from typing import List, Dict, Any
from collections import defaultdict
import time
from datetime import datetime, timedelta


class NotionClient:
    def __init__(self, token: str, database_id: str, filter_config: Dict[str, Any]):
        self.notion = Client(auth=token)
        self.database_id = database_id
        self.filter_config = filter_config
        print('Notion client initialized.')

    def get_one_month_ago(self) -> str:
        one_month_ago = datetime.now() - timedelta(days=30)
        return one_month_ago.strftime("%Y-%m-%dT%H:%M:%SZ")

    def get_base_filters(self) -> List[Dict[str, Any]]:
        one_month_ago_str = self.get_one_month_ago()
        return [
            {
                "property": self.filter_config["filter_property"],
                "select": {
                    "equals": self.filter_config["filter_value"]
                }
            },
            {
                "property": self.filter_config["quantity_property"],
                "number": {
                    "greater_than": self.filter_config["quantity_threshold"]
                }
            },
            {
                "property": "来源",
                "select": {
                    "equals": self.filter_config["source"]
                }
            },
            {
                "property": "发布时间",
                "date": {
                    "after": one_month_ago_str
                }
            }
        ]

    def get_filtered_pages(self) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            filters = self.get_base_filters()
            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "and": filters
                },
                sorts=[{"property": "Created time", "direction": "ascending"}],
                start_cursor=start_cursor,
                page_size=100
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    def get_filtered_pages_since(self, since_time: float) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            filters = self.get_base_filters()
            filters.append({
                "property": "Created time",
                "created_time": {
                    "after": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(since_time))
                }
            })

            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "and": filters
                },
                sorts=[{"property": "Created time", "direction": "ascending"}],
                start_cursor=start_cursor,
                page_size=100
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    """
    获取所有页面
    """

    def get_all_pages(self) -> List[Dict[str, Any]]:
        results = []
        start_cursor = None

        while True:
            response = self.notion.databases.query(
                database_id=self.database_id,
                start_cursor=start_cursor,
                page_size=100,  # Maximum page size
            )
            results.extend(response['results'])
            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    """
    删除页面内容
    """

    def delete_page(self, page_id: str) -> None:
        try:
            self.notion.pages.update(page_id=page_id, archived=True)
            print(f"Page {page_id} has been archived.")
        except Exception as e:
            print(f"Error archiving page {page_id}: {e}")

    """
    删除重复的页面-保留最新的页面
    """

    def remove_duplicates(self, pages: List[Dict[str, Any]], id_property: str) -> None:
        id_to_pages = defaultdict(list)

        for page in pages:
            page_id = page['id']
            id_value = self.get_property_value(page, id_property)
            if id_value:
                id_to_pages[id_value].append((page_id, page['created_time']))

        for id_value, page_info_list in id_to_pages.items():
            if len(page_info_list) > 1:
                # Sort by created_time in descending order and keep the most recent
                sorted_pages = sorted(page_info_list, key=lambda x: x[1], reverse=True)
                for page_id, _ in sorted_pages[1:]:
                    self.delete_page(page_id)

    def get_property_value(self, page: Dict[str, Any], property_name: str) -> str:
        property_value = page['properties'].get(property_name)
        if property_value:
            if property_value['type'] == 'rich_text':
                rich_text = property_value['rich_text']
                if rich_text:
                    return rich_text[0]['plain_text']
            elif property_value['type'] == 'select':
                select = property_value['select']
                if select:
                    return select['name']
        return None

    def remove_duplicates_keep_oldest(self, pages: List[Dict[str, Any]], id_property: str) -> None:
        id_to_pages = defaultdict(list)

        for page in pages:
            page_id = page['id']
            id_value = self.get_property_value(page, id_property)
            created_time = self.get_property_value(page, 'created_time')
            if id_value and created_time:
                id_to_pages[id_value].append((page_id, created_time))

        for id_value, page_info_list in id_to_pages.items():
            if len(page_info_list) > 1:
                # Sort by created_time in ascending order and keep the oldest
                sorted_pages = sorted(page_info_list, key=lambda x: x[1])
                for page_id, _ in sorted_pages[1:]:
                    self.delete_page(page_id)

    def check_duplicate_and_delete(self, page: Dict[str, Any]) -> None:
        """
        检查单个页面是否重复，如果是重复数据则立即删除
        """
        id_value = self.get_property_value(page, self.filter_config["id_property"])
        if not id_value:
            return

        # 查询是否已存在相同ID的页面
        response = self.notion.databases.query(
            database_id=self.database_id,
            filter={
                "property": self.filter_config["id_property"],
                "rich_text": {
                    "equals": id_value
                }
            }
        )

        existing_pages = response.get('results', [])
        if len(existing_pages) > 1:  # 找到重复页面
            # 按创建时间排序
            sorted_pages = sorted(existing_pages, key=lambda x: x['created_time'])
            # 保留最早的页面，删除当前页面
            if page['id'] != sorted_pages[0]['id']:
                print(f"Found duplicate for ID {id_value}. Deleting page {page['id']}")
                self.delete_page(page['id'])

    def monitor_and_clean(self) -> None:
        print(f"Starting monitoring for new pages with configured filters")
        last_check_time = None

        while True:
            current_time = time.time()
            
            if last_check_time is None:
                # 首次运行：获取所有页面并逐个检查
                pages = self.get_filtered_pages()
                print(f"First run: checking {len(pages)} pages")
                for page in pages:
                    self.check_duplicate_and_delete(page)
            else:
                # 后续运行：检查新页面
                new_pages = self.get_filtered_pages_since(last_check_time)
                if new_pages:
                    print(f"Found {len(new_pages)} new pages. Checking for duplicates...")
                    for page in new_pages:
                        self.check_duplicate_and_delete(page)
                else:
                    print("No new pages found.")

            last_check_time = current_time
            # time.sleep(self.filter_config.get("interval", 60))


def main():
    """
    1. 清理冗余重复数据 头条数据  根据唯一ID处理
    """
    # 全局配置
    filter_config = {
        "token": "**************************************************",
        "database_id": 'c495d8a81ed54703b333d2404e1c625a',  # yizhuang-data
        "filter_property": "领域",
        "filter_value": "tiyu",
        "quantity_property": "阅读量",
        "quantity_threshold": 10000,
        "id_property": "hmctdocid",
        "source": "公众号",
        "interval": 60  # 监控间隔（秒）
    }

    notion_client = NotionClient(
        token=filter_config["token"],
        database_id=filter_config["database_id"],
        filter_config=filter_config
    )
    
    # 开始监控和清理过程
    notion_client.monitor_and_clean()


if __name__ == '__main__':
    main()
