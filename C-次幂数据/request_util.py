import requests

class GetRequestUtils:
    @staticmethod
    def get(url, params=None, headers=None, timeout=None):
        """
        Send a GET request to the specified URL.

        :param url: The URL to send the request to.
        :param params: (optional) Dictionary of URL parameters to append to the URL.
        :param headers: (optional) Dictionary of HTTP headers to send with the request.
        :param timeout: (optional) How many seconds to wait for the server to send data before giving up.
        :return: Response object
        """
        try:
            response = requests.get(url, params=params, headers=headers, timeout=timeout,verify=False)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return None

    @staticmethod
    def get_json(url, params=None, headers=None, timeout=None):
        """
        Send a GET request to the specified URL and return the JSON response.

        :param url: The URL to send the request to.
        :param params: (optional) Dictionary of URL parameters to append to the URL.
        :param headers: (optional) Dictionary of HTTP headers to send with the request.
        :param timeout: (optional) How many seconds to wait for the server to send data before giving up.
        :return: JSON response or None if an error occurred
        """
        response = GetRequestUtils.get(url, params, headers, timeout)
        if response:
            return response.json()
        return None


import requests
from typing import Dict, Union

class PostRequestTool:
    @staticmethod
    def post(url: str, data: Dict = None, json: Dict = None, headers: Dict = None) -> Union[Dict, str]:
        """
        Send a POST request to the specified URL.

        :param url: The URL to send the POST request to.
        :param data: Dictionary, bytes, or file-like object to send in the body of the request.
        :param json: JSON data to send in the body of the request.
        :param headers: Dictionary of HTTP Headers to send with the request.
        :return: Dictionary containing the JSON response or the raw text response if JSON decoding fails.
        """
        try:
            response = requests.post(url, data=data, json=json, headers=headers)
            response.raise_for_status()  # Raise an HTTPError for bad responses (4xx and 5xx)
            try:
                return response.json()
            except ValueError:
                return response.text
        except requests.exceptions.RequestException as e:
            return str(e)

# Example usage:
# if __name__ == "__main__":
#     url = "https://api3.toolnb.com/tools/getWxArticleImages.json"
#     data = {"url": "http://mp.weixin.qq.com/s?__biz=MzkyNjY4NTkwOQ==&mid=2247485162&idx=1&sn=9b6150b6658ccf87e2493847402e3672&chksm=c3b514febdc6724eb72477b4d158f05677138ec251b4839cce6b8aba10f183c6b8bca8cfb86e&scene=126&sessionid=0#rd"}
#     # headers = {"Content-Type": "application/json","Cookie":"Hm_lvt_0000a307caa05f3ed0b0941b8609002d=1718981730; _ga=GA1.1.31039878.1718981731; Hm_lpvt_0000a307caa05f3ed0b0941b8609002d=1718981806; _ga_WV8ZJFXX9G=GS1.1.1718981730.1.1.1718981806.0.0.0"}
#     headers = {
#         'Content-Type': 'application/x-www-form-urlencoded',
#         'User-Agent':'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36'
#         # 根据需要添加其他头部信息
#     }
    
#     res = PostRequestTool.post(url, data=data, headers=headers)
#     if res['code']==1:
#         data=res['data']
#         url=data['url']
#         print(url)



# # Example usage:
# if __name__ == "__main__":
    # Cookie='Hm_lvt_10af5e9c5efe07e21c76a574dad9cede=1718519647; remember_token=c64068a4540f490691dd9940350b251b|0e04469eea7ac6bc9edee92839b3872a75c9bb4d0f81c9fb0322086a1087a8528011008a8c579daf1691d73c10fc8b08a7e719afd0f8b004170c533bd600eb26; session=.eJwdj0tuwzAMRO-idVHQJEVJvowh_tCiSALYyaro3at2yzczePwuR55xfZT9eb7irRyfXvYCMie0aTidALPmRK1aZYtumU6ztwEtMqEjdh0xdQi2LjxTvVqYxGYdrKa58zYaEQnTOg9FcnJuSMruYhOko7JNriMBuEOfZYmccYubxnlcYY-7X2WXPwjvsODrWuBf1YTXwCozJA-QsbmPwUAVFOuma8quM4_n4yvuK98GT-4ptDGHEgrUVqktNVz_YUaViOxUfn4BmlhQIA.ZnJ5yA.TyuTbS3a6XAay2cGuf8BIayJd9s; Hm_lpvt_10af5e9c5efe07e21c76a574dad9cede=1718778319'
    # url = "https://www.cimidata.com/api/articles/hit/9?page=1&limit=20"
    # params = {"page": "1","limit":20}  # Example parameters
    # headers = {"User-Agent": "Mozilla/5.0"}
    # headers = {
    #     "User-Agent": "Mozilla/5.0",
    #     'Cookie':Cookie
    # }
    # timeout = 50

    # response = GetRequestUtils.get(url, params, headers, timeout)
    # if response:
    #     print(f"Response status code: {response.status_code}")
    #     print(f"Response content: {response.text}")

    # json_response = GetRequestUtils.get_json(url, params, headers, timeout)
    # if json_response:
    #     print(f"JSON response: {json_response}")