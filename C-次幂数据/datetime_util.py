from datetime import datetime, timedelta

def get_last_monday():
    """
    获取上周一的日期
    """
    # 获取当前日期
    today = datetime.now()

    # 计算今天是星期几（星期一为0，星期日为6）
    weekday = today.weekday()

    # 计算上周一的日期
    # 如果今天是星期一，直接减去7天
    # 否则，减去今天是一周中的第几天，再加1天（因为星期一是一周的第一天）
    if weekday == 0:
        last_monday = today - timedelta(days=7)
    else:
        days=weekday+7 #获取上周一,不是本周一
        last_monday = today - timedelta(days=days)

    # 格式化日期为'YYYYMMDD'
    formatted_date = last_monday.strftime('%Y%m%d')

    print("上周一的日期是:", formatted_date)
    return formatted_date

def get_today():
    # 获取当前日期-格式化成"YYYY-MM-DD"
    now = datetime.now()
    formatted_date = now.strftime("%Y-%m-%d")
    print("当前日期:", formatted_date)
    return formatted_date
