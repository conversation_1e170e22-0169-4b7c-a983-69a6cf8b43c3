import requests
from bs4 import BeautifulSoup
import time
import json
import random
import os
import cloudscraper

class Spider91Porny:
    def __init__(self, start_page=1, end_page=5, delay=2):
        self.base_url = "https://91porny.com/video/category/latest/"
        self.start_page = start_page
        self.end_page = end_page
        self.delay = delay
        
        # 创建一个cloudscraper实例来自动处理Cloudflare保护
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'darwin',
                'desktop': True
            },
            delay=10
        )
        
        # 替换UserAgent部分
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 Chrome/96.0.4664.110 Safari/537.36"
        ]
        
        # 基础请求头
        self.headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
            "DNT": "1",
            "Priority": "u=1, i",
            "Sec-CH-UA": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"",
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": "\"macOS\"",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Connection": "keep-alive"
        }
        
        self.all_videos = []
        self.result_file = "91porny_videos.json"
    
    def get_page(self, page):
        """获取指定页面的HTML内容"""
        url = f"{self.base_url}{page}"
        try:
            # 随机User-Agent
            current_headers = self.headers.copy()
            current_headers["User-Agent"] = random.choice(self.user_agents)
            
            # 正确的Referer
            if page > 1:
                current_headers["Referer"] = f"https://91porny.com/video/category/latest/{page-1}"
            else:
                current_headers["Referer"] = "https://91porny.com/"
            
            # 先访问首页获取新Cookie
            if page == self.start_page:
                print("正在访问首页获取Cookie...")
                self.scraper.get("https://91porny.com/", headers=current_headers, timeout=15)
                time.sleep(5)  # 等待5秒
            
            # 使用cloudscraper处理Cloudflare保护
            response = self.scraper.get(url, headers=current_headers, timeout=15)
            
            # 检查HTTP状态码
            if response.status_code == 403:
                print(f"获取第{page}页时返回403，可能是被网站反爬机制拦截")
                print(f"当前使用的Headers: {current_headers}")
                return None
                
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"获取第{page}页时出错: {e}")
            return None
    
    def parse_page(self, html):
        """解析页面HTML，提取视频信息"""
        if not html:
            return []
        
        soup = BeautifulSoup(html, "html.parser")
        video_elems = soup.select("div.video-elem")
        page_videos = []
        
        for elem in video_elems:
            try:
                # 视频链接和预览图
                video_link_elem = elem.select_one("a.display")
                video_link = "https://91porny.com" + video_link_elem["href"] if video_link_elem else None
                
                # 预览图片
                img_elem = video_link_elem.select_one("div.img") if video_link_elem else None
                img_url = None
                if img_elem and "background-image" in img_elem.get("style", ""):
                    style = img_elem["style"]
                    # 提取URL('xxx')中的xxx部分
                    img_url = style.split("url('")[1].split("')")[0]
                    if img_url.startswith("//"):
                        img_url = "https:" + img_url
                
                # 视频时长
                duration = elem.select_one("small.layer").text.strip() if elem.select_one("small.layer") else None
                
                # 清晰度
                quality = elem.select_one("small.vip-layer").text.strip() if elem.select_one("small.vip-layer") else None
                
                # 标题
                title_elem = elem.select_one("a.title")
                title = title_elem.text.strip() if title_elem else None
                
                # 作者信息
                author_elem = elem.select_one("div.text-muted a.text-dark")
                author = author_elem.text.strip() if author_elem else None
                author_link = "https://91porny.com" + author_elem["href"] if author_elem else None
                
                # 发布时间和播放次数
                meta_info = elem.select("div.text-muted")[-1].text.strip() if len(elem.select("div.text-muted")) > 1 else None
                published_time = None
                play_count = None
                
                if meta_info:
                    parts = meta_info.split("|")
                    if len(parts) == 2:
                        published_time = parts[0].strip()
                        play_count = parts[1].strip()
                
                video_data = {
                    "title": title,
                    "video_link": video_link,
                    "img_url": img_url,
                    "duration": duration,
                    "quality": quality,
                    "author": author,
                    "author_link": author_link,
                    "published_time": published_time,
                    "play_count": play_count
                }
                
                page_videos.append(video_data)
            except Exception as e:
                print(f"解析视频元素时出错: {e}")
                continue
        
        return page_videos
    
    def crawl(self):
        """爬取指定页数的视频信息"""
        max_retries = 3  # 最大重试次数
        
        for page in range(self.start_page, self.end_page + 1):
            retries = 0
            success = False
            
            while retries < max_retries and not success:
                print(f"正在爬取第{page}页...（尝试 {retries+1}/{max_retries}）")
                html = self.get_page(page)
                
                if not html:
                    print(f"第{page}页获取失败，稍后重试...")
                    retries += 1
                    # 增加重试的延迟时间
                    delay_time = self.delay * (retries + 1) + random.uniform(3, 8)
                    print(f"等待{delay_time:.2f}秒后重试...")
                    time.sleep(delay_time)
                    continue
                
                page_videos = [video for video in self.parse_page(html) if video["title"] is not None]
                
                if page_videos:
                    self.all_videos.extend(page_videos)
                    print(f"第{page}页成功爬取了{len(page_videos)}个视频")
                    success = True
                else:
                    print(f"第{page}页没有解析到视频数据，稍后重试...")
                    retries += 1
                    if retries < max_retries:
                        delay_time = self.delay * (retries + 1) + random.uniform(3, 8)
                        print(f"等待{delay_time:.2f}秒后重试...")
                        time.sleep(delay_time)
            
            if not success:
                print(f"第{page}页在{max_retries}次尝试后仍然失败，跳过该页...")
            
            # 页面间的随机延迟，显著增加以避免被封
            if page < self.end_page:
                delay_time = self.delay * 2 + random.uniform(5, 15)
                print(f"等待{delay_time:.2f}秒后继续下一页...")
                time.sleep(delay_time)
        
        # 保存结果
        self.save_results()
    
    def save_results(self):
        """保存爬取的视频信息到文件"""
        if not self.all_videos:
            print("没有爬取到任何视频数据")
            return
            
        with open(self.result_file, "w", encoding="utf-8") as f:
            json.dump(self.all_videos, f, ensure_ascii=False, indent=2)
        print(f"已成功爬取共{len(self.all_videos)}个视频，并保存到{self.result_file}")

if __name__ == "__main__":
    # 运行前需要安装依赖：pip install cloudscraper fake-useragent
    print("开始爬取，请确保已安装所需依赖: pip install cloudscraper fake-useragent")
    
    # 创建爬虫实例并开始爬取，降低爬取页数并增加延迟
    spider = Spider91Porny(start_page=1, end_page=3, delay=5)
    spider.crawl()
