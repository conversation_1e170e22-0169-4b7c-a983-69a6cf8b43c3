import requests
import cloudscraper
import random
import time
import re
import os
import subprocess
from bs4 import BeautifulSoup
import json

class VideoDetailSpider:
    def __init__(self):
        # 创建cloudscraper实例处理Cloudflare保护
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'darwin',
                'desktop': True
            },
            delay=5
        )
        
        # 随机User-Agent列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 Chrome/96.0.4664.110 Safari/537.36"
        ]
        
        # 基础请求头
        self.headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
            "DNT": "1",
            "Sec-CH-UA": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"",
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": "\"macOS\"",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Connection": "keep-alive"
        }
        
        # 检查ffmpeg是否存在
        self.ffmpeg_available = self._check_ffmpeg()
    
    def _check_ffmpeg(self):
        """检查系统是否安装了ffmpeg"""
        try:
            subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return True
        except:
            print("警告: 未检测到ffmpeg，下载m3u8视频功能将不可用")
            return False
    
    def get_video_url(self, embed_url):
        """从嵌入页面获取视频地址"""
        try:
            # 随机User-Agent
            current_headers = self.headers.copy()
            current_headers["User-Agent"] = random.choice(self.user_agents)
            current_headers["Referer"] = "https://91porny.com/"
            
            # 获取嵌入页面内容
            print(f"正在获取视频嵌入页面: {embed_url}")
            response = self.scraper.get(embed_url, headers=current_headers, timeout=15)
            response.raise_for_status()
            html = response.text
            
            # 方法1: 直接从HTML中查找video标签
            soup = BeautifulSoup(html, "html.parser")
            video_tag = soup.find("video")
            if video_tag and video_tag.has_attr("src"):
                return {"video_url": video_tag["src"]}
            
            # 方法2: 从JavaScript变量中提取
            video_url_pattern = re.search(r'var\s+videoUrl\s*=\s*["\']([^"\']+)["\']', html)
            if video_url_pattern:
                return {"video_url": video_url_pattern.group(1)}
            
            # 方法3: 查找source标签
            source_tag = soup.find("source")
            if source_tag and source_tag.has_attr("src"):
                return {"video_url": source_tag["src"]}
            
            # 方法4: 查找m3u8文件链接
            m3u8_pattern = re.search(r'["\']([^"\']+\.m3u8[^"\']*)["\']', html)
            if m3u8_pattern:
                return {"video_url": m3u8_pattern.group(1)}
            
            # 方法5: 查找所有script标签中的视频地址
            for script in soup.find_all("script"):
                if script.string:
                    # 查找.m3u8或.mp4链接
                    video_pattern = re.search(r'["\']((https?:)?//[^"\']+\.(m3u8|mp4)[^"\']*)["\']', script.string)
                    if video_pattern:
                        video_url = video_pattern.group(1)
                        if video_url.startswith("//"):
                            video_url = "https:" + video_url
                        return {"video_url": video_url}
            
            print("未能找到视频地址")
            return {"error": "无法提取视频地址"}
            
        except Exception as e:
            print(f"获取视频地址出错: {e}")
            return {"error": str(e)}

    def save_video_info(self, video_id, video_info):
        """保存视频信息到文件"""
        filename = f"{video_id}_info.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(video_info, f, ensure_ascii=False, indent=2)
        print(f"视频信息已保存到 {filename}")
    
    def download_m3u8(self, video_id, video_url, headers):
        """下载m3u8视频"""
        # 检查是否安装了ffmpeg
        if not self.ffmpeg_available:
            print("错误: 下载m3u8视频需要安装ffmpeg")
            return False
        
        output_file = f"{video_id}.mp4"
        
        # 如果文件已存在，直接覆盖
        if os.path.exists(output_file):
            print(f"文件 {output_file} 已存在，将覆盖")
        
        try:
            print(f"开始下载视频 {video_id}...")
            # 使用ffmpeg下载视频
            command = [
                "ffmpeg",
                "-protocol_whitelist", "file,http,https,tcp,tls,crypto",
                "-headers", f"Referer: {headers['Referer']}\r\nUser-Agent: {headers['User-Agent']}\r\nOrigin: {headers['Origin']}\r\n",
                "-i", video_url,
                "-c", "copy",
                "-bsf:a", "aac_adtstoasc",
                output_file,
                "-y"  # 覆盖输出文件
            ]
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 显示下载进度
            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 提取并打印下载进度
                    if "time=" in output:
                        print(f"\r进度: {output.strip()}", end="")
            
            # 检查下载结果
            if process.returncode == 0:
                print(f"\n视频下载完成: {output_file}")
                return True
            else:
                print(f"\n视频下载失败，退出码: {process.returncode}")
                return False
                
        except Exception as e:
            print(f"下载视频时出错: {e}")
            return False

    def download_with_retry(self, video_id, embed_url, max_retries=3):
        """带重试机制的下载，自动处理m3u8链接失效问题"""
        retries = 0
        while retries < max_retries:
            # 每次都重新获取最新的视频URL
            video_info = self.get_video_url(embed_url)
            
            if "video_url" not in video_info:
                print(f"获取视频地址失败: {video_info.get('error', '未知错误')}")
                return False
            
            # 修复HTML实体编码
            video_info["video_url"] = video_info["video_url"].replace("&amp;", "&")
            
            # 添加必要请求头
            headers = {
                "Referer": embed_url,
                "User-Agent": random.choice(self.user_agents),
                "Origin": "https://91porny.com"
            }
            
            print(f"尝试下载 (第{retries+1}次)，使用最新视频地址...")
            
            # 尝试下载
            result = self.download_m3u8(video_id, video_info["video_url"], headers)
            if result:
                return True
            
            retries += 1
            print(f"下载失败，等待5秒后重试...")
            time.sleep(5)
        
        print(f"已尝试{max_retries}次，下载失败")
        return False

def extract_video_id(url):
    """从URL中提取视频ID"""
    # 例如从https://91porny.com/video/embed/e9d029b8cf90878cd48e提取e9d029b8cf90878cd48e
    parts = url.split("/")
    return parts[-1]

def main_download(url):
    spider = VideoDetailSpider()
    video_id = extract_video_id(url)
    spider.download_with_retry(video_id, url)

def main_get_info(url):
    spider = VideoDetailSpider()
    video_id = extract_video_id(url)
    video_info = spider.get_video_url(url)
    # spider.save_video_info(video_id, video_info)
    return video_info

if __name__ == "__main__":
    embed_url = "https://91porny.com/video/embed/e9d029b8cf90878cd48e"
    main_get_info(embed_url)