import sys
import json
import requests
import time

def main():
    if len(sys.argv) < 2:
        print("请提供要查询的域名关键词")
        print("用法: python http_domain.py [关键词]")
        print("设置默认关键词为 'ai'")
        keyword = "mcp"
        # sys.exit(1)
    else:
        keyword = sys.argv[1]

    print(f"正在查询关键词 '{keyword}' 的域名信息...\n")

    tlds = [
        # 核心通用 TLDs
        ".com", ".net", ".org", ".info",".io", ".co", ".ai", ".cn",".me", ".tv", ".cc", ".app", ".xyz", ".so",".video", ".domains",".pro", ".top",
        ".dev",".tech",".site",
    ]

    domains = [keyword + tld for tld in tlds]
    if not domains:
        print("没有生成任何域名进行查询。")
        sys.exit(0)
        
    domains_param = "%2C".join(domains)

    url_sse = f"https://instant.who.sb/api/v1/check?domain={domains_param}&sse=true&return_dates=true&return-prices=true"

    print("正在发送SSE请求...")
    
    headers_sse = {"Accept": "text/event-stream"}
    
    start_time = time.time()
    timeout_seconds = 60
    resp_sse = None 
    http_client = requests.Session()
    http_client.timeout = 10 # Default timeout for WHOIS requests

    try:
        # For SSE, timeout for initial connection, then stream indefinitely (or until server closes)
        # The overall script timeout (timeout_seconds) will handle long-running streams.
        resp_sse = requests.get(url_sse, headers=headers_sse, stream=True, timeout=15) 
        resp_sse.raise_for_status() # Check for HTTP errors like 4xx/5xx
    except requests.exceptions.RequestException as e:
        print(f"SSE请求失败: {e}")
        sys.exit(1)

    print("开始接收数据流...")
    print("---------------------------------------------------------------------------------------------------------------------------------------------")
    print(f"{'域名':<20} {'状态':<10} {'注册商':<30} {'注册时间':<25} {'到期时间':<25} {'详细状态'}")
    print("---------------------------------------------------------------------------------------------------------------------------------------------")

    current_event_name = None
    current_event_data_json_str = None 
    current_event_id = None
    
    result_count = 0
    processed_domains = set()

    try:
        for line_bytes in resp_sse.iter_lines():
            if time.time() - start_time > timeout_seconds:
                print("\n查询超时")
                if result_count < len(domains):
                    print(f"已完成 {result_count}/{len(domains)} 个域名的查询。")
                break
            
            line = line_bytes.decode('utf-8').strip()

            if not line: # Empty line signifies end of an event
                if current_event_name and current_event_data_json_str:
                    event_data = None
                    try:
                        event_data = json.loads(current_event_data_json_str)
                    except json.JSONDecodeError:
                        # Silently ignore malformed JSON in SSE data line to avoid crashing
                        # print(f"解析SSE JSON失败 for {current_event_name}: {e}, data: {current_event_data_json_str}")
                        current_event_name = None
                        current_event_data_json_str = None
                        current_event_id = None
                        continue

                    event_type = current_event_name
                    
                    if event_type == "shallow-checked" or event_type == "whois-cache-checked":
                        domain = event_data.get("domain")
                        meta = event_data.get("meta", {})

                        if not domain: # Should not happen if API is consistent
                            current_event_name = None
                            current_event_data_json_str = None
                            current_event_id = None
                            continue
                        
                        if domain in processed_domains: # Avoid reprocessing
                             current_event_name = None
                             current_event_data_json_str = None
                             current_event_id = None
                             continue

                        status = "未注册"
                        reg_date = "-"
                        exp_date = "-"
                        registrar = "-"
                        domain_detailed_status = "-" # Corresponds to ParsedWhoisData.Status, not used in Go output

                        existed = meta.get("existed")

                        if existed == "yes":
                            status = "已注册"
                            # From SSE event
                            if event_type == "shallow-checked":
                                dates_data = meta.get("dates", {})
                                if isinstance(dates_data, dict): # Ensure dates_data is a dict
                                     created = dates_data.get("created")
                                     if created: reg_date = created
                            elif event_type == "whois-cache-checked":
                                if meta.get("registered"): reg_date = meta["registered"]
                                if meta.get("expires"): exp_date = meta["expires"]
                            
                            # Call /api/v1/whois
                            url_whois = f"https://instant.who.sb/api/v1/whois?domain={domain}&cache=true&return-prices=false"
                            try:
                                resp_whois = http_client.get(url_whois) # Uses session's timeout
                                if resp_whois.status_code == 200:
                                    whois_data_json = resp_whois.json()
                                    parsed_whois = whois_data_json.get("parsed", {})
                                    if parsed_whois and parsed_whois.get("id"): # Check if parsed data is meaningful
                                        # Ensure registrar is not None before assigning
                                        registrar_val = parsed_whois.get("registrar")
                                        registrar = registrar_val if registrar_val is not None else "-"
                                        
                                        if parsed_whois.get("registered"):
                                            reg_date = parsed_whois["registered"]
                                        if parsed_whois.get("expires"):
                                            exp_date = parsed_whois["expires"]
                                        # domain_detailed_status = parsed_whois.get("status", "-") # As in Go, this was commented out
                                else:
                                    # Non-200 WHOIS responses are not necessarily errors for the whole script
                                    # print(f"WHOIS请求 '{domain}' 返回状态码: {resp_whois.status_code}")
                                    pass # Silently continue if WHOIS fails, keeping SSE data
                            except requests.exceptions.RequestException:
                                # print(f"WHOIS请求异常 {domain}: {e_whois}")
                                pass # Silently ignore individual WHOIS request errors
                            except json.JSONDecodeError:
                                # print(f"WHOIS JSON解析失败 {domain}: {e_json_whois}")
                                pass # Silently ignore WHOIS JSON parsing errors
                        
                        print(f"{domain:<20} {status:<10} {registrar:<30} {reg_date:<25} {exp_date:<25} {domain_detailed_status}")
                        processed_domains.add(domain)
                        result_count = len(processed_domains)

                        if result_count >= len(domains):
                            print("---------------------------------------------------------------------------------------------------------------------------------------------")
                            print("所有域名检查完成")
                            # Reset for next event and break outer loop
                            current_event_name = None
                            current_event_data_json_str = None
                            current_event_id = None
                            if resp_sse: resp_sse.close()
                            http_client.close()
                            return # Exit main function
                    
                    # Reset for next event
                    current_event_name = None
                    current_event_data_json_str = None
                    current_event_id = None
                continue

            if line.startswith("event:"):
                current_event_name = line[len("event:"):].strip()
            elif line.startswith("data:"):
                current_event_data_json_str = line[len("data:"):].strip() # Overwrites if multiple data lines, matching Go behavior
            elif line.startswith("id:"):
                current_event_id = line[len("id:"):].strip()
            # Ignore comment lines (starting with ':') or other lines
            
        # After loop finishes (either by break, or stream naturally ending)
        if not (time.time() - start_time > timeout_seconds): # If not ended by timeout
            if result_count < len(domains):
                print("\nSSE流结束，但并非所有域名都已处理。")
                print(f"已完成 {result_count}/{len(domains)} 个域名的查询。")
            # If result_count == len(domains), it was handled by the "所有域名检查完成" block

    except requests.exceptions.ChunkedEncodingError:
        print("SSE流传输中断 (ChunkedEncodingError)")
    except Exception as e:
        print(f"处理SSE流时发生意外错误: {e}")
    finally:
        if resp_sse:
            resp_sse.close()
        http_client.close()
        
        # Final status if not already covered by "所有域名检查完成" or "查询超时"
        if not (result_count >= len(domains) or (time.time() - start_time > timeout_seconds)):
            print("---------------------------------------------------------------------------------------------------------------------------------------------")
            print(f"域名检查结束. 已处理: {result_count}/{len(domains)}")


if __name__ == "__main__":
    main()
