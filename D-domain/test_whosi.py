import whois
import time
from concurrent.futures import ThreadPoolExecutor

def check_domain_availability(domain):
    """
    检查域名是否可用（未被注册）
    返回: 如果域名可用返回True，否则返回False
    """
    try:
        domain_info = whois.whois(domain)
        # 如果没有注册日期，则域名可能可用
        if domain_info.creation_date is None:
            return True
        return False
    except Exception as e:
        # 某些WHOIS错误通常表示域名未被注册
        if "No match for domain" in str(e):
            return True
        # 其他错误可能是查询问题
        print(f"检查域名 {domain} 时出错: {e}")
        return None

def batch_check_domains(domains, max_workers=10):
    """
    批量检查多个域名的可用性
    domains: 域名列表
    max_workers: 最大线程数
    """
    results = {}
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_domain = {executor.submit(check_domain_availability, domain): domain for domain in domains}
        
        for future in future_to_domain:
            domain = future_to_domain[future]
            try:
                available = future.result()
                results[domain] = available
                status = "可用" if available else "已注册"
                print(f"{domain}: {status}")
                # 添加延迟避免被服务器封禁
                time.sleep(1)
            except Exception as e:
                print(f"处理域名 {domain} 结果时出错: {e}")
    
    return results

# 示例用法
if __name__ == "__main__":
    # 要检查的域名列表
    domains_to_check = [
        "example.com",
        "example.net",
        "example.org",
        "examplesite123.com",
        "techfusion.io",
        "digitalgrowth.net"
    ]
    
    # 执行批量检查
    results = batch_check_domains(domains_to_check)
    
    # 打印可用的域名
    available_domains = [domain for domain, available in results.items() if available]
    print("\n可用的域名:")
    for domain in available_domains:
        print(domain)