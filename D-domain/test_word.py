import random
try:
    from nltk.corpus import words as nltk_words_resource # 使用别名导入
    words_corpus = nltk_words_resource # 将导入的资源赋值给 words_corpus
    # 确保 NLTK 的 words 语料库已下载
    # 首次运行时，如果未下载，Python 可能会提示错误或需要您手动下载。
    # 您可以运行以下 Python 代码来下载 (通常只需执行一次):
    # import nltk
    # nltk.download('words')
except ImportError:
    print("错误：NLTK库未安装。请先安装 NLTK (例如: pip install nltk) 并下载 'words' 语料库。")
    print("您可以运行以下 Python 代码来下载 (通常只需执行一次):")
    print("import nltk")
    print("nltk.download('words')")
    words_corpus = None # 重命名以避免与标准库 `words` 模块潜在冲突

class WordCombiner:
    MIN_WORD_LEN = 2
    MAX_WORD_LEN = 5 # 单个词的最大长度，以帮助满足组合长度
    MAX_COMBINED_LEN = 10
    MAX_RETRIES_FOR_COMBINATION = 100

    def __init__(self, word_list=None):
        if word_list:
            self.word_list = [w.lower() for w in word_list if self.MIN_WORD_LEN <= len(w) <= self.MAX_WORD_LEN and w.isalpha()]
            print(f"自定义词典加载并筛选完毕，可用单词：{len(self.word_list)}")
        elif words_corpus:
            print("正在加载并筛选 NLTK 英文词典...")
            # 筛选：小写，纯字母，长度在 MIN_WORD_LEN 和 MAX_WORD_LEN 之间
            self.word_list = [
                w.lower() for w in words_corpus.words() 
                if self.MIN_WORD_LEN <= len(w) <= self.MAX_WORD_LEN and w.isalpha()
            ]
            print(f"NLTK 词典加载并筛选完毕，包含 {len(self.word_list)} 个符合条件的单词。")
        else:
            print("警告：NLTK 词典加载失败或未配置。将使用内置的小型示例词典（已筛选）。")
            fallback_words = [
                "apple", "banana", "cherry", "date", "elderberry",
                "fig", "grape", "honeydew", "kiwi", "lemon",
                "mango", "nectarine", "orange", "papaya", "quince",
                "raspberry", "strawberry", "tangerine", "ugli", "vanilla",
                "watermelon", "xigua", "yuzu", "zucchini"
            ]
            self.word_list = [w.lower() for w in fallback_words if self.MIN_WORD_LEN <= len(w) <= self.MAX_WORD_LEN and w.isalpha()]
            print(f"小型词典加载并筛选完毕，包含 {len(self.word_list)} 个符合条件的单词。")
        
        if not self.word_list:
            print("警告：筛选后词典为空！可能无法生成任何组合。请检查词源或筛选条件。")

    def combine_two_words(self):
        if not self.word_list or len(self.word_list) < 2:
            return "词典中的单词不足以组合，或者筛选后词典为空。"

        for _ in range(self.MAX_RETRIES_FOR_COMBINATION):
            word1 = random.choice(self.word_list)
            word2 = random.choice(self.word_list)
            
            # 可以选择不允许重复使用同一个词
            # if word1 == word2 and len(self.word_list) > 1: # 需要词典至少有两个不同的词才能避免死循环
            #     temp_list = [w for w in self.word_list if w != word1]
            #     if not temp_list:
            #         continue # 如果除了word1没别的词了，就重试外层循环
            #     word2 = random.choice(temp_list)
            # elif word1 == word2:
            #     continue # 词典只有一个词，或者恰好抽到同一个，且不允许，则重试

            combined_word = word1 + word2
            if len(combined_word) <= self.MAX_COMBINED_LEN:
                return combined_word
            
        return f"未能找到长度不超过 {self.MAX_COMBINED_LEN} 的组合 (尝试了 {self.MAX_RETRIES_FOR_COMBINATION} 次)。"

    def generate_combinations_continuously(self, count=10):
        if not self.word_list or len(self.word_list) < 2:
            print("无法生成组合：词典中的单词不足或筛选后为空。")
            return

        print(f"\n从筛选后的词典中（包含 {len(self.word_list)} 个单词）持续组合两个单词：")
        successful_combinations = 0
        for i in range(count):
            combined_word = self.combine_two_words()
            print(f"组合 {i+1}: {combined_word}")
            if not combined_word.startswith("未能找到长度不超过"):
                successful_combinations += 1
        print(f"\n成功生成 {successful_combinations} 个符合条件的组合。")

    def generate_acronym_like_word(self, length: int):
        """
        生成指定长度的、由随机选择的单词首字母组成的缩略词。
        用于生成简短的域名候选。
        """
        if not self.word_list or len(self.word_list) < length:
            return f"筛选后词典中单词数 ({len(self.word_list)}) 少于所需长度 {length}，无法生成缩略词。"
        
        try:
            # 从词典中随机抽取 'length' 个不同的单词作为首字母的来源
            chosen_words = random.sample(self.word_list, length)
            # 提取每个单词的首字母（词表已预处理为小写）
            acronym = "".join([word[0] for word in chosen_words])
            return acronym
        except ValueError: 
            # 这通常在 len(self.word_list) < length 时发生，已被上面检查覆盖，但作为保险
            return f"无法从词典中抽取 {length} 个不同单词 (当前词典大小: {len(self.word_list)})。"

# --- 主程序 ---
if __name__ == "__main__":
    if words_corpus is None and not WordCombiner().word_list: # 检查NLTK是否失败且回退列表也为空
        print("\n无法继续，因为 NLTK 或其 'words' 语料库未能加载，且回退词典处理后也为空。请检查安装和筛选条件。")
    else:
        combiner = WordCombiner()
        if combiner.word_list and len(combiner.word_list) >=2:
             combiner.generate_combinations_continuously(50) # 生成50个组合示例

             print("\n--- 生成指定长度的缩略词样域名 ---")
             # 您可以根据需要调整缩略词的长度范围和生成数量
             min_acronym_len = 3
             max_acronym_len = 5 # 根据您的MAX_COMBINED_LEN = 10，缩略词不宜过长
             num_acronyms_to_generate = 15

             if len(combiner.word_list) < min_acronym_len:
                 print(f"筛选后词典中单词数 ({len(combiner.word_list)}) 过少，无法生成长度至少为 {min_acronym_len} 的缩略词。")
             else:
                for i in range(num_acronyms_to_generate):
                    # 随机决定本次生成的缩略词长度
                    # 确保目标长度不超过可用单词数，以防random.sample出错
                    current_max_len = min(max_acronym_len, len(combiner.word_list))
                    if current_max_len < min_acronym_len: # 如果筛选后词太少，连最小长度都达不到
                        print(f"无法生成长度在 {min_acronym_len}-{max_acronym_len} 之间的缩略词，词典单词不足。")
                        break
                    target_len = random.randint(min_acronym_len, current_max_len)
                    
                    acronym_word = combiner.generate_acronym_like_word(target_len)
                    print(f"长度为 {target_len} 的缩略词 {i+1}: {acronym_word}")
        else:
            print("\n词典中可用单词不足 (<2)，无法进行组合。")

    # 提示:
    # 为了获得更精准的 "热门" 词汇，建议使用专门整理过的热门词汇列表文件，
    # 并通过下面的自定义词典加载方式导入。
    # NLTK 的 'words' 语料库经过筛选后提供的是通用且符合基本条件的英文词汇。

    # 示例：使用自定义词汇列表文件 (例如: popular_words.txt, 每行一个单词)
    # try:
    #     with open("popular_words.txt", "r", encoding="utf-8") as f:
    #         custom_words = [line.strip() for line in f if line.strip()]
    #     if custom_words:
    #         print("\n--- 使用自定义词典 ---")
    #         # 自定义词典也会经过 WordCombiner内部的筛选逻辑
    #         custom_combiner = WordCombiner(word_list=custom_words)
    #         if custom_combiner.word_list and len(custom_combiner.word_list) >=2:
    #             custom_combiner.generate_combinations_continuously(10)
    #         else:
    #             print("\n自定义词典筛选后可用单词不足 (<2)，无法进行组合。")   
    #     else:
    #         print("\n自定义词汇文件为空或未能加载。")
    # except FileNotFoundError:
    #     print("\n未找到自定义词汇文件 popular_words.txt。")
    # except Exception as e:
    #     print(f"\n加载自定义词汇文件时出错: {e}")
