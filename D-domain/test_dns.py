import dns.resolver
import socket
from concurrent.futures import ThreadPoolExecutor
import time

def check_domain_dns(domain):
    """
    通过DNS查询检查域名是否被注册
    如果域名未注册，DNS查询通常会失败
    """
    try:
        # 尝试解析A记录
        answers = dns.resolver.resolve(domain, 'A')
        return False  # 域名已注册
    except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
        # NXDOMAIN表示域名不存在，NoAnswer表示没有A记录
        # 进一步检查是否有其他记录
        try:
            # 尝试使用socket进行连接测试
            socket.gethostbyname(domain)
            return False  # 域名已注册
        except socket.gaierror:
            return True  # 域名可能未注册
    except Exception as e:
        print(f"检查域名 {domain} 时出错: {e}")
        return None

def batch_check_domains_dns(domains, max_workers=20):
    """
    使用DNS查询批量检查域名可用性（更快但准确性略低）
    """
    results = {}
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_domain = {executor.submit(check_domain_dns, domain): domain for domain in domains}
        
        for future in future_to_domain:
            domain = future_to_domain[future]
            try:
                available = future.result()
                results[domain] = available
                status = "可用" if available else "已注册"
                print(f"{domain}: {status}")
                # 添加少量延迟避免请求过快
                time.sleep(0.2)
            except Exception as e:
                print(f"处理域名 {domain} 结果时出错: {e}")
    
    return results

def generate_domain_variations(base_word, extensions=None, prefixes=None, suffixes=None):
    """
    根据基础词生成域名变体
    """
    if extensions is None:
        extensions = ['.com', '.net', '.org', '.io', '.co', '.app', '.tech']
    
    if prefixes is None:
        prefixes = ['', 'my', 'get', 'try', 'use', 'the', 'best', 'pro', 'smart']
    
    if suffixes is None:
        suffixes = ['', 'app', 'hub', 'spot', 'zone', 'tech', 'cloud', 'site', 'plus', 'pro']
    
    domains = []
    
    for prefix in prefixes:
        for suffix in suffixes:
            for ext in extensions:
                if prefix and suffix:
                    domain = f"{prefix}{base_word}{suffix}{ext}"
                elif prefix:
                    domain = f"{prefix}{base_word}{ext}"
                elif suffix:
                    domain = f"{base_word}{suffix}{ext}"
                else:
                    domain = f"{base_word}{ext}"
                
                domains.append(domain.lower())
    
    return domains

# 示例用法
if __name__ == "__main__":
    # 基础关键词
    base_words = ["tech", "digital", "cloud", "app", "smart", "cyber"]
    
    # 生成域名变体
    all_domains = []
    for word in base_words:
        variations = generate_domain_variations(
            word, 
            extensions=['.com', '.net', '.io'], 
            prefixes=['', 'my', 'get'],
            suffixes=['', 'hub', 'pro']
        )
        all_domains.extend(variations[:5])  # 限制每个基础词只取5个变体
    
    # 仅检查前10个域名作为示例
    sample_domains = all_domains[:10]
    print(f"检查以下域名: {sample_domains}")
    
    # 执行批量检查
    results = batch_check_domains_dns(sample_domains)
    
    # 打印可用的域名
    available_domains = [domain for domain, available in results.items() if available]
    print("\n可用的域名:")
    for domain in available_domains:
        print(domain)