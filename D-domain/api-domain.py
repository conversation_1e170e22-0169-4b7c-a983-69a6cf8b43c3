import whois
import time
import asyncio
import aiohttp
from concurrent.futures import Thr<PERSON>PoolExecutor
from flask import Flask, request, jsonify, render_template_string
from datetime import datetime
import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('domain_checker.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

def check_domain_with_whois(domain_name):
    """使用python-whois库获取准确的WHOIS信息"""
    start_time = time.time()
    whois_info = {}
    try:
        logger.info(f"开始查询域名: {domain_name}")
        # 修复whois模块用法
        try:
            # 尝试新版本whois用法
            domain = whois.query(domain_name)
            if domain:
                whois_info = domain.__dict__
                registered = True
                result = "域名已被注册"
                status = True
                creation_date = domain.creation_date
                if isinstance(creation_date, datetime):
                    creation_date = creation_date.strftime('%Y-%m-%d')
                elif creation_date:
                    creation_date = str(creation_date)
            else:
                result = "域名可以注册"
                status = False
                creation_date = None
        except AttributeError:
            # 尝试另一种用法
            domain = whois.whois(domain_name)
            
            # 将字典形式的结果转换为更易读的格式
            if domain:
                # 检查是否有注册日期，expiration_date或status来判断域名是否被注册
                registered = False
                creation_date = None
                
                # 更好的判断方式，优先检查status，然后是创建和过期日期
                if hasattr(domain, 'status') and domain.status or hasattr(domain, 'registrar') and domain.registrar:
                    registered = True
                elif hasattr(domain, 'creation_date') and domain.creation_date:
                    registered = True
                    # 处理创建日期可能是列表的情况
                    if isinstance(domain.creation_date, list) and domain.creation_date:
                        creation_date = domain.creation_date[0]
                    else:
                        creation_date = domain.creation_date
                    
                    if isinstance(creation_date, datetime):
                        creation_date = creation_date.strftime('%Y-%m-%d')
                    elif creation_date:
                        creation_date = str(creation_date)
                elif hasattr(domain, 'expiration_date') and domain.expiration_date:
                    registered = True
                
                # 如果注册了，准备详细信息
                if registered:
                    result = "域名已被注册"
                    status = True
                    
                    # 收集所有可能的WHOIS信息
                    for key, value in domain.items():
                        if value:
                            if isinstance(value, list) and value:
                                # 对于列表类型，取第一个值或全部加入
                                if key in ['name_servers', 'emails', 'status']:
                                    # 数组类型直接显示所有
                                    whois_info[key] = ", ".join(str(v) for v in value if v)
                                else:
                                    # 其他类型如日期，通常只取第一个
                                    first_value = value[0]
                                    if isinstance(first_value, datetime):
                                        whois_info[key] = first_value.strftime('%Y-%m-%d')
                                    else:
                                        whois_info[key] = str(first_value)
                            elif isinstance(value, datetime):
                                whois_info[key] = value.strftime('%Y-%m-%d')
                            else:
                                whois_info[key] = str(value)
                    
                    # 确保关键信息一定在字典里
                    if 'registrar' not in whois_info and hasattr(domain, 'registrar') and domain.registrar:
                        whois_info['registrar'] = str(domain.registrar)
                    if 'creation_date' not in whois_info and creation_date:
                        whois_info['creation_date'] = creation_date
                else:
                    result = "域名可以注册"
                    status = False
                    creation_date = None
            else:
                # 如果WHOIS查询结果为空
                result = "域名可以注册"
                status = False
                creation_date = None

        logger.info(f"查询完成: {domain_name}, 结果: {result}")
        
    except Exception as e:
        logger.error(f"查询域名 {domain_name} 时出错: {str(e)}")
        if "No match for domain" in str(e) or "No match for" in str(e) or "not found" in str(e).lower():
            result = "域名可以注册"
            status = False
            creation_date = None
        else:
            result = f"WHOIS查询出错: {str(e)}"
            status = None  # 不确定状态
            creation_date = None
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    return {
        "方法": "WHOIS查询",
        "结果": result,
        "状态": status,  # True=已注册, False=未注册, None=不确定
        "创建日期": creation_date,
        "执行时间": f"{execution_time:.2f}秒",
        "whois_info": whois_info
    }

async def check_domain_with_api_async(domain_name):
    """使用query.domains API异步检查域名状态"""
    start_time = time.time()
    
    # 使用query.domains的API
    api_url = f"https://query.domains/api/check/{domain_name}"
    
    whois_info = {}
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, timeout=5) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if "available" in data:
                        if not data["available"]:
                            result = "域名已被注册"
                            status = True
                            
                            # 提取注册日期
                            creation_date = data.get("created")
                            if creation_date:
                                # API通常返回的是ISO格式日期
                                try:
                                    creation_date = datetime.fromisoformat(creation_date.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                                except:
                                    pass
                            
                            # 提取其他WHOIS信息
                            if "registrar" in data:
                                whois_info["registrar"] = data["registrar"]
                            if "expires" in data:
                                whois_info["expiration_date"] = data["expires"]
                            if "name_servers" in data:
                                whois_info["name_servers"] = ", ".join(data["name_servers"])
                        else:
                            result = "域名可以注册"
                            status = False
                            creation_date = None
                    else:
                        result = "API返回数据格式错误"
                        status = None
                        creation_date = None
                else:
                    result = f"API请求失败 (状态码: {response.status})"
                    status = None
                    creation_date = None
    except Exception as e:
        result = f"API请求出错: {str(e)}"
        status = None
        creation_date = None
    
    end_time = time.time()
    execution_time = end_time - start_time
    return {
        "方法": "API查询",
        "结果": result,
        "状态": status,
        "创建日期": creation_date,
        "执行时间": f"{execution_time:.2f}秒",
        "whois_info": whois_info
    }

def check_domain_fast(domain_name):
    """快速检查方法 - 使用DNS解析"""
    start_time = time.time()
    
    try:
        import socket
        socket.gethostbyname(domain_name)
        # 如果能解析到IP，则域名已注册
        result = "域名可能已被注册(DNS检测)"
        status = True
        creation_date = None
    except socket.gaierror:
        # 无法解析到IP，可能未注册
        result = "域名可能未注册(DNS检测)"
        status = False
        creation_date = None
    except Exception as e:
        result = f"DNS检测出错: {str(e)}"
        status = None
        creation_date = None
    
    end_time = time.time()
    execution_time = end_time - start_time
    return {
        "方法": "快速DNS检测",
        "结果": result,
        "状态": status,
        "创建日期": creation_date,
        "执行时间": f"{execution_time:.2f}秒",
        "whois_info": {}
    }

async def check_domain_combined_async(domain_name):
    """异步结合多种查询方法，提高准确性"""
    # 首先使用DNS快速检查
    dns_result = check_domain_fast(domain_name)
    
    # 如果DNS检查结果已经确定域名未注册，那么就不需要进一步查询
    if dns_result["状态"] is False:
        return dns_result
    
    # 否则，同时使用API和WHOIS进行查询，取结果更可靠的一个
    api_result_future = asyncio.create_task(check_domain_with_api_async(domain_name))
    
    # WHOIS查询放在线程池中执行，避免阻塞
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as pool:
        whois_result_future = loop.run_in_executor(pool, check_domain_with_whois, domain_name)
        
        # 等待两个查询都完成
        api_result = await api_result_future
        whois_result = await whois_result_future
    
    # 如果API查询成功且提供了详细信息，优先使用API结果
    if api_result["状态"] is not None and api_result["whois_info"]:
        return api_result
    
    # 如果WHOIS查询成功，使用WHOIS结果
    if whois_result["状态"] is not None:
        return whois_result
    
    # 如果都失败，但API有结果，使用API结果
    if api_result["状态"] is not None:
        return api_result
    
    # 最后fallback到DNS结果
    return dns_result

def generate_domain_variants(base_domain):
    """生成不同后缀的域名变体"""
    # 常见域名后缀列表 - 扩展了更多主流后缀
    common_tlds = [
        "com", "net", "org", "io", "ai", "cn", "co", "app", "dev", 
        "tech", "info", "biz", "me", "xyz", "site", "online", "store", 
        "shop", "digital", "cloud", "live", "world", "vip", "top", "fun",
        "design", "space", "club", "team", "pro", "studio", "blog"
    ]
    
    # 处理输入的域名,去除可能已有的后缀
    if "." in base_domain:
        parts = base_domain.split(".")
        if len(parts) >= 2 and parts[-1].lower() in common_tlds:
            base_name = ".".join(parts[:-1])
        else:
            base_name = base_domain
    else:
        base_name = base_domain
    
    # 生成不同后缀的域名列表
    domain_variants = [f"{base_name}.{tld}" for tld in common_tlds]
    
    return domain_variants

def generate_related_keywords(keyword):
    """生成与给定关键词相关的词汇 - 扩展了更多主流关键词组合"""
    # 移除可能的TLD后缀，获取基础名称
    if "." in keyword:
        base_name = keyword.split(".")[0]
    else:
        base_name = keyword
    
    # 常见前缀 - 扩展更多主流前缀
    prefixes = [
        "my", "the", "best", "top", "get", "try", "use", "pro", "smart", 
        "easy", "quick", "fast", "go", "new", "next", "first", "meta",
        "ai", "web", "app", "tech", "cloud", "cyber", "digital", "online",
        "e", "i", "insta", "super", "ultra", "power", "global", "world"
    ]
    
    # 常见后缀 - 扩展更多主流后缀
    suffixes = [
        "hub", "spot", "center", "place", "space", "zone", "app", 
        "tool", "box", "tech", "solutions", "group", "pro", "plus",
        "now", "today", "hq", "ai", "cloud", "web", "lab", "studio",
        "works", "one", "team", "store", "shop", "market", "world",
        "global", "connect", "link", "net", "central", "point", "base"
    ]
    
    # 常见组合单词（两个关键词的组合）
    common_pairs = [
        f"{base_name}tech", f"tech{base_name}", 
        f"{base_name}ai", f"ai{base_name}",
        f"{base_name}app", f"app{base_name}",
        f"{base_name}hub", f"{base_name}pro",
        f"{base_name}cloud", f"cloud{base_name}",
        f"{base_name}web", f"web{base_name}",
        f"{base_name}digital", f"digital{base_name}",
        f"{base_name}online", f"{base_name}world"
    ]
    
    related_keywords = []
    
    # 生成前缀关键词
    for prefix in prefixes[:8]:  # 限制只使用前8个前缀
        related_keywords.append(f"{prefix}{base_name}")
    
    # 生成后缀关键词
    for suffix in suffixes[:8]:  # 限制只使用前8个后缀
        related_keywords.append(f"{base_name}{suffix}")
    
    # 添加常见组合
    related_keywords.extend(common_pairs[:8])
    
    # 返回前24个关联关键词
    return related_keywords[:24]

async def check_domains_async(keyword, max_workers=5):
    """异步检查域名可用性并返回结果"""
    # 生成域名变体
    domain_variants = generate_domain_variants(keyword)
    
    # 创建异步任务
    tasks = [check_domain_combined_async(domain) for domain in domain_variants]
    
    # 等待所有任务完成
    results_list = await asyncio.gather(*tasks)
    
    # 将结果与域名对应
    results = {domain: result for domain, result in zip(domain_variants, results_list)}
    
    return results

async def check_related_domains_async(keyword, limit=10):
    """异步检查与关键词相关的域名"""
    related_keywords = generate_related_keywords(keyword)
    
    # 只检查前limit个关联关键词
    domains_to_check = [f"{related_keyword}.com" for related_keyword in related_keywords[:limit]]
    
    # 创建异步任务
    tasks = [check_domain_combined_async(domain) for domain in domains_to_check]
    
    # 等待所有任务完成，使用gather返回结果顺序与任务顺序相同
    results_list = await asyncio.gather(*tasks)
    
    # 将结果与域名对应
    results = {domain: result for domain, result in zip(domains_to_check, results_list)}
    
    return results

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名注册查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .search-form {
            display: flex;
            margin: 20px 0;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-form input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            min-width: 200px;
        }
        .search-form button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .search-form button:hover {
            background-color: #45a049;
        }
        .options {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .available {
            color: green;
            font-weight: bold;
        }
        .unavailable {
            color: red;
        }
        .uncertain {
            color: orange;
        }
        .results-section {
            margin-top: 30px;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .method-badge {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            background-color: #f0f0f0;
            color: #666;
            margin-left: 5px;
        }
        .recheck-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 10px;
        }
        .recheck-btn:hover {
            background-color: #0b7dda;
        }
        .whois-details {
            display: none;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 14px;
        }
        .whois-details.show {
            display: block;
        }
        .detail-row {
            margin-bottom: 8px;
        }
        .detail-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        .detail-value {
            color: #333;
        }
        .toggle-details {
            color: #2196F3;
            text-decoration: underline;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
        }
        .spinner {
            display: inline-block;
            width: 14px;
            height: 14px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-top-color: #2196F3;
            border-radius: 50%;
            animation: spin 1s ease-in-out infinite;
            vertical-align: middle;
            margin-right: 5px;
        }
        .progress-bar {
            height: 4px;
            width: 100%;
            background-color: #f3f3f3;
            position: relative;
            margin-top: 10px;
            overflow: hidden;
            border-radius: 2px;
        }
        .progress-bar .progress {
            position: absolute;
            height: 100%;
            background-color: #4CAF50;
            width: 0;
            transition: width 0.3s ease;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>域名注册查询</h1>
        <form class="search-form" action="/" method="get">
            <input type="text" name="keyword" placeholder="输入关键词或域名" value="{{ keyword }}" required>
            <button type="submit">查询</button>
        </form>
        
        <div class="loading" id="loading">
            <div>正在查询中，请稍候...</div>
            <div class="progress-bar">
                <div class="progress" id="progress"></div>
            </div>
        </div>
        
        {% if domain_results %}
        <div class="results-section">
            <h2>主要域名结果</h2>
            <table>
                <tr>
                    <th>域名</th>
                    <th>状态</th>
                    <th>注册日期</th>
                    <th>操作</th>
                </tr>
                {% for domain, result in domain_results.items() %}
                <tr id="domain-row-{{ domain|replace('.', '-') }}">
                    <td>{{ domain }}</td>
                    {% if result.状态 == False %}
                    <td class="available">{{ result.结果 }} <span class="method-badge">{{ result.方法 }}</span></td>
                    <td>-</td>
                    <td>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'dns')">DNS二次检测</button>
                    </td>
                    {% elif result.状态 == True %}
                    <td class="unavailable">
                        {{ result.结果 }} <span class="method-badge">{{ result.方法 }}</span>
                        {% if result.whois_info %}
                        <span class="toggle-details" onclick="toggleWhoisDetails('{{ domain|replace('.', '-') }}')">显示详情</span>
                        {% endif %}
                    </td>
                    <td>{{ result.创建日期 or '-' }}</td>
                    <td>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'dns')">DNS二次检测</button>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'whois')">WHOIS检测</button>
                    </td>
                    {% else %}
                    <td class="uncertain">{{ result.结果 }} <span class="method-badge">{{ result.方法 }}</span></td>
                    <td>-</td>
                    <td>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'dns')">DNS二次检测</button>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'whois')">WHOIS检测</button>
                    </td>
                    {% endif %}
                </tr>
                {% if result.状态 == True and result.whois_info %}
                <tr id="whois-row-{{ domain|replace('.', '-') }}" style="display:none">
                    <td colspan="4">
                        <div class="whois-details" id="whois-details-{{ domain|replace('.', '-') }}">
                            <h3>WHOIS详细信息</h3>
                            {% for key, value in result.whois_info.items() %}
                            <div class="detail-row">
                                <span class="detail-label">{{ key|replace('_', ' ')|title }}:</span>
                                <span class="detail-value">{{ value }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </td>
                </tr>
                {% endif %}
                {% endfor %}
            </table>
        </div>
        {% endif %}
        
        {% if related_results %}
        <div class="results-section">
            <h2>相关域名结果</h2>
            <table>
                <tr>
                    <th>相关域名</th>
                    <th>状态</th>
                    <th>注册日期</th>
                    <th>操作</th>
                </tr>
                {% for domain, result in related_results.items() %}
                <tr id="domain-row-{{ domain|replace('.', '-') }}">
                    <td>{{ domain }}</td>
                    {% if result.状态 == False %}
                    <td class="available">{{ result.结果 }} <span class="method-badge">{{ result.方法 }}</span></td>
                    <td>-</td>
                    <td>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'dns')">DNS二次检测</button>
                    </td>
                    {% elif result.状态 == True %}
                    <td class="unavailable">
                        {{ result.结果 }} <span class="method-badge">{{ result.方法 }}</span>
                        {% if result.whois_info %}
                        <span class="toggle-details" onclick="toggleWhoisDetails('{{ domain|replace('.', '-') }}')">显示详情</span>
                        {% endif %}
                    </td>
                    <td>{{ result.创建日期 or '-' }}</td>
                    <td>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'dns')">DNS二次检测</button>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'whois')">WHOIS检测</button>
                    </td>
                    {% else %}
                    <td class="uncertain">{{ result.结果 }} <span class="method-badge">{{ result.方法 }}</span></td>
                    <td>-</td>
                    <td>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'dns')">DNS二次检测</button>
                        <button class="recheck-btn" onclick="recheckDomain('{{ domain }}', 'whois')">WHOIS检测</button>
                    </td>
                    {% endif %}
                </tr>
                {% if result.状态 == True and result.whois_info %}
                <tr id="whois-row-{{ domain|replace('.', '-') }}" style="display:none">
                    <td colspan="4">
                        <div class="whois-details" id="whois-details-{{ domain|replace('.', '-') }}">
                            <h3>WHOIS详细信息</h3>
                            {% for key, value in result.whois_info.items() %}
                            <div class="detail-row">
                                <span class="detail-label">{{ key|replace('_', ' ')|title }}:</span>
                                <span class="detail-value">{{ value }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </td>
                </tr>
                {% endif %}
                {% endfor %}
            </table>
        </div>
        {% endif %}
    </div>
    
    <script>
        document.querySelector('form').addEventListener('submit', function() {
            document.getElementById('loading').style.display = 'block';
            startProgressBar();
        });
        
        function startProgressBar() {
            const progress = document.getElementById('progress');
            let width = 0;
            const interval = setInterval(function() {
                if (width >= 90) {
                    clearInterval(interval);
                } else {
                    width += 2;
                    progress.style.width = width + '%';
                }
            }, 200);
        }
        
        function toggleWhoisDetails(domainId) {
            const row = document.getElementById('whois-row-' + domainId);
            const details = document.getElementById('whois-details-' + domainId);
            if (row.style.display === 'none') {
                row.style.display = 'table-row';
                details.classList.add('show');
            } else {
                row.style.display = 'none';
                details.classList.remove('show');
            }
        }
        
        function recheckDomain(domain, method) {
            const cell = document.querySelector(`#domain-row-${domain.replace(/\./g, '-')} td:nth-child(2)`);
            const originalContent = cell.innerHTML;
            cell.innerHTML = `<div class="spinner"></div> 正在检测中...`;
            
            fetch(`/api/recheck?domain=${encodeURIComponent(domain)}&method=${method}`)
            .then(response => response.json())
            .then(data => {
                const resultRow = document.querySelector(`#domain-row-${domain.replace(/\./g, '-')}`);
                const statCell = resultRow.querySelector('td:nth-child(2)');
                const dateCell = resultRow.querySelector('td:nth-child(3)');
                
                // 更新状态
                if (data.状态 === false) {
                    statCell.className = 'available';
                    statCell.innerHTML = `${data.结果} <span class="method-badge">${data.方法}</span>`;
                } else if (data.状态 === true) {
                    statCell.className = 'unavailable';
                    let content = `${data.结果} <span class="method-badge">${data.方法}</span>`;
                    if (data.whois_info && Object.keys(data.whois_info).length > 0) {
                        content += ` <span class="toggle-details" onclick="toggleWhoisDetails('${domain.replace(/\./g, '-')}')">显示详情</span>`;
                        
                        // 如果不存在WHOIS详情行，则创建
                        if (!document.getElementById(`whois-row-${domain.replace(/\./g, '-')}`)) {
                            const detailsRow = document.createElement('tr');
                            detailsRow.id = `whois-row-${domain.replace(/\./g, '-')}`;
                            detailsRow.style.display = 'none';
                            
                            let detailsHTML = `
                                <td colspan="4">
                                    <div class="whois-details" id="whois-details-${domain.replace(/\./g, '-')}">
                                        <h3>WHOIS详细信息</h3>
                            `;
                            
                            for (const [key, value] of Object.entries(data.whois_info)) {
                                detailsHTML += `
                                    <div class="detail-row">
                                        <span class="detail-label">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                                        <span class="detail-value">${value}</span>
                                    </div>
                                `;
                            }
                            
                            detailsHTML += `
                                    </div>
                                </td>
                            `;
                            
                            detailsRow.innerHTML = detailsHTML;
                            resultRow.parentNode.insertBefore(detailsRow, resultRow.nextSibling);
                        } else {
                            // 更新现有的WHOIS详情
                            const detailsDiv = document.getElementById(`whois-details-${domain.replace(/\./g, '-')}`);
                            let detailsHTML = `<h3>WHOIS详细信息</h3>`;
                            
                            for (const [key, value] of Object.entries(data.whois_info)) {
                                detailsHTML += `
                                    <div class="detail-row">
                                        <span class="detail-label">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                                        <span class="detail-value">${value}</span>
                                    </div>
                                `;
                            }
                            
                            detailsDiv.innerHTML = detailsHTML;
                        }
                    }
                    statCell.innerHTML = content;
                } else {
                    statCell.className = 'uncertain';
                    statCell.innerHTML = `${data.结果} <span class="method-badge">${data.方法}</span>`;
                }
                
                // 更新注册日期
                dateCell.textContent = data.创建日期 || '-';
            })
            .catch(error => {
                cell.innerHTML = originalContent + '<br><span style="color:red">检测出错，请重试</span>';
                console.error('检测出错:', error);
            });
        }
    </script>
</body>
</html>
'''

@app.route('/', methods=['GET'])
async def index():
    keyword = request.args.get('keyword', '')
    domain_results = {}
    related_results = {}
    
    if keyword:
        # 异步查询域名
        loop = asyncio.get_event_loop()
        domain_results = await check_domains_async(keyword)
        related_results = await check_related_domains_async(keyword)
    
    return render_template_string(HTML_TEMPLATE, 
                                keyword=keyword,
                                domain_results=domain_results, 
                                related_results=related_results)

@app.route('/api/check', methods=['GET'])
async def api_check_domain():
    keyword = request.args.get('keyword', '')
    
    if not keyword:
        return jsonify({"error": "请提供关键词"}), 400
    
    # 异步查询域名
    domain_results = await check_domains_async(keyword)
    related_results = await check_related_domains_async(keyword)
    
    return jsonify({
        "keyword": keyword,
        "domain_results": domain_results,
        "related_results": related_results
    })

@app.route('/api/recheck', methods=['GET'])
async def recheck_domain():
    """单独检查一个域名，支持选择检查方法"""
    domain = request.args.get('domain', '')
    method = request.args.get('method', 'dns')  # 默认使用DNS方法
    
    if not domain:
        return jsonify({"error": "请提供域名"}), 400
    
    if method == 'dns':
        result = check_domain_fast(domain)
    elif method == 'whois':
        result = check_domain_with_whois(domain)
    else:
        return jsonify({"error": "不支持的检测方法"}), 400
    
    return jsonify(result)

# 使Flask支持异步路由
def run_async_app():
    app.run(debug=True, host='0.0.0.0', port=9000)

if __name__ == '__main__':
    import nest_asyncio
    nest_asyncio.apply()  # 允许在同一事件循环中嵌套asyncio
    run_async_app()
