import whois
import time
import socket
import requests
import re
import dns.resolver
from concurrent.futures import Thread<PERSON>oolExecutor

def check_domain_with_whois(domain_name):
    """使用whois库检查域名是否被注册（最权威的方法）"""
    start_time = time.time()
    try:
        domain = whois.whois(domain_name)
        
        # 更详细的WHOIS结果分析
        if domain.status is None and domain.creation_date is None and domain.expiration_date is None:
            result = "域名可以注册"
            status = False
        else:
            # 检查是否有注册日期或到期日期
            if domain.creation_date or domain.expiration_date:
                result = f"域名已被注册 (创建日期: {domain.creation_date})"
                status = True
            else:
                result = "域名已被注册"
                status = True
    except Exception as e:
        if "No match for domain" in str(e):
            result = "域名可以注册 (WHOIS无匹配记录)"
            status = False
        else:
            result = f"WHOIS查询出错: {str(e)}"
            status = None  # 不确定状态
    
    end_time = time.time()
    execution_time = end_time - start_time
    return {
        "方法": "WHOIS查询",
        "结果": result,
        "状态": status,  # True=已注册, False=未注册, None=不确定
        "执行时间": f"{execution_time:.2f}秒",
        "可信度": 90  # WHOIS是最权威的方法
    }

def check_domain_with_dns(domain_name):
    """使用DNS查询检查域名是否有DNS记录"""
    start_time = time.time()
    status = None
    
    try:
        # 尝试多种DNS记录类型
        records = []
        for record_type in ['A', 'MX', 'NS', 'CNAME', 'TXT']:
            try:
                answers = dns.resolver.resolve(domain_name, record_type)
                if answers:
                    records.append(f"{record_type}记录")
            except (dns.resolver.NoAnswer, dns.resolver.NXDOMAIN, dns.resolver.NoNameservers):
                continue
            except Exception:
                continue
        
        if records:
            result = f"域名已被注册并有DNS记录 ({', '.join(records)})"
            status = True
        else:
            # 尝试使用socket
            socket.gethostbyname(domain_name)
            result = "域名已被注册并有DNS记录"
            status = True
    except socket.gaierror:
        # 无法解析可能意味着域名未注册或没有DNS记录
        result = "域名可能未被注册或没有DNS记录"
        status = False
    except Exception as e:
        result = f"DNS查询出错: {str(e)}"
        status = None
    
    end_time = time.time()
    execution_time = end_time - start_time
    return {
        "方法": "DNS查询",
        "结果": result,
        "状态": status,
        "执行时间": f"{execution_time:.2f}秒",
        "可信度": 70  # DNS查询可靠性较高但不如WHOIS
    }

def check_domain_with_http(domain_name):
    """使用HTTP请求检查域名是否有网站"""
    start_time = time.time()
    status = None
    
    try:
        # 尝试HTTPS和HTTP
        for protocol in ['https', 'http']:
            try:
                response = requests.get(f"{protocol}://{domain_name}", 
                                       timeout=3, 
                                       headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code < 400:
                    result = f"域名已被注册且有活跃网站 ({protocol})"
                    status = True
                    break
                elif 400 <= response.status_code < 500:
                    result = f"域名已被注册但网站可能有问题 (状态码: {response.status_code})"
                    status = True
                    break
            except requests.RequestException:
                continue
        else:  # 如果for循环正常结束（没有break）
            result = "域名可能未被注册或没有活跃网站"
            status = False
    except Exception as e:
        result = f"HTTP请求出错: {str(e)}"
        status = None
    
    end_time = time.time()
    execution_time = end_time - start_time
    return {
        "方法": "HTTP请求",
        "结果": result,
        "状态": status,
        "执行时间": f"{execution_time:.2f}秒",
        "可信度": 50  # HTTP请求可靠性较低
    }

def check_domain_availability(domain_name):
    """使用多种方法检查域名并返回综合结果"""
    print(f"正在检查域名: {domain_name}")
    
    # 使用线程池并行执行三种检查方法
    with ThreadPoolExecutor(max_workers=3) as executor:
        whois_future = executor.submit(check_domain_with_whois, domain_name)
        # dns_future = executor.submit(check_domain_with_dns, domain_name)
        # http_future = executor.submit(check_domain_with_http, domain_name)
        
        results = [
            whois_future.result(),
            # dns_future.result(),
            # http_future.result()
        ]
    
    # 综合判断域名状态
    registered_votes = 0
    unregistered_votes = 0
    total_confidence = 0
    
    for result in results:
        if result["状态"] is True:  # 已注册
            registered_votes += result["可信度"]
        elif result["状态"] is False:  # 未注册
            unregistered_votes += result["可信度"]
        
        if result["状态"] is not None:
            total_confidence += result["可信度"]
    
    # 计算综合结果
    if total_confidence > 0:
        registered_percentage = (registered_votes / total_confidence) * 100
        
        if registered_percentage >= 70:
            final_status = "已注册"
            confidence = registered_percentage
        elif registered_percentage <= 30:
            final_status = "未注册"
            confidence = 100 - registered_percentage
        else:
            final_status = "状态不确定"
            confidence = 50
    else:
        final_status = "无法确定"
        confidence = 0
    
    # 添加综合结果到返回值
    results.append({
        "方法": "综合判断",
        "结果": f"域名{final_status} (可信度: {confidence:.1f}%)",
        "状态": final_status,
        "执行时间": "N/A",
        "可信度": confidence
    })
    
    return results

def generate_domain_variants(base_domain):
    """生成不同后缀的域名变体"""
    # 常见域名后缀列表
    common_tlds = [
        "com", "net", "org", "io", "ai", "cn", "co", 
        "tech", "app", "dev", "info", "biz", "me", 
        "xyz", "site", "online", "store", "shop"
    ]
    
    # 处理输入的域名,去除可能已有的后缀
    if "." in base_domain:
        parts = base_domain.split(".")
        if len(parts) >= 2 and parts[-1].lower() in common_tlds:
            base_name = ".".join(parts[:-1])
        else:
            base_name = base_domain
    else:
        base_name = base_domain
    
    # 生成不同后缀的域名列表
    domain_variants = [f"{base_name}.{tld}" for tld in common_tlds]
    
    return domain_variants

def generate_related_keywords(base_domain):
    """生成与给定域名相关的关联提示词"""
    # 移除可能的TLD后缀，获取基础名称
    if "." in base_domain:
        base_name = base_domain.split(".")[0]
    else:
        base_name = base_domain
    
    # 常见前缀 - 扩展更多有价值的前缀
    prefixes = [
        # 基础前缀
        "my", "the", "best", "top", "get", "try", "use", "pro", "smart", "easy", "quick", "fast",
        # 扩展前缀
        "go", "join", "new", "next", "first", "prime", "pure", "true", "just", "all", "one", "our",
        "meta", "ultra", "super", "hyper", "micro", "mini", "mega", "max", "peak", "elite", "premium",
        "instant", "daily", "weekly", "global", "local", "mobile", "pocket", "power", "bright", "fresh"
    ]
    
    # 常见后缀 - 扩展更多有价值的后缀
    suffixes = [
        # 基础后缀
        "hub", "spot", "center", "place", "space", "zone", "app", "tool", "box", "kit", "ware", "tech", "solutions", "group",
        # 扩展后缀
        "pro", "plus", "max", "boost", "genius", "master", "expert", "guru", "ninja", "wizard", "hero", "star",
        "base", "core", "central", "direct", "express", "fast", "now", "today", "24", "365", "world", "global",
        "nation", "city", "town", "valley", "park", "bay", "port", "bridge", "connect", "link", "net", "web",
        "cloud", "store", "shop", "market", "mart", "depot", "hq", "labs", "works", "studio", "factory", "forge"
    ]
    
    # 相关行业术语（根据基础名称可能的领域调整）- 扩展更多行业和领域
    industry_terms = {
        # 技术相关
        "ai": ["intelligence", "smart", "neural", "machine", "learning", "cognitive", "robot", "automation", "data", "predict", "brain", "mind", "think", "logic", "deep", "vision", "voice", "chat", "gpt", "ml"],
        "tool": ["utility", "kit", "gadget", "device", "instrument", "equipment", "resource", "solution", "assist", "helper", "aid", "support", "craft", "maker", "build", "create", "fix", "repair"],
        "tech": ["technology", "digital", "innovation", "software", "hardware", "system", "platform", "solution", "future", "modern", "advanced", "cutting", "edge", "frontier", "pioneer", "breakthrough"],
        "app": ["application", "software", "program", "platform", "service", "solution", "system", "mobile", "desktop", "web", "cloud", "native", "hybrid", "interface", "experience", "interactive"],
        "web": ["website", "internet", "online", "digital", "cyber", "network", "site", "browser", "surf", "domain", "host", "server", "portal", "page", "responsive", "dynamic", "static", "content"],
        "data": ["analytics", "insights", "metrics", "statistics", "database", "storage", "warehouse", "lake", "mining", "processing", "visualization", "dashboard", "report", "monitor", "track", "measure"],
        "cloud": ["server", "storage", "computing", "saas", "paas", "iaas", "hosting", "virtual", "remote", "distributed", "scalable", "elastic", "flexible", "secure", "backup", "sync"],
        "crypto": ["blockchain", "token", "coin", "wallet", "mining", "defi", "nft", "exchange", "trade", "ledger", "hash", "smart", "contract", "decentralized", "peer", "secure"],
        
        # 商业相关
        "shop": ["store", "market", "mall", "boutique", "retail", "commerce", "buy", "sell", "deal", "discount", "sale", "bargain", "price", "cart", "checkout", "purchase", "order", "delivery"],
        "finance": ["money", "bank", "invest", "wealth", "capital", "fund", "asset", "stock", "market", "trade", "exchange", "crypto", "payment", "transaction", "budget", "save", "loan", "credit"],
        "business": ["company", "corp", "enterprise", "startup", "venture", "firm", "agency", "office", "work", "job", "career", "professional", "service", "consult", "manage", "strategy", "growth"],
        "market": ["sell", "buy", "trade", "exchange", "auction", "deal", "offer", "demand", "supply", "price", "value", "worth", "cost", "retail", "wholesale", "consumer", "customer", "client"],
        
        # 内容相关
        "blog": ["journal", "diary", "news", "article", "post", "content", "media", "write", "read", "story", "narrative", "chronicle", "record", "log", "update", "publish", "author", "editor"],
        "media": ["video", "audio", "photo", "image", "picture", "music", "sound", "podcast", "stream", "broadcast", "channel", "studio", "production", "creative", "content", "entertainment"],
        "social": ["network", "community", "group", "forum", "chat", "message", "share", "connect", "friend", "follow", "like", "comment", "post", "profile", "status", "feed", "trending", "viral"],
        "content": ["create", "curate", "manage", "publish", "distribute", "share", "engage", "analyze", "optimize", "strategy", "plan", "calendar", "schedule", "editorial", "creative", "original"],
        
        # 生活相关
        "game": ["play", "gaming", "entertainment", "fun", "interactive", "arcade", "player", "level", "score", "win", "compete", "challenge", "adventure", "action", "strategy", "puzzle", "sport", "team"],
        "edu": ["education", "learning", "teaching", "school", "academy", "training", "course", "study", "knowledge", "skill", "lesson", "class", "lecture", "seminar", "workshop", "tutor", "mentor", "coach"],
        "health": ["wellness", "fitness", "medical", "care", "healthy", "life", "body", "mind", "nutrition", "diet", "exercise", "workout", "therapy", "healing", "medicine", "doctor", "clinic", "hospital"],
        "food": ["eat", "cook", "recipe", "meal", "dish", "cuisine", "taste", "flavor", "kitchen", "chef", "restaurant", "cafe", "bakery", "organic", "fresh", "healthy", "gourmet", "delicious"],
        "travel": ["trip", "journey", "tour", "explore", "adventure", "discover", "destination", "vacation", "holiday", "booking", "reservation", "hotel", "flight", "cruise", "guide", "map", "local", "global"],
        "home": ["house", "living", "interior", "exterior", "design", "decor", "furniture", "garden", "kitchen", "bathroom", "bedroom", "office", "smart", "secure", "comfort", "cozy", "modern", "traditional"]
    }
    
    # 流行趋势词
    trend_terms = [
        "ai", "gpt", "chat", "crypto", "nft", "meta", "verse", "web3", "defi", "dao", 
        "sustainable", "green", "eco", "carbon", "zero", "neutral", 
        "remote", "virtual", "hybrid", "digital", "smart", "connected", 
        "creator", "influencer", "content", "stream", "live", "social"
    ]
    
    # 地域相关词汇
    geo_terms = [
        "global", "world", "international", "local", "national", "regional",
        "asia", "euro", "america", "pacific", "africa", "china", "usa", "uk", "india"
    ]
    
    # 数字组合
    number_combinations = [
        "24", "365", "247", "360", "180", "720", "1000", "100", "101", "202", "404",
        "2023", "2024", "2025", "123", "321", "007", "911", "112", "999", "888", "777", "555", "333", "111"
    ]
    
    # 简短、易记的组合
    short_combinations = [
        "go", "io", "me", "my", "we", "us", "it", "in", "on", "up", "to", "by", "at",
        "ez", "ok", "hi", "hey", "now", "yes", "pro", "top", "max", "best"
    ]
    
    related_keywords = []
    
    # 1. 添加前缀组合
    for prefix in prefixes:
        related_keywords.append(f"{prefix}{base_name}")
    
    # 2. 添加后缀组合
    for suffix in suffixes:
        related_keywords.append(f"{base_name}{suffix}")
    
    # 3. 添加行业相关术语
    # 尝试找出基础名称可能属于的行业
    potential_industries = []
    for industry, terms in industry_terms.items():
        if industry in base_name.lower():
            potential_industries.append(industry)
        for term in terms:
            if term in base_name.lower():
                potential_industries.append(industry)
                break
    
    # 如果没有找到明确的行业，使用一些通用术语
    if not potential_industries:
        potential_industries = ["tech", "app", "tool", "business", "web"]  # 默认行业
    
    # 为每个可能的行业添加相关术语
    for industry in potential_industries:
        for term in industry_terms.get(industry, []):
            if term != base_name.lower():  # 避免重复
                related_keywords.append(f"{base_name}{term}")
                related_keywords.append(f"{term}{base_name}")
    
    # 4. 添加流行趋势词组合
    for term in trend_terms:
        if term.lower() != base_name.lower():  # 避免重复
            related_keywords.append(f"{base_name}{term}")
            related_keywords.append(f"{term}{base_name}")
    
    # 5. 添加地域相关词汇组合
    for term in geo_terms:
        related_keywords.append(f"{base_name}{term}")
        related_keywords.append(f"{term}{base_name}")
    
    # 6. 添加数字组合
    for num in number_combinations:
        related_keywords.append(f"{base_name}{num}")
    
    # 7. 添加短组合
    for short in short_combinations:
        if short.lower() != base_name.lower():  # 避免重复
            related_keywords.append(f"{short}{base_name}")
            related_keywords.append(f"{base_name}{short}")
    
    # 8. 添加一些常见的变体
    variants = [
        f"{base_name}online",
        f"{base_name}digital",
        f"{base_name}web",
        f"{base_name}net",
        f"{base_name}cloud",
        f"{base_name}pro",
        f"{base_name}plus",
        f"{base_name}hub",
        f"i{base_name}",
        f"e{base_name}",
        f"a{base_name}",
        f"{base_name}24",
        f"{base_name}365",
        f"{base_name}now",
        f"{base_name}today",
        f"{base_name}hq",
        f"{base_name}app",
        f"{base_name}site",
        f"{base_name}world",
        f"get{base_name}",
        f"go{base_name}",
        f"try{base_name}",
        f"use{base_name}",
        f"your{base_name}",
        f"my{base_name}",
        f"the{base_name}",
        f"{base_name}team",
        f"{base_name}group",
        f"{base_name}club",
        f"{base_name}community"
    ]
    related_keywords.extend(variants)
    
    # 9. 添加缩写和简写形式
    # 如果域名长度大于5，尝试生成缩写
    if len(base_name) > 5:
        # 元音字母缩写 (去除元音)
        vowels = "aeiou"
        no_vowels = ''.join([c for c in base_name if c.lower() not in vowels])
        if len(no_vowels) >= 3:
            related_keywords.append(no_vowels)
        
        # 首字母缩写 (如果有多个单词)
        if "-" in base_name or "_" in base_name or " " in base_name:
            words = re.split(r'[-_ ]', base_name)
            if len(words) > 1:
                acronym = ''.join([word[0] for word in words if word])
                if len(acronym) >= 2:
                    related_keywords.append(acronym)
        
        # 截断版本
        if len(base_name) > 8:
            related_keywords.append(base_name[:8])
        if len(base_name) > 6:
            related_keywords.append(base_name[:6])
        if len(base_name) > 4:
            related_keywords.append(base_name[:4])
    
    # 移除重复项、过长项和非法字符
    cleaned_keywords = []
    for kw in related_keywords:
        # 清理关键词 (移除非字母数字字符)
        cleaned = re.sub(r'[^a-zA-Z0-9]', '', kw)
        # 只保留合理长度的关键词 (2-63个字符)
        if 2 <= len(cleaned) <= 63:
            cleaned_keywords.append(cleaned.lower())
    
    # 移除重复项并排序
    related_keywords = sorted(list(set(cleaned_keywords)))
    
    # 按长度排序 (优先显示较短的域名)
    related_keywords.sort(key=len)
    
    return related_keywords

def test_domain_availability(domain_input=None):
    """测试域名可用性,支持多后缀检查"""
    if domain_input is None:
        domain_input = input("请输入要检查的域名(不含后缀将检查多个后缀): ")
        if not domain_input:
            domain_input = "aitool"  # 默认域名基础名称
    
    # 检查是否需要生成多个后缀变体
    if "." in domain_input and any(domain_input.endswith(f".{tld}") for tld in ["com", "net", "org", "io", "ai", "cn"]):
        # 用户输入了完整域名,只检查这一个
        domains_to_check = [domain_input]
    else:
        # 用户输入了基础名称,生成多个后缀变体
        domains_to_check = generate_domain_variants(domain_input)
        print(f"将检查以下{len(domains_to_check)}个域名变体:")
        for d in domains_to_check:
            print(f"- {d}")
        print()
    
    # 存储所有域名的结果
    all_results = {}
    
    # 检查每个域名
    for domain in domains_to_check:
        print(f"\n===== 检查域名: {domain} =====")
        results = check_domain_availability(domain)
        
        print("\n域名检查结果:")
        for result in results:
            if "执行时间" in result and result["执行时间"] != "N/A":
                print(f"{result['方法']}: {result['结果']} (耗时: {result['执行时间']})")
            else:
                print(f"{result['方法']}: {result['结果']}")
        
        # 获取综合判断结果
        final_result = results[-1]
        print(f"\n最终判断: {final_result['结果']}")
        
        # 存储结果
        all_results[domain] = final_result["状态"]
    
    # 显示所有域名的综合结果
    if len(domains_to_check) > 1:
        print("\n\n===== 所有域名检查结果汇总 =====")
        available_domains = []
        registered_domains = []
        uncertain_domains = []
        
        for domain, status in all_results.items():
            if status == "未注册":
                available_domains.append(domain)
            elif status == "已注册":
                registered_domains.append(domain)
            else:
                uncertain_domains.append(domain)
        
        if available_domains:
            print("\n可注册的域名:")
            for domain in available_domains:
                print(f"- {domain}")
        
        if registered_domains:
            print("\n已被注册的域名:")
            for domain in registered_domains:
                print(f"- {domain}")
        
        if uncertain_domains:
            print("\n状态不确定的域名:")
            for domain in uncertain_domains:
                print(f"- {domain}")
    
    return all_results

def test_related_keywords(domain_input=None):
    """测试域名关联提示词生成和检查"""
    if domain_input is None:
        domain_input = input("请输入要生成关联提示词的域名: ")
        if not domain_input:
            domain_input = "aitool"  # 默认域名基础名称
    
    # 提取基础域名
    base_domain = domain_input.split(".")[0] if "." in domain_input else domain_input
    
    print(f"\n===== 为 '{base_domain}' 生成域名关联提示词 =====")
    related_keywords = generate_related_keywords(base_domain)
    
    # 对关联词进行分类展示
    print(f"\n为 '{base_domain}' 生成的关联提示词:")
    
    # 按长度分组
    short_keywords = [kw for kw in related_keywords if len(kw) <= 6]
    medium_keywords = [kw for kw in related_keywords if 7 <= len(kw) <= 10]
    long_keywords = [kw for kw in related_keywords if len(kw) > 10]
    
    if short_keywords:
        print("\n简短域名 (推荐):")
        # 每行显示8个关联词
        for i in range(0, len(short_keywords), 8):
            print(", ".join(short_keywords[i:i+8]))
    
    if medium_keywords:
        print("\n中等长度域名:")
        # 每行显示5个关联词
        for i in range(0, len(medium_keywords), 5):
            print(", ".join(medium_keywords[i:i+5]))
    
    if long_keywords:
        print("\n较长域名:")
        # 每行显示3个关联词
        for i in range(0, len(long_keywords), 3):
            print(", ".join(long_keywords[i:i+3]))
    
    # 随机选择一些关联词进行可注册性检查
    import random
    # 修复这里：确保sample_size不大于要抽样的集合大小
    if short_keywords:
        sample_size = min(5, len(short_keywords))
    else:
        sample_size = min(5, len(related_keywords))
        
    if sample_size > 0:
        print("\n\n===== 随机检查部分关联域名可注册性 =====")
        sampled_keywords = random.sample(short_keywords if short_keywords else related_keywords, sample_size)
        
        sampled_results = {}
        for keyword in sampled_keywords:
            # 检查.com域名
            domain_to_check = f"{keyword}.com"
            print(f"\n检查域名: {domain_to_check}")
            results = check_domain_availability(domain_to_check)
            final_result = results[-1]
            print(f"结果: {final_result['结果']}")
            sampled_results[domain_to_check] = final_result["状态"]
        
        # 显示可注册的域名
        available_domains = [domain for domain, status in sampled_results.items() if status == "未注册"]
        if available_domains:
            print("\n可注册的域名:")
            for domain in available_domains:
                print(f"- {domain}")
    
    print(f"\n共生成 {len(related_keywords)} 个关联提示词 (短域名: {len(short_keywords)}, 中等长度: {len(medium_keywords)}, 长域名: {len(long_keywords)})")
    
    return related_keywords

# 使用示例
if __name__ == "__main__":
    """
    对标网站: https://namae.dev/s/aitool
    1. 工具类网站-查询域名
    2. 域名的注册可信度;
    3. 域名的详细信息;
    4. 关联域名的提供-查询结果;
    5. 域名注册服务;
    """
    print("===== 域名检查工具 =====")
    print("1. 检查域名可用性(支持多后缀)")
    print("2. 生成域名关联提示词")
    print("3. 同时执行两种检查")
    
    choice = input("\n请选择功能(1/2/3): ")
    
    domain_input = input("\n请输入域名(不输入则使用默认域名'aitool'): ")
    if not domain_input:
        domain_input = "aitool"  # 默认域名基础名称
    
    if choice == "1":
        # 只检查域名可用性
        test_domain_availability(domain_input)
    elif choice == "2":
        # 只生成关联提示词
        test_related_keywords(domain_input)
    else:
        # 默认执行两种检查
        print("\n===== 第一步: 检查域名可用性 =====")
        test_domain_availability(domain_input)
        
        print("\n\n===== 第二步: 生成域名关联提示词 =====")
        test_related_keywords(domain_input)