import requests
import json

def fetch_entries():
    url = "https://api.follow.is/entries"
    
    headers = {
        "cookie":"authjs.csrf-token=74ce5eaa012f2dad1ce767bbba60ad8699638c16b3d1b793d883d84890ad73ea%7C289134df609bbb11bb6cba2cc40adca2261092f6ea004c6fd92e5ee8ee0f23a8; authjs.callback-url=https%3A%2F%2Fapp.follow.is; authjs.session-token=261a07ad-7304-488e-ad91-dffb84d651de; ph_phc_EZGEvBt830JgBHTiwpHqJAEbWnbv63m5UpreojwEWNL_posthog=%7B%22distinct_id%22%3A%2201929fea-ab43-7615-a5eb-acbcfeb8aace%22%2C%22%24sesid%22%3A%5B1729259945095%2C%2201929fea-ab40-7288-b6fd-b729782f1444%22%2C1729259809600%5D%7D",
        "accept": "application/json",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "origin": "https://app.follow.is",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-app-dev": "0",
        "x-app-version": "0.0.1-alpha.21"
    }
    
    payload = {
        "publishedAfter": "2024-10-08T19:50:33.187Z",
        "isArchived": False,
        "view": 2,
        "listId": "67805406301694976",
        "csrfToken": "74ce5eaa012f2dad1ce767bbba60ad8699638c16b3d1b793d883d84890ad73ea"
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        print(data)
        if data["code"] == 0:
            return data["data"]
        else:
            print(f"请求失败，错误代码: {data['code']}")
            return None
    
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")
        return None

def main():
    entries = fetch_entries()
    if entries:
        for entry in entries:
            print(json.dumps(entry, ensure_ascii=False, indent=2))
    else:
        print("未获取到数据")

if __name__ == "__main__":
    main()

