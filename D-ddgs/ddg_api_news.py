import requests
import json
import os

'''
核心项目: https://github.com/deedy5/duckduckgo_search?tab=readme-ov-file#6-news---news-search-by-duckduckgocom
'''


def search_with_duckduckgo(query):
    duckduckgo_api_url = "https://ddg.search2ai.online/searchNews"
    body = {
        "q": query,
        "max_results": os.getenv("MAX_RESULTS", "10")
    }
    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(duckduckgo_api_url, headers=headers, data=json.dumps(body))
        response.raise_for_status()  # 如果响应状态码不是200，则抛出异常

        data = response.json()
        results = [
            {
                "title": item["title"],
                "link": item["url"],
                "snippet": item["body"]
            }
            for item in data["results"]
        ]
        return results
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
    except ValueError as e:
        print(f"JSON解析错误: {e}")
        return None


# 示例使用
# query = "Python programming"
# query = "柯洁，十年磨一剑，剑剑必封喉！6.5目，力斩韩春兰杯冠军卞相壹"
# query="5 NBA Free Agents Eyeing One Last Big Contract"
query = "Where the 2024 'Steph Curry Game' Ranks Among All-Time Great Team USA Performances"
results = search_with_duckduckgo(query)
if results:
    print(json.dumps(results, indent=4))
