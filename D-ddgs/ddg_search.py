from duckduckgo_search import DDGS


class DDGSTextSearch:
    @staticmethod
    def run(query, region="wt-wt", safesearch="off", timelimit=None, max_results=5):
        """执行文本搜索
        Args:
            query: 搜索关键词
            region: 地区代码，如 wt-wt, us-en, uk-en, ru-ru 等
            safesearch: 安全搜索，可选值：on, moderate, off
            timelimit: 时间限制，可选值：d(天), w(周), m(月), y(年)
            max_results: 最大结果数
        """
        results = DDGS().text(query, region=region, safesearch=safesearch, 
                            timelimit=timelimit, max_results=max_results)
        for result in results:
            print(result)


class DDGSNews:
    @staticmethod
    def run(keywords, region="wt-wt", safesearch="off", timelimit="w", max_results=5):
        """搜索新闻
        Args:
            keywords: 搜索关键词
            region: 地区代码
            safesearch: 安全搜索级别
            timelimit: 时间限制，可选值：d(天), w(周), m(月)
            max_results: 最大结果数
        """
        results = DDGS().news(keywords=keywords, region=region, safesearch=safesearch, 
                            timelimit=timelimit, max_results=max_results)
        for result in results:
            print(result)


class DDGSImages:
    @staticmethod
    def run(keywords, region="wt-wt", safesearch="moderate", size=None, color=None, 
           type_image=None, layout=None, license_image=None, max_results=10, timelimit=None):
        """搜索图片
        Args:
            keywords: 搜索关键词
            region: 地区代码，如 wt-wt, us-en, uk-en, ru-ru 等
            safesearch: 安全搜索级别：on, moderate, off
            timelimit: Day, Week, Month, Year
            size: Small, Medium, Large, Wallpaper
            color: color, Monochrome, Red, Orange, Yellow, Green, Blue,
                  Purple, Pink, Brown, Black, Gray, Teal, White
            type_image: photo, clipart, gif, transparent, line
            layout: Square, Tall, Wide
            license_image: any (All Creative Commons), Public (PublicDomain),
                         Share (Free to Share and Use), ShareCommercially (Free to Share and Use Commercially),
                         Modify (Free to Modify, Share, and Use), ModifyCommercially (Free to Modify, Share, and
                         Use Commercially)
            max_results: 最大结果数
        """
        results = DDGS().images(
            keywords=keywords,
            region=region,
            safesearch=safesearch,
            size=size,
            color=color,
            type_image=type_image,
            layout=layout,
            license_image=license_image,
            max_results=max_results,
            timelimit=timelimit,
        )
        for result in results:
            print(result)


class DDGSChat:
    @staticmethod
    def run(query, model='claude-3-haiku'):
        """使用聊天功能
        Args:
            query: 聊天问题
            model: AI模型，默认使用 claude-3-haiku
        """
        results = DDGS().chat(query, model=model)
        print(results)


class DDGSVideos:
    @staticmethod
    def run(keywords, region="wt-wt", safesearch="off", timelimit=None, resolution=None, 
           duration=None, license_videos=None, max_results=5):
        """搜索视频
        Args:
            keywords: 搜索关键词
            region: 地区代码，如 wt-wt, us-en, uk-en, ru-ru 等
            safesearch: 安全搜索级别：on, moderate, off
            timelimit: 时间限制：d(天), w(周), m(月)
            resolution: 分辨率：high, standard
            duration: 时长：short, medium, long
            license_videos: 许可证类型：creativeCommon, youtube
            max_results: 最大结果数
        """
        results = DDGS().videos(
            keywords=keywords,
            region=region,
            safesearch=safesearch,
            timelimit=timelimit,
            resolution=resolution,
            duration=duration,
            license_videos=license_videos,
            max_results=max_results
        )
        for result in results:
            print(result)


if __name__ == "__main__":
    # 测试各种搜索功能
    query = "对话王星、嘉嘉：逃离妙瓦底"
    
    # print("\n=== 测试文本搜索 ===")
    # DDGSTextSearch.run(query, timelimit="m")  # 搜索一个月内的文本
    
    # print("\n=== 测试新闻搜索 ===")
    # DDGSNews.run(query, timelimit="w")  # 搜索一周内的新闻
    
    print("\n=== 测试图片搜索 ===")
    DDGSImages.run(
        keywords=query,
        safesearch="off",
        size="Large",      # 首字母大写
        color=None,
        type_image="photo",
        layout=None,
        license_image="any",
        max_results=5,
        timelimit="Month"  # 首字母大写
    )
    
    print("\n=== 测试视频搜索 ===")
    DDGSVideos.run(
        keywords=query,
        safesearch="off",
        timelimit="w",  # 一周内的视频
        resolution="high",  # 高清视频
        duration="medium",  # 中等时长
        license_videos="youtube",  # YouTube许可
        max_results=5
    )
    
    print("\n=== 测试聊天功能 ===")
    DDGSChat.run(query + "新闻分析")
