
import requests

# 目标URL - 这里应该是你想要发送POST请求的实际API地址
url = f"https://html.duckduckgo.com/html/"


# 请求头部信息 - 根据API的要求来设置
headers = {
    "Content-Type": "application/json",
}

# 请求体 - 这里的内容需要根据API的要求来填写
payload = {
    "key1": "value1",
    "key2": "value2",
}

# 发送POST请求
response = requests.post(url, json=payload, headers=headers)

# 检查响应状态码
if response.status_code == 200:
    # 处理成功的响应
    print("Success:", response.json())
else:
    # 处理失败的响应
    print("Error:", response.status_code, response.text)
