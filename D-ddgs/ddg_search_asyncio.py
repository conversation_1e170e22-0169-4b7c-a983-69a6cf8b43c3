import asyncio
from duckduckgo_search import AsyncDDGS
# def news(
#     keywords: str,
#     region: str = "wt-wt",
#     safesearch: str = "moderate",
#     timelimit: Optional[str] = None,
#     max_results: Optional[int] = None,
# ) -> List[Dict[str, str]]:
#     """DuckDuckGo news search. Query params: https://duckduckgo.com/params.
    
#     Args:
#         keywords: keywords for query.
#         region: wt-wt, us-en, uk-en, ru-ru, etc. Defaults to "wt-wt".
#         safesearch: on, moderate, off. Defaults to "moderate".
#         timelimit: d, w, m. Defaults to None.
#         max_results: max number of results. If None, returns results only from the first response. Defaults to None.
    
#     Returns:
#         List of dictionaries with news search results.
#     """
async def main():
    # 初始化 AsyncDDGS 对象
    ddgs = AsyncDDGS()
    
    # 异步文本搜索
    results = await ddgs.atext("巴黎奥运会", max_results=5)
    print(results)
    # 异步图片搜索
    # images = await ddgs.aimages("python异步logo", max_results=3)
    # print(images)
    
    
    
asyncio.run(main())