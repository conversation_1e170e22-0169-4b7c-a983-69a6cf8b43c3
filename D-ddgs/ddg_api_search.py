import requests
import json

# DuckDuckGo API URL
duckDuckGoApiUrl = "https://ddg.search2ai.online/search"
# duckDuckGoApiUrl = "https://ddg.search2ai.online/searchNews"

# 测试查询
# query = "柯洁，十年磨一剑，剑剑必封喉！6.5目，力斩韩春兰杯冠军卞相壹"
query = "Where the 2024 'Steph Curry Game' Ranks Among All-Time Great Team USA Performances"

# 测试最大结果数
MAX_RESULTS = 5

# 请求体
body = {
    "q": query,
    "max_results": MAX_RESULTS
}

# 发送POST请求
response = requests.post(duckDuckGoApiUrl, headers={"Content-Type": "application/json"}, data=json.dumps(body))

# 检查请求是否成功
if response.status_code == 200:
    # 解析返回的JSON数据
    data = response.json()
    # 打印结果
    for item in data['results']:
        print("Title:", item['title'])
        print("Link:", item['href'])
        print("Snippet:", item['body'])
        print("---")
else:
    print("Failed to retrieve data:", response.status_code)
