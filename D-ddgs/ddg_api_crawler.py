import requests
import json
'''
核心项目: https://jina.ai/reader/#apiform
'''
def crawler(url):
    print(f"正在使用 URL 进行自定义爬取: {json.dumps(url)}")
    try:
        response = requests.post('https://crawler.search2ai.one',
                                 headers={"Content-Type": "application/json"},
                                 data=json.dumps({"url": url}))

        if response.status_code != 200:
            print(f"API 请求失败, 状态码: {response.status_code}")
            return f"API 请求失败, 状态码: {response.status_code}"

        content_type = response.headers.get("content-type")
        if not content_type or 'application/json' not in content_type:
            print("收到的响应不是有效的 JSON 格式")
            return "收到的响应不是有效的 JSON 格式"

        data = response.json()
        print('自定义爬取服务调用完成')
        print(data['title'])
        print(data['content'])
        return json.dumps(data)
    except Exception as e:
        print(f"在 crawler 函数中捕获到错误: {e}")
        return f"在 crawler 函数中捕获到错误: {e}"

# query='https://www.toutiao.com/article/7376212134885212713/?log_from=2fa3aa47cec3f_1717425288362'
# query='https://mp.weixin.qq.com/s/0dasSuaZFnhJXjo8P1T-9g'
query='https://mp.weixin.qq.com/s/UidrX5sLYXg-FRie5tZ9LQ'
print(crawler(query))