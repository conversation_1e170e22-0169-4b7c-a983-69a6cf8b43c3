
from util import notion_util,time_util

from dotenv import load_dotenv
import os
from llm import llm_poe

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_MRRS = os.environ.get("NOTION_DATABASE_MRRS")
NOTION_DATABASE_DWFB = os.environ.get("NOTION_DATABASE_DWFB")
notion_mrrs = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_MRRS)
notion_dwfb = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_DWFB)
    


def save_notion_dwfb():
    """同步短文发布-微头条发布"""
    while True:
        settings = llm_poe.get_settings()  # 获取剩余积分
        if settings is None: break
        message_point_balance = settings['messagePointInfo']['messagePointBalance']
        if int(message_point_balance) < 500:
            print(f'poe 积分不足，暂停执行')
            break

        day = time_util.get_yesterday(1)
        params = {
            'Tags': '初始化',
            'yesterday': day,
            'datasource': '头条', #TODO-其他带有图片的类型
        }
        contentDict = notion_mrrs.get_content_by_condition_redian(params)
        if contentDict is None:
            break
        if 'image_url' not in contentDict: continue
        try:
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            image_url = contentDict['image_url']
            page_title = contentDict['page_title']
            print(f'待处理: page_chinese_title:{page_title},page_url:{page_url}')

            # 保存到另一个DB中
            content=llm_poe.call_content_common(query=page_title, origin_url=page_url, area='微热点')
            if content is None or len(content)<80: 
                notion_mrrs.update_page_content_keji(page_id=page_id, properties_params="发布失败")
                continue
            
            # 6. 记录到Notion中
            content=f'{content} #{page_title}'
            if image_url is None:
                page = {
                    "title": page_title,
                    'content': content,
                    'area': '热点',
                    'hmcturl': page_url,
                    # 'picture_url': image_url
                }
            else:
                page = {
                    "title": page_title,
                    'content': content,
                    'area': '热点',
                    'hmcturl': page_url,
                    'picture_url': image_url
                }
        
            newPage = notion_dwfb.create_page_duanwen(page=page)
            new_page_id = newPage['id']
            notion_mrrs.update_page_content_keji(page_id=page_id, properties_params="微头条发布成功")
            print(f'Save Notion: pageId: {new_page_id},title: {page_title}')
        except Exception as e:
            print(f'热点微头条异常:{e}')

def main():
    try:
        # 1.2 同步存储微头条手动内容
        save_notion_dwfb()
        print('程序结束')
    except Exception as e:
        print(f"程序异常:{e}")


if __name__ == "__main__":
    """
     1. 热点新闻同步手写notion，发布微头条
    """
    main()