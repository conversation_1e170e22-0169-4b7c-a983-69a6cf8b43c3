import os
import re

from dotenv import load_dotenv

import gzh_article_clean_tools
from llm import llm_poe
from util import feed_util, article_util, re_util
from util import notion_util
from util import system_util
from util import time_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)


class TiYu:
    def __init__(self):
        print("TiYu init start")

    def _init_params(self):
        print("TiYu init start")
        content = self._get_news()
        self.title = content['title']
        self.page_url = content['link']
        self.id = content['id']
        self.published_at = time_util.formatted_date(content['published'])

    def _get_news(self):
        content = feed_util.check_feed_bleacherreport()  # 英文标题需要翻译
        return content

    def _get_news_list(self):
        contents = feed_util.check_feed_kejixun_list()
        return contents

    def _get_news_ai(slef):
        from util import kejixun_util
        return kejixun_util.get_kejixun_ai()

    def _get_general_word(self, title: str, content: str, file_path: None, image_urls: list):
        """生成对应Word文档"""
        try:
            at = article_util.article()
            print('生成Word中...')
            file_path = f'{file_path}{title}.docx'
            at.create_word_doc_content_keji(file_path=file_path, contents=content, image_urls=image_urls)
            return True
        except Exception as e:
            print(f'生成文档失败:{e}')
            return False


def save_notion():
    # # 1. 获取订阅Rss最新数据
    ty = TiYu()
    contents = ty._get_news_list()
    contents_ai = ty._get_news_ai()
    contents.extend(contents_ai)

    if contents is None: return
    ids = feed_util.get_exist_ids()
    for content in contents:
        id = content['id']
        if id in ids: continue
        image_url = content['image_url']
        if image_url is None or len(image_url) == 0: continue
        ty.page_url = content['link']
        ty.id = id
        if len(content['published']) == 0: continue
        if '年' in content['published']:
            ty.published_at = time_util.convert_to_iso_date_string(content['published'])
        else:
            ty.published_at = time_util.data_format(content['published'])
        ty.title = content['title']
        # 保留字段,目前不用!
        text_content = f'[{ty.page_url}]联网检索相关信息，保证信息的准确性！重新生成新闻文章,保证标题吸引力， 文章的开篇段落要注明新闻对应北京时间多少和新闻报道来源出处，不需要小标题，段落适当位置用Bing image搜索添加相关图片，确保图片可以正常浏览！全文至少1000字'
        # # 6. 记录到Notion中
        page = {
            "title": ty.title,
            'chinese_title': ty.title,
            'prompt': text_content,
            'id': ty.id,
            'area': 'keji',
            'datasource': 'kejixun.com',
            'hmcturl': ty.page_url,
            'published_at': ty.published_at,
            'picture_url': image_url,
            'tag': content['tag']
        }
        try:
            newPage = notion.create_page_keji(page=page)
            page_id = newPage['id']
            print(f'Save Notion: ID:{ty.id},pageId: {page_id},title: {ty.title}')
            feed_util.save_fail_ids(ty.title, ty.id, 'success')
        except Exception as e:
            print(e)
            continue


from datetime import datetime, timedelta


def get_yesterday(days: int):
    # 获取当前时间
    current_time = datetime.now()
    # 计算前一天的时间
    yesterday = current_time - timedelta(days=days)
    formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
    print(formatted_time)
    return formatted_time

from util.time_util import TimeoutUtils
@TimeoutUtils.timeout(timeout_seconds=1200) #超过二十分钟返回None
def general_word_auto():
    """自动生成文章-llm大模型"""
    # 1. Notion获取条件数据
    day = get_yesterday(3)
    contentDict = notion.get_content_by_condition_keji(day)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_chinese_title = contentDict['page_chinese_title']
    page_url = contentDict['page_url']
    image_url = contentDict['image_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_chinese_title:{page_chinese_title},page_url:{page_url}')

    # 2. 检索图片
    imagelist = []
    imagelist.append(image_url)
    from util import bingImage_util
    images = bingImage_util.search_images(search_term=page_title, count=5)
    imagelist.extend(images)

    # 3. 生成内容:
    area = '科技'
    content = llm_poe.call_content_common(query=page_chinese_title, origin_url=page_url, area=area)
    if content is None or len(content) == 0: return False
    title, content = re_util.handle_content(text=content)
    title = re.sub(r'[^\w\s,，、!！？？]', '', title)

    # 二次处理图片
    pic_list = re_util.get_img_urls(content)  # 图片数据源
    if len(pic_list) == 0: pic_list.extend(imagelist)

    # 4.生成word文档:
    # markdown_content = notion.retrieve_and_convert_to_markdown(block_id=page_id)
    ty = TiYu()
    os_type = system_util.get_os()
    file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'
    flag = ty._get_general_word(title=title, content=content, file_path=file_path, image_urls=pic_list)
    if flag:
        notion.update_page_content(page_id=page_id, properties_params='格式化')
    else:
        notion.update_page_content(page_id=page_id, properties_params='发布失败')


def main():
    print('开始执行: 科技.py')
    '''
    1. 订阅所有rss数据,保存到Notion中
    2. 手动复制Prompt-coze-NBA生成图文,粘贴到Block中
    3. 筛选文章生成文档Word
    4. 影刀自动发布
    '''
    # 1. 保存所有Rss数据到Notion
    save_notion()
    # 2. 筛选标识-手动文章-生成Word
    # general_word_auto()

    # 2.1 筛选标识-手动文章-生成Word
    for i in range(3):
        general_word_auto()


if __name__ == '__main__':
    main()

    gzh_article_clean_tools.clean()
