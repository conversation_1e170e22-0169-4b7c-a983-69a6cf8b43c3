from notion_client import Client


class notion_client:
    def __init__(self, token, data_base_id):
        """
        初始化
        """
        self.global_notion = Client(auth=token)
        self.global_database_id = data_base_id
        print('开始Notion自动化获取数据...')

    """
    获取所有页面
    """

    def get_all_pages(self, database_id):
        results = []
        start_cursor = None

        while True:
            response = self.global_notion.databases.query(
                database_id=database_id,
                start_cursor=start_cursor,
                page_size=100,  # Maximum page size
            )
            results.extend(response['results'])
            # temp 处理重复数据
            self.delete_duplicate_page(results, "id")

            start_cursor = response.get('next_cursor')
            if not start_cursor:
                break

        return results

    """
    删除页面内容
    """

    def delete_page_content(self, page_id):
        try:
            del_block = self.global_notion.blocks.delete(block_id=page_id)
            print(del_block['id'])
        except Exception as e:
            print(e)
            return None

    def trash_page_content(self, page_id):
        result = self.global_notion.pages.update(page_id=page_id, in_trash=True)
        print(result['id'])

    """
    删除重复的页面-保留最新的页面
    """

    def delete_duplicate_page(self, page_list, property_name):
        property_name_set = set()
        for page in page_list:
            if page["object"] == "page":
                for key, value in page["properties"].items():
                    if key == property_name:
                        # 获取富文本类型的值
                        if len(value['rich_text'])==0:continue
                        text_value = value['rich_text'][0]['text']['content']
                        if text_value in property_name_set:
                            print(page["id"])
                            try:
                                self.delete_page_content(page["id"])
                                # self.trash_page_content(page["id"])
                            except Exception as e:
                                print(f'删除异常: {e}')
                        else:
                            property_name_set.add(text_value)


def main():
    """
      1. 清理冗余重复数据 头条数据  根据唯一ID处理
      """
    data_base_id = '424704b86446424cb5765fdd46e1642f'  # NBA-data
    token = "**************************************************"
    notion = notion_client(token=token, data_base_id=data_base_id)
    notion.get_all_pages(notion.global_database_id)
    print('Notion清理结束...')


if __name__ == '__main__':
    main()
