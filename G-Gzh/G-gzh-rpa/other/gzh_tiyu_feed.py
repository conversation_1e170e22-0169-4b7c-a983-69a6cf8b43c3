import os
import re
import time
from dotenv import load_dotenv

from llm import llm_coze
from llm import llm_poe
from util import feed_util, article_util, re_util
from util import notion_util
from util import time_util
from util import transaction_tengxun_util
from util import system_util
from util import file_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
NOTION_DATABASE_DWFB = os.environ.get("NOTION_DATABASE_DWFB")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)
notion_dwfb = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_DWFB)


class TiYu:
    def __init__(self):
        print("TiYu init start")

    def _init_params(self):
        print("TiYu init start")
        content = self._get_news()
        self.title = content['title']
        self.page_url = content['link']
        self.id = content['id']
        self.published_at = time_util.formatted_date(content['published'])

    def _get_news(self):
        content = feed_util.check_feed_bleacherreport()  # 英文标题需要翻译
        return content

    def _get_news_list(self):
        espn_contents = feed_util.check_feed_espn_list()
        contents = feed_util.check_feed_bleacherreport_list()
        if espn_contents is None and contents is None: return None
        espn_contents.extend(contents)
        return espn_contents

    def _get_llm_content(self, page_url: str):
        text_content = f'[{page_url}]联网检索相关信息，重新生成新闻文章, 不需要小标题，段落适当位置用Bing image搜索添加相关图片，全文至少1000字'
        content = llm_coze.call_content_common(query=text_content, area='NBA')
        return content

    def _get_general_word(self, title: str, content: str, file_path: None, image_urls: list):
        try:
            at = article_util.article()
            print('生成Word中...')
            file_path = f'{file_path}{title}.docx'
            at.create_word_doc_content_tiyu(file_path=file_path, contents=content, image_urls=image_urls)
            return True
        except Exception as e:
            print(f'生成文档失败:{e}')
            return False


def save_notion():
    # # 1. 获取订阅Rss最新数据
    ty = TiYu()
    contents = ty._get_news_list()
    if contents is None or len(contents) == 0: return
    for content in contents:
        if content.get('image_url') is None: continue
        image_url = content['image_url']
        if image_url is None or len(image_url) == 0: continue
        if 'bleacherreport.com' in content['link']:
            datasource = 'bleacherreport.com'
            ty.published_at = content['published']
        elif 'espn.com' in content['link']:
            datasource = 'espn.com'
            ty.published_at = time_util.convert_est_to_cst(content['published'])
        ty.page_url = content['link']
        ty.id = content['id']
        ty.title = content['title']
        title = transaction_tengxun_util.translate_text(ty.title)
        print(f'\n中文标题:{title}')

        # 保留字段,目前不用!
        # text_content = f'[{ty.page_url}]联网检索相关信息，保证信息的准确性！重新生成新闻文章,保证标题吸引力， 文章的开篇段落要注明新闻对应北京时间多少和新闻报道来源出处，不需要小标题，段落适当位置用Bing image搜索添加相关图片，确保图片可以正常浏览！全文至少1000字'
        text_content = f'请充当文案高手。写作时，请保留原文的主题，并使用更加口水文和更强烈的情感表达，尤其是第一句。请勿打印文本以外的内容。中文输出时，字数限制在 150 字以内。忠实于原文的精髓，但要增强口语化的魅力和情感冲击力，尤其是一开始。没有多余的文字，只有纯粹的力量。最后一行内容和读者互动，保持在 150 字以内，末尾添加标签，使用中文，断行以增强感染力。内容是：[{ty.page_url}]'

        # # 6. 记录到Notion中
        page = {
            "title": ty.title,
            'chinese_title': title,
            'prompt': text_content,
            'id': ty.id,
            'area': 'tiyu',
            'datasource': datasource,
            'hmcturl': ty.page_url,
            'published_at': ty.published_at,
            'picture_url': image_url
        }
        try:
            newPage = notion.create_page(page=page)
            page_id = newPage['id']
            print(f'Save Notion: ID:{ty.id},pageId: {page_id},title: {title}')
            feed_util.save_fail_ids(ty.title, ty.id, 'success')
        except Exception as e:
            print(e)
            continue

def general_word_auto():
    """TODO:目前生成文章内容和新闻不符,修改"""
    """自动生成文章-llm大模型"""
    # 1. Notion获取条件数据
    day = time_util.get_yesterday(2)
    params = {
        'Tags': '初始化',
        'yesterday': day,
        'area': 'tiyu',
    }
    contentDict = notion.get_content_by_condition_nba(params)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_chinese_title = contentDict['page_chinese_title']
    page_url = contentDict['page_url']
    image_url = contentDict['image_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_chinese_title:{page_chinese_title},page_url:{page_url}')

    # 2. 检索图片
    imagelist = []
    imagelist.append(image_url)
    from util import bingImage_util
    images = bingImage_util.search_images(search_term=page_title, count=5)
    imagelist.extend(images)

    # 获取原文内容
    # from util import reader_ai_util
    # data=reader_ai_util.parse_url_data(url=page_url)
    # origin_title = data['title']
    # origin_content = data['content']
    # origin_content=get_weixin_html(url=page_url)

    # 3. 生成内容:
    area = '体育'
    content = llm_poe.call_content_common(query=page_title, origin_url=page_url, area=area)
    if content is None or len(content) == 0: return False
    title, content = re_util.handle_content(text=content)
    title = re.sub(r'[^\w\s,，、!！？？]', '', title)

    # 二次处理图片
    pic_list = re_util.get_img_urls(content)  # 图片数据源
    if len(pic_list) == 0: pic_list.extend(imagelist)

    # 4.生成word文档:
    os_type = system_util.get_os()
    file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'
    ty = TiYu()
    flag = ty._get_general_word(title=title, content=content, file_path=file_path, image_urls=pic_list)
    notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")


def run_notion_three():
    file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if system_util.get_os == "Windows" else f'/Volumes/文章存档/{area}/待发布/'
    max_iterations = 10
    iteration_count = 0

    while iteration_count < max_iterations:
        docx_count = 0
        for root, dirs, files in os.walk(file_path):
            if '待发布' in root and '头条待发布' not in root:
                docx_count += sum(1 for file in files if file.endswith('.docx'))

        if docx_count >= 4:
            print(f"文件数量足够 ({docx_count} 个 .docx 文件)")
            break  # Exit the loop if the file count is sufficient

        message = f"子目录中的 .docx 文件数量不足 4 个,只有 {docx_count} 个。<br><br>"
        print(message)
        main()
        iteration_count += 1
    if iteration_count == max_iterations:
        print("已达到最大迭代次数,但文件数量仍然不足。")

def save_notion_dwfb():
    """同步短文发布-微头条发布"""
    while True:
        day = time_util.get_yesterday(1)
        params = {
            'Tags': '初始化',
            'yesterday': day,
            'area': 'tiyu',
        }
        contentDict = notion.get_content_by_condition_nba(params)
        if contentDict is None:
            break
        page_id = contentDict['page_id']
        page_chinese_title = contentDict['page_chinese_title']
        page_url = contentDict['page_url']
        image_url = contentDict['image_url']
        page_title = contentDict['page_title']
        print(f'待处理: page_chinese_title:{page_chinese_title},page_url:{page_url}')

        # 保存到另一个DB中
        content=llm_poe.call_content_common(query=page_title, origin_url=page_url, area='微体育')
        if content is None: return
        
        # 6. 记录到Notion中
        page = {
            "title": page_title,
            'content': content,
            'area': '体育',
            'hmcturl': page_url,
            'picture_url': image_url
        }
        try:
            newPage = notion_dwfb.create_page_duanwen(page=page)
            new_page_id = newPage['id']
            # notion.update_page_content(page_id=page_id, properties_params="微头条发布成功")
            notion.update_page_properties(page_id=page_id,tags='微头条发布成功', area='tiyu')
            print(f'Save Notion: pageId: {new_page_id},title: {page_title}')
        except Exception as e:
            print(e)
    # notion.update_page_content(page_id=page_id, properties_params="微头条发布成功" if flag else "微头条发布失败")




def main():
    '''
    1. 订阅所有rss数据,保存到Notion中
    2. 手动复制Prompt-coze-NBA生成图文,粘贴到Block中
    3. 筛选文章生成文档Word
    4. 影刀自动发布
    '''
    try:
        print('开始:gzh_tiyu_feed.py')

        settings = llm_poe.get_settings()  # 获取剩余积分
        if settings is None: return None

        # 1. 保存所有Rss数据到Notion
        save_notion()
        # 1.2 同步存储微头条手动内容
        save_notion_dwfb()


        # 2.1 筛选标识-手动文章-生成Word
        destination_folder = "/Volumes/文章存档/体育/待发布/"  # 目标文件夹
        while True:
            docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
            if len(docx_files) < 4:  # 文件小于4篇持续
                general_word_auto()
            else:
                print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
                break
            time.sleep(1)  # Wait for 1 second before checking again


        # 2.2 移动福利一篇到这里:
        source_folder = "/Volumes/文章存档/美女/待发布/"
        latest_docx = file_util.find_latest_docx(source_folder)
        file_util.move_file(latest_docx, destination_folder)

        # general_word_auto()
        print('结束:gzh_tiyu_feed.py')
    except Exception as e:
        print(f"体育异常:{e}")


area = '体育'
if __name__ == '__main__':
    main()
    # save_notion()
    # save_notion_dwfb()
