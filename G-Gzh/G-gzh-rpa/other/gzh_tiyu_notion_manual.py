from llm import llm_coze
from util import feed_util, article_util, re_util
from util import notion_util
from util import time_util


class TiYu:
    def __init__(self):
        print("TiYu init start")

    def _init_params(self):
        print("TiYu init start")
        content = self._get_news()
        self.title = content['title']
        self.page_url = content['link']
        self.id = content['id']
        self.published_at = time_util.formatted_date(content['published'])

    def _get_news(self):
        content = feed_util.check_feed_bleacherreport()  # 英文标题需要翻译
        return content

    def _get_news_list(self):
        contents = feed_util.check_feed_bleacherreport_list()
        return contents

    def _get_llm_content(self, page_url: str):
        text_content = f'[{page_url}]联网检索相关信息，重新生成新闻文章, 不需要小标题，段落适当位置用Bing image搜索添加相关图片，全文至少1000字'
        content = llm_coze.call_content_common(query=text_content, area='NBA')
        return content

    def _get_general_word(self, title: str, content: str, file_path: None, area: str):
        at = article_util.article()
        if file_path is None: file_path = f'/Volumes/文章存档/{area}/待发布/'  # 局域网同步

        pic_list = re_util.get_img_urls(content)  # 图片数据源
        # content = re_util.get_content(content)  # 纯文档
        print('生成Word中...')
        file_path = f'{file_path}{title}.docx'
        at.create_word_doc_content_tiyu(file_path=file_path, contents=content, image_urls=pic_list)


def general_word():
    ty = TiYu()
    token = "**************************************************"
    database_id = '424704b86446424cb5765fdd46e1642f'  # NBA-data
    notion = notion_util.notion_client(token=token, database_id=database_id)
    day = time_util.get_yesterday(2)
    params = {
        'Tags': '手动文章',
        'yesterday': day,
        # 'area': 'chuangye',
        # 'readcount': 1000,
        # 'author': '大橘创业说',
    }
    contentDict = notion.get_content_by_condition_nba(params)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_chinese_title = contentDict['page_chinese_title']
    page_url = contentDict['page_url']
    image_url = contentDict['image_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_chinese_title:{page_chinese_title},page_url:{page_url}')

    # page_id, page_chinese_title,page_url = notion.get_content_by_condition_nba()
    # if page_id is None: return
    # print(f'page_id:{page_id},page_chinese_title:{page_chinese_title}')

    # 生成word文档:
    markdown_content = notion.retrieve_and_convert_to_markdown(block_id=page_id)
    # from util import docx_util
    # output_docx_file='/Users/<USER>/Downloads/fwh.docx'
    # docx_util.markdown_to_docx_from_string(markdown_content,output_docx_file)
    area = '体育'
    ty._get_general_word(title=page_chinese_title, content=markdown_content, file_path=None, area=area)
    notion.update_page_content(page_id=page_id, properties_params='格式化')


'''
1. 订阅所有rss数据,保存到Notion中
2. 手动复制Prompt-coze-NBA生成图文,粘贴到Block中
3. 筛选文章生成文档Word
4. 影刀自动发布
'''


def main():
    # 3. 筛选文章生成文档Word  两步分开执行
    # general_word()

    for i in range(4):
        general_word()  # 针对Tags:手动文章-生成


if __name__ == '__main__':
    main()
