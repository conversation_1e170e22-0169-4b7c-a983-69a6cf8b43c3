import requests
from re import findall
from bs4 import BeautifulSoup
import time
import os
from util import article_util
from util import system_util
from util import github_util
from util import docx_util
from llm import llm_poe
import re
from util import system_util
from util import re_util
from util import request_util
from llm import llm_coze
from util import notion_util
from util import time_util


def general_qinggan(page_title: str, page_url: str, area: str):
    # 3. 生成内容:
    content = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
    if content is None or len(content) < 100: return False
    title, content = re_util.handle_content(text=content)
    new_weixin_title = re.sub(r'[^\w\s,，、"!！？？]', '', title)

    # 4. 转换成word文档
    try:
        os_type = system_util.get_os()
        output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
        at = article_util.article()
        """注意:图片素材来源"""
        temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\插画宝宝' if os_type == "Windows" else f'/Volumes/文章存档/Images/插画宝宝'

        at.create_word_doc_content_qinggan(file_path=output_docx, contents=content, temp_dir=temp_dir)
        print()
        print("原标题：《" + page_title + "》")
        print("新标题：《" + new_weixin_title + "》")
        print('下载的Word文件:' + output_docx)
        return True
    except Exception as e:
        print(f'文档异常:{e}')
        return False


token = "**************************************************"
database_id = "c495d8a81ed54703b333d2404e1c625a"  # 易撰-Data
notion = notion_util.notion_client(token=token, database_id=database_id)


def get_url_from_notion():
    day = time_util.get_yesterday(30)
    params = {
        'yesterday': day,
        'area': 'lvyou',
        'readcount': 1000,
        'author': '六六旅行官',
    }
    contentDict = notion.get_content_by_condition_qinggan(params=params)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_url = contentDict['page_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_url:{page_url}')
    return page_id, page_title, page_url, notion


def main(weixin_url: None):
    '''1. 主要用于生成旅行文档'''
    # 1. 获取数据源
    if weixin_url is None:
        page_id, page_title, page_url, notion = get_url_from_notion()
        if page_id is None: return

    # 2. 生成文案
    result = general_qinggan(page_title=page_title, page_url=page_url, area='星座')

    # 3. 更新数据源状态
    if result:
        notion.update_page_content(page_id=page_id, properties_params='格式化')
    else:
        notion.update_page_content(page_id=page_id, properties_params='生成失败')


if __name__ == "__main__":
    """旅行"""
    for i in range(10):
        main(None)

    import gzh_article_clean_tools

    gzh_article_clean_tools.clean()
