import os
import sys
import textwrap

from util import email_utils

sys.path.append(os.path.dirname(__file__))


class article:
    def __init__(self) -> None:
        print("article init")
        pass

    def delete_empty_docs_in_folder(self, folder_path):
        # 遍历文件夹中的所有文件
        for filename in os.listdir(folder_path):
            # if filename.endswith('.doc'):
            file_path = os.path.join(folder_path, filename)
            # 如果文件小于10kb
            threshold = 10 * 1024
            file_size = os.path.getsize(file_path)
            if file_size == 0 or file_size < threshold or filename == '.DS_Store':
                # 删除空文件
                os.remove(file_path)
                print(f"Deleted empty .doc file: {file_path}")

    def delete_empty_folder(self, folder_path):
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            # 如果是文件夹就删除
            if os.path.isdir(file_path):
                os.rmdir(file_path)
                print(f"Deleted empty folder: {file_path}")


def main():
    data = [
        {'keji': '科技'},
        {'tiyu': '体育'},
        {'tiyu': '体育01'},
        {'shehui': '社会'},
        {'qinggan': '情感'},
        {'yule': '娱乐'},
        {'sannong': '三农'},
        {'wenan': '文案'},
        {'touxiang': '头像'},
        {'touxiang': '创业'},
        {'touxiang': '育儿'},
        {'touxiang': '旅游'},
        {'touxiang': '美女'},
        {'touxiang': '星座'}
    ]
    at = article()
    from util import system_util
    os_type = system_util.get_os()
    # 遍历数据
    for item in data:
        for key, value in item.items():
            print(f"键: {key}, 值: {value}")
            area = value
            save_file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}' if os_type == "Windows" else f'/Volumes/文章存档/{area}'  # 局域网同步
            at.delete_empty_folder(os.path.join(save_file_path, '待发布'))
            at.delete_empty_docs_in_folder(os.path.join(save_file_path, '待发布'))
    print("删除空文件夹和空文件完成")


def find_and_remove_ds_store_files(directory):
    """清理DS_Store"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file == ".DS_Store":
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    print(f"已删除文件: {file_path}")
                except Exception as e:
                    print(f"删除文件 {file_path} 时出错: {e}")

            file_path = os.path.join(root, file)
            try:
                file_size = os.path.getsize(file_path)
                if file_size == 0:
                    os.remove(file_path)
                    print(f"已删除空文件: {file_path}")
            except Exception as e:
                print(f"删除文件 {file_path} 时出错: {e}")

def delete_small_jpg_files(directory, size_threshold=70 * 1024):
    """
    递归遍历指定目录及其子目录，删除小于指定大小的JPG文件。

    :param directory: 要遍历的目录路径
    :param size_threshold: 文件大小阈值（以字节为单位），默认100KB
    """
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.jpg') or file.lower().endswith('.png'):
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                if file_size < size_threshold:
                    print(f"Deleting {file_path} (size: {file_size} bytes)")
                    os.remove(file_path)

def delete_small_docx_files(directory, size_threshold=100 * 1024):
    """
    递归遍历指定目录及其子目录，删除小于指定大小的DOCX文件。

    :param directory: 要遍历的目录路径
    :param size_threshold: 文件大小阈值（以字节为单位），默认100KB
    """
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.docx') or file.lower().endswith('.doc'):
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                if file_size < size_threshold:
                    print(f"Deleting {file_path} (size: {file_size} bytes)")
                    os.remove(file_path)



def check():
    """判断文件夹内的docx文件数量是否大于4"""
    dir_paths = [
        '/Volumes/文章存档/',
    ]
    string = []
    for dir_path in dir_paths:
        for root, dirs, files in os.walk(dir_path):
            if '待发布' in root and '头条待发布' not in root:
                docx_count = sum(1 for file in files if file.endswith('.docx'))
                if docx_count < 4:
                    message = f"子目录 {root} 中的.docx文件数量不足4个,只有 {docx_count} 个。<br><br>"
                    print(message)
                    string.append(message)

    if string:
        email_content = ''.join(string)
        formatted_email_content = textwrap.fill(email_content, width=80, replace_whitespace=False)
        email_utils.send_email("待发布目录不足4篇", formatted_email_content)
    else:
        print("所有待发布目录的.docx文件数量都足够。")
        
def clean():
    dirs = [
        '/Volumes/文章存档/',
    ]
    for dir in dirs:
        #1. 删除文件,防止自动化异常
        find_and_remove_ds_store_files(dir)
        delete_small_docx_files(directory=dir)

    # 2. 删除小于100KB的jpg文件
    # dir = '/Volumes/文章存档/Images/9223/'
    # dir = '/Volumes/文章存档/Images/'
    # delete_small_jpg_files(dir)

    # 3. 校验文章数量发邮件
    check()



if __name__ == '__main__':
    clean()
