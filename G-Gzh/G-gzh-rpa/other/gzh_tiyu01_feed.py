import os

from dotenv import load_dotenv

from util import notion_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)

from util import file_util
import time


def check_and_move_docx(source_folder, destination_folder):
    """持续移动文件到指定文件夹"""
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        if len(docx_files) < 3:
            try:
                latest_docx = file_util.find_latest_docx(source_folder)
                file_util.move_file(latest_docx, destination_folder)
            except Exception as e:
                print(f"An error occurred: {e}")
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


def run_notion():
    """1. 校验文件夹符合数量 2.不符合移动文件  3.不达标发邮件"""
    destination_folder = "/Volumes/文章存档/体育01/待发布/"  # 目标文件夹
    dir_paths = [
        destination_folder
    ]
    string = []
    for dir_path in dir_paths:
        for root, dirs, files in os.walk(dir_path):
            if '待发布' in root and '头条待发布' not in root:
                docx_count = sum(1 for file in files if file.endswith('.docx'))
                if docx_count < 4:
                    message = f"子目录 {root} 中的.docx文件数量不足4个,只有 {docx_count} 个。<br><br>"
                    print(message)
                    string.append(message)

    if string:
        # 保证一个体育+两个福利的文件
        # 移动文件-体育:
        source_folder = "/Volumes/文章存档/体育/待发布/"
        destination_folder = "/Volumes/文章存档/体育01/待发布/"
        latest_docx = file_util.find_latest_docx(source_folder)
        file_util.move_file(latest_docx, destination_folder)

        # 移动文件-福利:
        source_folder = "/Volumes/文章存档/美女/待发布/"
        destination_folder = "/Volumes/文章存档/体育01/待发布/"
        check_and_move_docx(source_folder, destination_folder)
    else:
        print("所有待发布目录的.docx文件数量都足够。")

    print('体育01-运行结束')


if __name__ == '__main__':
    # main()
    run_notion()

    # import gzh_article_clean_tools
    #
    # gzh_article_clean_tools.clean()
