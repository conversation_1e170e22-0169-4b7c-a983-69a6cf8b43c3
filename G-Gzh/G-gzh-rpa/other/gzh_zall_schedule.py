import os
import time

import schedule

from gzh_chuagnye import run_notion as chuangye_main
# from gzh_keji_feed import main as keji_main
from gzh_keji_gzh import run_notion as keji_main
from gzh_keji_news import main as keji_news_main
from gzh_meinv import run_notion as meinv_main
from gzh_qinggan import run_notion as qinggan_main
from gzh_tiyu01_feed import run_notion as tiyu_main03
from gzh_tiyu_feed import main as tiyu_main
from gzh_tiyu_news import main as tiyu_news_main
from gzh_redian_news import main as redian_main
from gzh_zhichang_news import main as zhichang_news_main
from gzh_touxiangbizhi import run_notion as touxiang_main
from gzh_wenan import run_notion as wenan_main
from gzh_xingzuo import run_notion as xingzuo_main
from gzh_yuer import run_notion as yuer_main
from gzh_zhichang import run_notion as zhichang_main
from llm import llm_poe


def run_notion_foetch(area: str, callback_function):
    """校验文件目录是否足够四篇文章,继续生成"""
    file_path = f'/Volumes/文章存档/{area}/待发布/'
    max_iterations = 10
    iteration_count = 0

    while iteration_count < max_iterations:
        docx_count = 0
        for root, dirs, files in os.walk(file_path):
            if '待发布' in root and '头条待发布' not in root:
                docx_count += sum(1 for file in files if file.endswith('.docx'))

        if area in ['体育', '科技']:  # 实时性文章
            if docx_count >= 4:
                print(f"{area}:文件数量足够 ({docx_count} 个 .docx 文件)")
                break  # Exit the loop if the file count is sufficient
        else:
            if docx_count >= 21:
                print(f"{area}:文件数量足够 ({docx_count} 个 .docx 文件)")
                break  # Exit the loop if the file count is sufficient

        print(f"子目录中的:{area} .docx 文件数量不足 4 个,只有 {docx_count} 个。<br><br>")
        callback_function()
        iteration_count += 1
    if iteration_count == max_iterations:
        print(f"{area}:已达到最大迭代次数,但文件数量仍然不足。")


def run():
    # 1.确认poe链接
    settings = llm_poe.get_settings()  # 获取剩余积分
    if settings is None: return
    function_to_area_mapping = {
        # tiyu_main: '体育',
        tiyu_news_main: '体育',
        # tiyu_main03: '体育',
        # keji_main: '科技',
        # # 上面实时性,下面不需要
        # qinggan_main: '情感',
        # xingzuo_main: '星座',
        # chuangye_main: '创业',
        # touxiang_main: '头像',
        # wenan_main: '文案',
        # yuer_main: '育儿',
        # zhichang_main: '职场',
        meinv_main: '美女'  # Commented out for now
    }
    for function, area in function_to_area_mapping.items():
        run_notion_foetch(area, function)

    import gzh_article_clean_tools
    gzh_article_clean_tools.clean()
    gzh_article_clean_tools.check()


def job():
    print('[程序运行时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    run()
    redian_main() #热点 单独处理
    zhichang_news_main() #职场 单独处理
    keji_news_main()
    print('[程序结束时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


def schedule_job():
    # schedule.every().day.at("08:00").do(job)  # 指定时间触发
    # schedule.every().day.at("13:00").do(job)  # 指定时间触发
    # schedule.every().day.at("23:00").do(job)  # 指定时间触发
    schedule.every(3).hours.do(job)  # 一天六次
    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    print('[公众号文章生成:程序启动...]')
    schedule_job() #定时运行
    # run() #直接运行
