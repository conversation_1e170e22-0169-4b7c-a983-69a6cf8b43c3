import requests
from re import findall
from bs4 import BeautifulSoup
import time
import os
from util import article_util
from util import system_util
from util import github_util
from util import docx_util

import re
from util import system_util

weixin_title = ""
weixin_time = ""


# 获取微信公众号内容,保存标题和时间
def get_weixin_html(url):
    global weixin_time, weixin_title
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    # 获取标题
    temp = soup.find('h1')
    if temp is None:
        res = requests.get(url)
        soup = BeautifulSoup(res.text, "html.parser")
        temp = soup.find('h1')
    weixin_title = temp.string.strip()

    # 使用正则表达式获取时间
    result = findall(r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}', res.text)
    weixin_time = result[0]

    # 获取正文html并修改
    content = soup.find(id='js_content')
    soup2 = BeautifulSoup((str(content)), "html.parser")
    soup2.div['style'] = 'visibility: visible;'
    html = str(soup2)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    result = findall(pattern, html)

    # 将data-src修改为src
    for url in result:
        html = html.replace('data-src="' + url + '"', 'src="' + url + '"')

    return html


# 上传图片至服务器
def download_pic(content, pic_path):
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)
    # 使用正则表达式查找所有需要下载的图片链接
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)

    count = 1
    for index, item in enumerate(pic_list, 1):
        # count = 1
        flag = True
        pic_url = str(item)

        while flag and count <= 10:
            try:
                if pic_url.find('res.wx.qq.com') > 0:
                    flag = False
                    content = content.replace(
                        'https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/Expression/<EMAIL>',
                        '')  # 去除底部图片-Word不需要
                    break
                elif pic_url.find('png') > 0:
                    file_name = str(index) + '.png'

                elif pic_url.find('gif') > 0:
                    file_name = str(index) + '.gif'

                elif pic_url.find('jpg') > 0:
                    file_name = str(index) + '.jpg'
                else:
                    flag = False
                    break

                data = requests.get(pic_url);
                image_path = pic_path + file_name
                with open(pic_path + file_name, "wb") as f:
                    f.write(data.content)

                # 将图片链接替换为本地链接     
                '''
                1. 下载到本地,生成word有图片. 但是HTML文件打开时候Windows无法连接Mac生成的图片路径, 打开Word可以因为已经把图片内置到Word中. 目前是公众号导入Word报错问题
                2. 选择GitHub上传,转换word时候图片无法正常, 但支持HTML复制到公众号
                '''
                content = content.replace(pic_url, image_path)

                # 将图片链接为GitHub图床
                # picture_url = github_util.upload_image_to_github(image_path, None) #TODO: 下载到GitHub上
                # content = content.replace(pic_url, picture_url)

                content = content.replace('&amp;from=appmsg', '')
                content = content.replace('data-src=', 'src=')
                content = content.replace('border-width: 1px;', 'border-width: 2px;')
                content = content.replace('border-color: rgb(149, 55, 52);', 'border-color: rgb(211, 13, 211);')

                flag = False
                print('已下载第' + str(count) + '张图片.')
                count += 1
                time.sleep(1)

            except:
                count += 1
                time.sleep(1)

        if count > 10:
            print("下载出错：", pic_url)

    return content


"""
下载图片和文字内容
1. 输入微信文章链接
2. 保存到绝对路径下
3. 图片和文章HTML和文字TXT格式
"""
from util import request_util
from llm import llm_coze


def get_fengmian(weixin_url: str, save_path: str):
    url = "https://api3.toolnb.com/tools/getWxArticleImages.json"
    data = {"url": weixin_url}
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        # 根据需要添加其他头部信息
        'Cookie': "Hm_lvt_0000a307caa05f3ed0b0941b8609002d=1718981730; _ga=GA1.1.31039878.1718981731; Hm_lpvt_0000a307caa05f3ed0b0941b8609002d=1718982472; _ga_WV8ZJFXX9G=GS1.1.1718981730.1.1.1718982472.0.0.0",
        'Origin': 'https://www.toolnb.com',
        'Referer': 'https://www.toolnb.com'
    }
    res = request_util.PostRequestTool.post(url, data=data, headers=headers)
    if res['code'] == 1:
        data = res['data']
        pic_url = data['url']
        print(pic_url)
        data = requests.get(pic_url);
        with open(save_path, "wb") as f:
            f.write(data.content)


def general_qinggan(page_title: str, page_url: str, area: str,content:str):
    # 2. 本地存储
    os_type = system_util.get_os()
    
    # 1. 获取图片
    from util import parse_toutiao_util
    image_urls, div_text = parse_toutiao_util.toutiao_parese_content_images(url=page_url)
    # image_urls=['https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/d705d84365594040bbf7a80ef287a9ac~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1719995783&x-signature=QVZ9knRGDAidYZsGqX2HFFZUb2c%3D']

    # 2. 标题
    from llm import llm_deepseek
    title = llm_deepseek.call_title(page_title)
    if title is None or len(title) == 0:
        print('内容风控处理')
        return
    title = re.sub(r'^\d+\.?\s*', '', title.split("\n")[1])
    title = title.replace('_', '')
    pattern = r'[^\w\s,，、!！？？]'
    # 使用正则表达式去除特殊标点符号
    new_weixin_title = re.sub(pattern, '', title)
    # print(f'新文章标题是:{title}')

    # 3. 生成内容:
    # from llm import llm_poe
    # content = llm_poe.call_content_common(query=page_title, area=area)

    # 4. 转换成word文档
    output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
    at = article_util.article()
    at.create_word_doc_content(file_path=output_docx, contents=content, image_urls=image_urls, title=new_weixin_title,
                               area=area)

    print()
    print("原标题：《" + page_title + "》")
    print("新标题：《" + new_weixin_title + "》")
    # print("发布时间：" + weixin_time)
    # print("下载到：" + save_file_path)
    print('下载的Word文件:' + output_docx)


from util import notion_util
from util import time_util


def get_url_from_notion():
    token = "**************************************************"
    database_id = "c495d8a81ed54703b333d2404e1c625a"  # 易撰-Data
    notion = notion_util.notion_client(token=token, database_id=database_id)
    day = time_util.get_yesterday(10)
    params = {
        'yesterday': day,
        'area': 'qinggan',
        'readcount': 100000,
        # 'author':'悦尔句子',
    }
    page_id, page_title, page_url = notion.get_content_by_condition_qinggan(params=params)
    if page_id is None: return
    print(f'数据为: {page_title}')
    return page_id, page_title, page_url, notion


'''1. 主要用于生成情感文档'''
def main(weixin_url: None):
    # 1. 获取数据源
    # if weixin_url is None:
    #     page_id, page_title, page_url, notion = get_url_from_notion()
    #     if page_id is None: return

    # 2. 生成文案
    area = '情感'
    page_title='高智商杀夫案揭秘：山西女子如何用一周时间让丈夫“幸福”离世？'
    page_url='https://www.toutiao.com/article/7383503497708175912'
    content="""
婚姻是坟墓,爱情的坟墓。初识兰兰时,张磊绝没想到,年轻貌美的妻子会成为将自己推入坟墓的凶手。

兰兰出身书香门第,温婉贤淑,张磊与她结婚三年,夫妻恩爱,是亲朋好友眼中的模范夫妻。可谁曾想,在兰兰眼中,丈夫却是个"拖油瓶",自己的小家庭,成了通往幸福生活的枷锁。

"亲爱的,过来,我有话要跟你说。"兰兰温柔地唤着丈夫。皎洁的月光下,她的面容显得格外柔和。

张磊放下手中的报纸,笑着走到妻子面前。兰兰伸出葱白的手指,轻轻抚摸着丈夫英俊的面庞,眼中闪烁着异样的光芒。"从明天开始,我要给你一个惊喜,连续七天,我都会给你准备不一样的早餐。"

"真的吗?太好了!"张磊露出惊喜的笑容,他怎会想到,妻子给自己准备的,是一场蓄谋已久的死亡盛宴。

接下来的七天里,张磊每天早上睁开眼,都会发现床头摆着妻子精心准备的爱心早餐。第一天是法式吐司配煎蛋,第二天是芝士蛋饼配培根,第三天是丰盛的英式早餐......兰兰像变了一个人,把张磊宠上了天。张磊沉浸在妻子的温柔乡里,连胃里传来的一阵阵绞痛都变得甜蜜起来。

直到第七天早晨,张磊再也没能睁开眼睛。当警察赶到时,张磊已然断气多时,脸上还带着幸福的微笑。验尸报告显示,张磊是心脏骤停而亡,体内检出大量砷化物。警方很快将目光锁定在了兰兰身上。

经过审讯,真相终于大白。原来,兰兰早就对这段婚姻感到厌倦,却碍于面子不愿离婚。于是,她制定了一个"完美计划",决定让丈夫在幸福中死去。兰兰利用在药厂工作的便利,偷偷购入剧毒的三氧化二砷,连续一周,将其化入丈夫的早餐,剂量由少到多,直至致死。

法庭上,面对铁证如山,兰兰却显得出奇的平静。她面带微笑地说:"我只是让他死得其所,在我的爱意中死去,这是他应得的。"说完,竟眼含热泪,像是回忆起了什么美好的往事。

"爱到深处是杀人。"兰兰最后留下了这样一句话。人性之恶,令人胆寒。张磊就这样死在了妻子精心布置的甜蜜陷阱里,连死前的七天都充满了幸福的错觉。他做梦也没想到,曾经无微不至呵护自己的妻子,竟会成为置他于死地的加害者。

婚姻的面纱被撕碎,爱情的谎言被戳穿。当张磊咽下最后一口掺了砒霜的早餐时,不知他的内心是否依然相信着妻子的爱?他的笑容是因为感受到了兰兰的"温柔",还是对这出荒诞剧终于落下帷幕的解脱?

张磊用生命,验证了哀婚的残酷。他以为可以用爱去捆绑,却没想到等来的是刀锋般的背叛。多少个日日夜夜,他把妻子捧在心尖,到头来却成了兰兰眼中的累赘与障碍。爱情的面具被撕下,他终于看清了所谓婚姻的真面目。

"兰兰,我恨你!更恨我自己......"在生命的最后时刻,不知张磊是否发出了这样的呐喊,为自己的盲目付出感到懊悔,为曾经深信不疑的爱情哀叹。死神降临,带走了他,也带走了那个他以为会白头偕老的女人。

张磊死了,死于妻子的毒计。可同时,他又死于自己的天真,死于对婚姻爱情的盲目迷信。或许,当初选择与兰兰结婚时,他就已经签下了自己的死亡判决书。他无法从兰兰编织的甜蜜陷阱中逃脱,只能在虚幻的幸福泡影中,迎来生命的终结。

这出荒诞的悲剧,是婚姻的讽刺,更是人性的映照。当爱情的面具被撕下,当婚姻的真相大白,我们终将发现,所谓夫妻,不过是一场你死我活的残酷游戏。而游戏的最后,总有一方倒在血泊中,带着无奈的微笑,追悔莫及。

张磊走了,带着对爱情的迷思,对婚姻的幻想,长眠于地下。而兰兰,这个曾经的"贤妻良母",终将在冰冷的铁窗内度过余生,用无尽的懊悔与孤寂偿还自己的罪孽。两个年轻的生命,就这样在婚姻的泥沼中沉沦,上演了一出荒诞而又真实的人间悲剧。

爱情是甜蜜的毒药,婚姻是华丽的牢笼。张磊和兰兰,用鲜血与泪水,诠释了哀婚背后那残酷的真相。或许,惟有挣脱世俗的枷锁,摒弃虚幻的爱情,我们才能寻得心灵的自由,获得生命的解脱。

这个故事告诉我们,婚姻从来不是一片坦途,爱情更非永恒不变。或许,惟有保持警惕,用理性看待感情,我们才能收获幸福.
"""
    general_qinggan(page_title=page_title, page_url=page_url, area=area,content=content)

    # 3. 更新数据源状态
    # notion.update_page_content(page_id=page_id,properties_params='格式化')


if __name__ == "__main__":
    main(None)
