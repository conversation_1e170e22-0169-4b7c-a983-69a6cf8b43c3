import requests
from re import findall
from bs4 import BeautifulSoup
import time
import os
from util import article_util
from util import system_util
from util import github_util
from util import docx_util

import re
from util import system_util

weixin_title = ""
weixin_time = ""


# 获取微信公众号内容,保存标题和时间
def get_weixin_html(url):
    global weixin_time, weixin_title
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    # 获取标题
    temp = soup.find('h1')
    if temp is None:
        res = requests.get(url)
        soup = BeautifulSoup(res.text, "html.parser")
        temp = soup.find('h1')
    weixin_title = temp.string.strip()

    # 使用正则表达式获取时间
    result = findall(r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}', res.text)
    weixin_time = result[0]

    # 获取正文html并修改
    content = soup.find(id='js_content')
    soup2 = BeautifulSoup((str(content)), "html.parser")
    soup2.div['style'] = 'visibility: visible;'
    html = str(soup2)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    result = findall(pattern, html)

    # 将data-src修改为src
    for url in result:
        html = html.replace('data-src="' + url + '"', 'src="' + url + '"')

    return html


# 上传图片至服务器
def download_pic(content, pic_path):
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)
    # 使用正则表达式查找所有需要下载的图片链接
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)

    count = 1
    for index, item in enumerate(pic_list, 1):
        # count = 1
        flag = True
        pic_url = str(item)

        while flag and count <= 10:
            try:
                if pic_url.find('res.wx.qq.com') > 0:
                    flag = False
                    content = content.replace(
                        'https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/Expression/<EMAIL>',
                        '')  # 去除底部图片-Word不需要
                    break
                elif pic_url.find('png') > 0:
                    file_name = str(index) + '.png'

                elif pic_url.find('gif') > 0:
                    file_name = str(index) + '.gif'

                elif pic_url.find('jpg') > 0:
                    file_name = str(index) + '.jpg'
                else:
                    flag = False
                    break

                data = requests.get(pic_url);
                image_path = pic_path + file_name
                with open(pic_path + file_name, "wb") as f:
                    f.write(data.content)

                # 将图片链接替换为本地链接     
                '''
                1. 下载到本地,生成word有图片. 但是HTML文件打开时候Windows无法连接Mac生成的图片路径, 打开Word可以因为已经把图片内置到Word中. 目前是公众号导入Word报错问题
                2. 选择GitHub上传,转换word时候图片无法正常, 但支持HTML复制到公众号
                '''
                content = content.replace(pic_url, image_path)

                # 将图片链接为GitHub图床
                # picture_url = github_util.upload_image_to_github(image_path, None)  # TODO: 下载到GitHub上
                # content = content.replace(pic_url, picture_url)

                content = content.replace('&amp;from=appmsg', '')
                content = content.replace('data-src=', 'src=')
                content = content.replace('border-width: 1px;', 'border-width: 2px;')
                content = content.replace('border-color: rgb(149, 55, 52);', 'border-color: rgb(211, 13, 211);')

                flag = False
                print('已下载第' + str(count) + '张图片.')
                count += 1
                time.sleep(1)

            except:
                count += 1
                time.sleep(1)

        if count > 10:
            print("下载出错：", pic_url)

    return content


"""
下载图片和文字内容
1. 输入微信文章链接
2. 保存到绝对路径下
3. 图片和文章HTML和文字TXT格式
"""
from util import request_util
from llm import llm_coze


def get_fengmian(weixin_url: str, save_path: str):
    url = "https://api3.toolnb.com/tools/getWxArticleImages.json"
    data = {"url": weixin_url}
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        # 根据需要添加其他头部信息
        'Cookie': "Hm_lvt_0000a307caa05f3ed0b0941b8609002d=1718981730; _ga=GA1.1.31039878.1718981731; Hm_lpvt_0000a307caa05f3ed0b0941b8609002d=1718982472; _ga_WV8ZJFXX9G=GS1.1.1718981730.1.1.1718982472.0.0.0",
        'Origin': 'https://www.toolnb.com',
        'Referer': 'https://www.toolnb.com'
    }
    res = request_util.PostRequestTool.post(url, data=data, headers=headers)
    if res['code'] == 1:
        data = res['data']
        pic_url = data['url']
        # print(pic_url)
        data = requests.get(pic_url);
        with open(save_path, "wb") as f:
            f.write(data.content)
        return pic_url


def wenan_general(weixin_url: str, area: str):
    # 1.获取html
    content = get_weixin_html(weixin_url)

    # 2. 本地存储
    os_type = system_util.get_os()
    save_file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\图文下载\\{weixin_title}\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/图文下载/{weixin_title}/'  # 局域网同步
    if not os.path.exists(save_file_path):
        os.makedirs(save_file_path)

    # 2.1 保存封面:
    try:
        fengmian_url=get_fengmian(weixin_url=weixin_url, save_path=save_file_path + '封面.jpg')
    except Exception as e:
        print(f'封面异常:{e}')
    
    # 解析图片url
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)
    pic_list.append(fengmian_url)

    # 2.2 下载图片到本地替换-或者上传到GitHub
    # content = download_pic(content=content, pic_path=save_file_path)  # 下载到本地

    # 公众号-昵称信息替换
    headimg = 'https://cdn.wenhaofree.com/gh/wenhaofree/Image/blog/516edcb9-ec76-4fac-abc8-f58b7536e20e_头像-清风-4k.jpeg'
    id = 'MzkxMDYxNDUwMg=='
    nickname = '清风侃侃'
    signature = '总有清风一两缕，解我十万八千愁'
    replaced_string = re.sub(r'data-headimg="[^"]*"', f'data-headimg="{headimg}"', content)
    replaced_string = re.sub(r'data-id="[^"]*"', f'data-id="{id}"', replaced_string)
    replaced_string = re.sub(r'data-nickname="[^"]*"', f'data-nickname="{nickname}"', replaced_string)
    replaced_string = re.sub(r'data-signature="[^"]*"', f'data-signature="{signature}"', replaced_string)
    content = replaced_string

    # 文案内容-替换
    content = content.replace('关注补充浪漫细胞', '关注清风')
    # pattern = r'<span[^>]*>(.*?)</span>'
    # matches = re.findall(pattern, content)
    # # 打印匹配到的文本
    # for match in matches:
    #     if 'style=' in match or len(match) == 0 or '在看' == match or '最好看' in match:
    #         continue
    #     print(match)
    #     content_text = llm_coze.call_content_common(query=match, area='文案')
    #     if content_text is None: return
    #     content = content.replace(match, content_text)

    # 标题:
    # new_weixin_title = llm_coze.call_content_common(query=weixin_title, area='文案')
    # print(f'原标题:{weixin_title}')
    # print(f'新标题:{new_weixin_title}')
    new_weixin_title=weixin_title

    # 3.保存至本地 文档
    with open(save_file_path + new_weixin_title + '.txt', 'w+', encoding="utf-8") as f:
        f.write(content)
    with open(save_file_path + weixin_title + '.html', 'w+', encoding="utf-8") as f:
        f.write(content)


    soup = BeautifulSoup(content, 'html.parser')
    text_content = soup.get_text()  # 需要\n 作为换行
    text_content = text_content.replace('。', '。\n')
    print(f'正文内容:{text_content}')


    # 4. 转换成word文档
    try:
        at = article_util.article()
        file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'
        # 一个换行添加一张图片
        # title = f'【福利】{weixin_title}'
        file_path = f'{file_path}{new_weixin_title}.docx'
        at.create_word_doc_content_wenan(file_path=file_path, contents=text_content, image_urls=pic_list)

        
        output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
        docx_util.convert_html_to_docx(input_html=save_file_path + weixin_title + '.html', output_docx=output_docx)
    except Exception as e:
        print(f'失败:{e}')
        return False
    print()
    print("标题：《" + weixin_title + "》")
    print("发布时间：" + weixin_time)
    print("下载到：" + save_file_path)
    print('Word文件:' + output_docx)
    return True


from util import notion_util
from util import time_util


def get_url_from_notion():
    token = "**************************************************"
    database_id = "c495d8a81ed54703b333d2404e1c625a"  # 易撰-Data
    notion = notion_util.notion_client(token=token, database_id=database_id)
    day = time_util.get_yesterday(50)
    params = {
        'yesterday': day,
        # 'area': 'wenan',
        'readcount': 100,
        'author': '西海情语馆',
    }
    contents = notion.get_content_by_condition(params=params, start_cursor=None)
    print(f'数据为: {contents}')
    if contents is None: return None, None
    return contents, notion


def main(weixin_url: None):
    '''1. 主要用于生成文案文档'''

    # 1. 获取数据源
    if weixin_url is None:
        contents, notion = get_url_from_notion()
        if contents is None: return
        weixin_url = contents['page_url']

    # 2. 生成文案
    area = '文案'
    flag = wenan_general(weixin_url=weixin_url, area=area)

    # 3. 更新数据源状态

    page_id = contents['page_id']
    if flag:
        # notion.update_page_content(page_id=page_id, properties_params='格式化')
        notion.update_page_properties(page_id=page_id,tags='格式化', area=area)
    else:
        notion.update_page_content(page_id=page_id, properties_params='发布失败')


if __name__ == "__main__":
    # weixin_url = 'https://www.toutiao.com/article/7382851930441155072'
    # main(weixin_url=weixin_url)

    # main(weixin_url=None)

    for i in range(5):
        main(weixin_url=None)

    import gzh_article_clean_tools

    gzh_article_clean_tools.clean()
