import os
import re
import time
from re import findall

import requests
from bs4 import BeautifulSoup
from dotenv import load_dotenv

import gzh_article_clean_tools
from llm import llm_poe
from util import article_util
from util import system_util
from util import re_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")

weixin_title = ""
weixin_time = ""

area = '情感'
os_type = system_util.get_os()
destination_folder = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'


# 获取微信公众号内容,保存标题和时间
def get_weixin_html(url):
    global weixin_time, weixin_title
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    # 获取标题
    temp = soup.find('h1')
    if temp is None:
        res = requests.get(url)
        soup = BeautifulSoup(res.text, "html.parser")
        temp = soup.find('h1')
    weixin_title = temp.string.strip()

    # 使用正则表达式获取时间
    result = findall(r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}', res.text)
    weixin_time = result[0]

    # 获取正文html并修改
    content = soup.find(id='js_content')
    soup2 = BeautifulSoup((str(content)), "html.parser")
    soup2.div['style'] = 'visibility: visible;'
    html = str(soup2)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    result = findall(pattern, html)

    # 将data-src修改为src
    for url in result:
        html = html.replace('data-src="' + url + '"', 'src="' + url + '"')

    return html


# 上传图片至服务器
def download_pic(content, pic_path):
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)
    # 使用正则表达式查找所有需要下载的图片链接
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)

    count = 1
    for index, item in enumerate(pic_list, 1):
        # count = 1
        flag = True
        pic_url = str(item)

        while flag and count <= 10:
            try:
                if pic_url.find('res.wx.qq.com') > 0:
                    flag = False
                    content = content.replace(
                        'https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/Expression/<EMAIL>',
                        '')  # 去除底部图片-Word不需要
                    break
                elif pic_url.find('png') > 0:
                    file_name = str(index) + '.png'

                elif pic_url.find('gif') > 0:
                    file_name = str(index) + '.gif'

                elif pic_url.find('jpg') > 0:
                    file_name = str(index) + '.jpg'
                else:
                    flag = False
                    break

                data = requests.get(pic_url);
                image_path = pic_path + file_name
                with open(pic_path + file_name, "wb") as f:
                    f.write(data.content)

                # 将图片链接替换为本地链接     
                '''
                1. 下载到本地,生成word有图片. 但是HTML文件打开时候Windows无法连接Mac生成的图片路径, 打开Word可以因为已经把图片内置到Word中. 目前是公众号导入Word报错问题
                2. 选择GitHub上传,转换word时候图片无法正常, 但支持HTML复制到公众号
                '''
                content = content.replace(pic_url, image_path)

                # 将图片链接为GitHub图床
                # picture_url = github_util.upload_image_to_github(image_path, None) #TODO: 下载到GitHub上
                # content = content.replace(pic_url, picture_url)

                content = content.replace('&amp;from=appmsg', '')
                content = content.replace('data-src=', 'src=')
                content = content.replace('border-width: 1px;', 'border-width: 2px;')
                content = content.replace('border-color: rgb(149, 55, 52);', 'border-color: rgb(211, 13, 211);')

                flag = False
                print('已下载第' + str(count) + '张图片.')
                count += 1
                time.sleep(1)

            except:
                count += 1
                time.sleep(1)

        if count > 10:
            print("下载出错：", pic_url)

    return content


"""
下载图片和文字内容
1. 输入微信文章链接
2. 保存到绝对路径下
3. 图片和文章HTML和文字TXT格式
"""
from util import request_util


def get_fengmian(weixin_url: str, save_path: str):
    url = "https://api3.toolnb.com/tools/getWxArticleImages.json"
    data = {"url": weixin_url}
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        # 根据需要添加其他头部信息
        'Cookie': "Hm_lvt_0000a307caa05f3ed0b0941b8609002d=1718981730; _ga=GA1.1.31039878.1718981731; Hm_lpvt_0000a307caa05f3ed0b0941b8609002d=1718982472; _ga_WV8ZJFXX9G=GS1.1.1718981730.1.1.1718982472.0.0.0",
        'Origin': 'https://www.toolnb.com',
        'Referer': 'https://www.toolnb.com'
    }
    res = request_util.PostRequestTool.post(url, data=data, headers=headers)
    if res['code'] == 1:
        data = res['data']
        pic_url = data['url']
        print(pic_url)
        data = requests.get(pic_url);
        with open(save_path, "wb") as f:
            f.write(data.content)


def general_qinggan(page_title: str, page_url: str, area: str):
    # 3. 生成内容:
    try:
        contents = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
        if contents is None or len(contents) < 500: return False

        content = re.sub(r'^.*?一、', '一、', contents, flags=re.DOTALL)  # 去除一\前面冗余内容
        parts = re_util.parse_text(text=content)
        # 打印解析后的三个部分
        for i, part in enumerate(parts, 1):
            title, content = re_util.handle_content(text=part)
            new_weixin_title = re.sub(r'^[一二三四五六七八九十]、', '', title)

            output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
            at = article_util.article()
            temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\插画情侣' if os_type == "Windows" else f'/Volumes/文章存档/Images/插画情侣'
            at.create_word_doc_content_qinggan(file_path=output_docx, contents=content, temp_dir=temp_dir)

        # from util import re_util
        # title, content = re_util.handle_content(text=content)
        # new_weixin_title = re.sub(r'[^\w\s,，、!！？？]', '', title)
        # # 4. 转换成word文档
        # os_type = system_util.get_os()
        # output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
        # at = article_util.article()
        # temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\插画情侣' if os_type == "Windows" else f'/Volumes/文章存档/Images/插画情侣'
        # at.create_word_doc_content_qinggan(file_path=output_docx, contents=content, temp_dir=temp_dir)

        print("原标题：《" + page_title + "》")
        print('下载的Word文件:' + output_docx)
        return True
    except Exception as e:
        print(f'文档异常:{e}')
        return False


from util import notion_util
from util import time_util


def get_url_from_notion():
    token = "**************************************************"
    database_id = "c495d8a81ed54703b333d2404e1c625a"  # 易撰-Data
    notion = notion_util.notion_client(token=token, database_id=database_id)
    day = time_util.get_yesterday(30)
    params = {
        'yesterday': day,
        'area': 'qinggan',
        'readcount': 1000,
        # 'author': '伴句诗',
        'author': '醒姐姐',
    }
    contentDict = notion.get_content_by_condition_qinggan(params=params)
    if contentDict is None or len(contentDict) == 1: return None, None, None, None
    page_id = contentDict['page_id']
    page_url = contentDict['page_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_url:{page_url}')
    return page_id, page_title, page_url, notion


'''1. 主要用于生成情感文档'''


def main():
    print('开始执行:gzh_qinggan')
    for i in range(3):
        # 1. 获取数据源
        page_id, page_title, page_url, notion = get_url_from_notion()
        if page_id is None: return

        # 2. 生成文案
        result = general_qinggan(page_title=page_title, page_url=page_url, area='情感')

        # 3. 更新数据源状态
        if result:
            notion.update_page_content(page_id=page_id, properties_params='格式化')
        else:
            notion.update_page_content(page_id=page_id, properties_params='生成失败')


def run_notion():
    try:
        print('开始:gzh_qinggan.py')
        """通过Notion获取数据"""
        notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
        params = {
            'readcount': 10000,
            'author': '伴句诗',  # 伴句诗 醒姐姐
        }
        contentDict = notion.get_content_by_condition_coommon(params=params)
        if contentDict is None: return None
        page_id = contentDict['page_id']
        page_url = contentDict['page_url']
        page_title = contentDict['page_title']
        print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

        result = general_qinggan(page_title=page_title, page_url=page_url, area='情感')
        if result:
            notion.update_page_content(page_id=page_id, properties_params='格式化')
        else:
            notion.update_page_content(page_id=page_id, properties_params='生成失败')
        print('结束:gzh_qinggan.py')
    except Exception as e:
        print(f"情感异常:{e}")


def main():
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 10: break  # 限定二十次
        if len(docx_files) < 4:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    """
    1. 获取公众号-情感数据
    2. 获取标题+poe=文章内容
    3. 获取图片URLs
    4. 生成Word=文章内容+图片拼接
    """
    main()

    gzh_article_clean_tools.clean()
