import os
import re
import time

from dotenv import load_dotenv

from llm import llm_poe
from util import article_util
from util import notion_util
from util import system_util
from util import re_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")

area = '星座'
os_type = system_util.get_os()
destination_folder = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'


def general_xingzuo(page_title: str, page_url: str, area: str):
    # 3. 生成内容:
    try:
        content = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
        if content is None or len(content) < 500: return False

        parts = re_util.parse_text(text=content)
        # 打印解析后的三个部分
        for i, part in enumerate(parts, 1):
            title, content = re_util.handle_content(text=part)
            # new_weixin_title = re.sub(r'[^\w\s,，、!！？？]', '', title)
            new_weixin_title = re.sub(r'^[一二三四五六七八九十]、', '', title)
            new_weixin_title = re.sub(r'\*\*', '', new_weixin_title)
            output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
            at = article_util.article()
            """注意:图片素材来源"""
            # temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\十二生肖' if os_type == "Windows" else f'/Volumes/文章存档/Images/十二生肖'
            temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\甜美女孩' if os_type == "Windows" else f'/Volumes/文章存档/Images/甜美女孩'
            at.create_word_doc_content_xingzuo(file_path=output_docx, contents=content, temp_dir=temp_dir)
        return True
    except Exception as e:
        print(f'文档异常:{e}')
    return False


def run_notion():
    """通过Notion获取数据"""
    for i in range(3):
        try:
            notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
            params = {
                'readcount': 1000,
                'author': '小幸运说星象',  # 可可小缘 小幸运说星象
            }
            contentDict = notion.get_content_by_condition_coommon(params=params)
            if contentDict is None: return None
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_title = contentDict['page_title']
            print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

            flag = general_xingzuo(page_title=page_title, page_url=page_url, area='星座')
            notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")
        except Exception as e:
            print(f"星座异常:{e}")


def main():
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 10: break  # 限定二十次
        if len(docx_files) < 4:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    """星座"""
    main()
