import requests
import json,time
from llm.llm_groq_tiyu import call_content_common

from bs4 import BeautifulSoup
from groq import Groq
import re
from util import  article_util, re_util,system_util
from util import notion_util,feed_util,time_util

from docx import Document
from docx.shared import Inches
from dotenv import load_dotenv
import os
from llm import llm_poe

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
NOTION_DATABASE_DWFB = os.environ.get("NOTION_DATABASE_DWFB")
notion_nba = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)
notion_dwfb = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_DWFB)

import concurrent.futures

def call_with_timeout(func, timeout, *args, **kwargs):
    """设置方法超时时间限制"""
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(func, *args, **kwargs)
        try:
            return future.result(timeout=timeout)
        except concurrent.futures.TimeoutError:
            return None



def save_notion_dwfb():
    """同步短文发布-微头条发布"""
    while True:
        settings = llm_poe.get_settings()  # 获取剩余积分
        if settings is None: break
        message_point_balance = settings['messagePointInfo']['messagePointBalance']
        if int(message_point_balance) < 500:
            print(f'poe 积分不足，暂停执行')
            break

        day = time_util.get_yesterday(15)
        params = {
            'Tags': '初始化',
            'yesterday': day,
            'area':'职场',
            'datasource': '豆瓣',
        }
        contentDict = notion_nba.get_content_by_condition_douban(params)
        if contentDict is None:
            break
        try:
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            # image_url = contentDict['image_url']
            page_title = contentDict['page_title']
            page_chinese_title = contentDict['page_chinese_title']
            print(f'待处理: page_chinese_title:{page_title},page_url:{page_url}')

            # poe文字内容  300秒超时时间限制
            content = call_with_timeout(llm_poe.call_content_common, 300, query=page_title, origin_url=page_url, area='微职场')
            # content=llm_poe.call_content_common(query=page_chinese_title, origin_url=page_url, area='微职场')
            if content is None or len(content)<80: 
                notion_nba.update_page_content(page_id=page_id, properties_params="发布失败")
                continue
            
            # 6. 记录到Notion中
            page = {
                "title": page_title,
                'content': f'某网友诉说:\n {content}',
                'area': '职场',
                'hmcturl': page_url,
                # 'picture_url': image_url
            }
        
            newPage = notion_dwfb.create_page_duanwen(page=page)
            new_page_id = newPage['id']
            notion_nba.update_page_content(page_id=page_id, properties_params="微头条发布成功")
            print(f'Save Notion: pageId: {new_page_id},title: {page_title}')
        except Exception as e:
            print(f'职场微头条异常:{e}')

def main():
    try:
        save_notion_dwfb()
    except Exception as e:
        print(f"职场异常:{e}")


if __name__ == "__main__":
    """
     1. 豆瓣职场内容. new_douban采集到NBA数据库
     2. 再洗稿内容到手动发布库,
     3. 定时发布职场微头条9225
    """
    main()