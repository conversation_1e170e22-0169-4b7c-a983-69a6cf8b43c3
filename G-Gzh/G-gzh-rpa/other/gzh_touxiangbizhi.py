import os
import re
import time
from re import findall

import requests
from bs4 import BeautifulSoup
from dotenv import load_dotenv

import gzh_article_clean_tools
from util import docx_util
from util import notion_util
from util import request_util
from util import system_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")

weixin_title = ""
weixin_time = ""


# 获取微信公众号内容,保存标题和时间
def get_weixin_html(url):
    global weixin_time, weixin_title
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    # 获取标题
    temp = soup.find('h1')
    if temp is None:
        res = requests.get(url)
        soup = BeautifulSoup(res.text, "html.parser")
        temp = soup.find('h1')

    weixin_title = temp.string.strip()

    # 使用正则表达式获取时间
    result = findall(r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}', res.text)
    weixin_time = result[0]

    # 获取正文html并修改
    content = soup.find(id='js_content')
    soup2 = BeautifulSoup((str(content)), "html.parser")
    soup2.div['style'] = 'visibility: visible;'
    html = str(soup2)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    result = findall(pattern, html)

    # 将data-src修改为src
    for url in result:
        html = html.replace('data-src="' + url + '"', 'src="' + url + '"')

    return html


# 上传图片至服务器
def download_pic(content, pic_path):
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)
    # 使用正则表达式查找所有需要下载的图片链接
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)

    count = 1
    for index, item in enumerate(pic_list, 1):
        # count = 1
        flag = True
        pic_url = str(item)

        while flag:
            try:
                if pic_url.find('png') > 0:
                    file_name = str(index) + '.png'

                elif pic_url.find('gif') > 0:
                    file_name = str(index) + '.gif'

                elif pic_url.find('jpg') > 0:
                    file_name = str(index) + '.jpg'
                else:
                    flag = False
                    break

                image_path = pic_path + file_name
                # 如果图片不存在
                if not os.path.exists(image_path):
                    # 下载图片
                    data = requests.get(pic_url);
                    with open(pic_path + file_name, "wb") as f:
                        f.write(data.content)

                # 将图片链接替换为本地链接- 如果转成Word文档需要本地图片链接.  
                content = content.replace(pic_url, image_path)

                # 将图片链接替换为远程链接-wenhaofree.com  - 如果直接HTML复制页面需要远程图片链接
                # picture_url = github_util.upload_image_to_github(image_path, None)
                # content = content.replace(pic_url, picture_url)

                content = content.replace('&amp;from=appmsg', '')
                content = content.replace('data-src=', 'src=')
                content = content.replace('border-width: 1px;', 'border-width: 2px;')
                content = content.replace('border-color: rgb(149, 55, 52);', 'border-color: rgb(211, 13, 211);')

                flag = False
                print('已下载第' + str(count) + '张图片.')
                count += 1
                time.sleep(1)
            except:
                count += 1
                time.sleep(1)

    return content


def get_fengmian(weixin_url: str, save_path: str):
    url = "https://api3.toolnb.com/tools/getWxArticleImages.json"
    data = {"url": weixin_url}
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        # 根据需要添加其他头部信息
        'Cookie': "Hm_lvt_0000a307caa05f3ed0b0941b8609002d=1718981730; _ga=GA1.1.31039878.1718981731; Hm_lpvt_0000a307caa05f3ed0b0941b8609002d=1718982472; _ga_WV8ZJFXX9G=GS1.1.1718981730.1.1.1718982472.0.0.0",
        'Origin': 'https://www.toolnb.com',
        'Referer': 'https://www.toolnb.com'
    }
    res = request_util.PostRequestTool.post(url, data=data, headers=headers)
    if res['code'] == 1:
        data = res['data']
        pic_url = data['url']
        print(pic_url)
        data = requests.get(pic_url);
        with open(save_path, "wb") as f:
            f.write(data.content)
        return pic_url


def general_touxiangbizhi(weixin_url: str, area: str):
    # 1.获取html
    content = get_weixin_html(weixin_url)
    # 2.1 更换标题:deek
    # 2. 本地存储
    os_type = system_util.get_os()
    save_file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\图文下载\\{weixin_title}\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/图文下载/{weixin_title}/'  # 局域网同步
    if not os.path.exists(save_file_path): os.makedirs(save_file_path)

    # 2.1 保存封面:
    try:
        get_fengmian(weixin_url=weixin_url, save_path=save_file_path + '封面.jpg')
    except Exception as e:
        print(f'封面异常:{e}')
    # 2.2 下载图片到本地
    content = download_pic(content=content, pic_path=save_file_path)  # 下载到本地

    # 公众号-昵称信息替换
    headimg = 'https://cdn.wenhaofree.com/gh/wenhaofree/Image/blog/516edcb9-ec76-4fac-abc8-f58b7536e20e_头像-清风-4k.jpeg'
    id = 'MzkxMDYxNDUwMg=='
    nickname = '清风侃侃'
    signature = '总有清风一两缕，解我十万八千愁'
    replaced_string = re.sub(r'data-headimg="[^"]*"', f'data-headimg="{headimg}"', content)
    replaced_string = re.sub(r'data-id="[^"]*"', f'data-id="{id}"', replaced_string)
    replaced_string = re.sub(r'data-nickname="[^"]*"', f'data-nickname="{nickname}"', replaced_string)
    replaced_string = re.sub(r'data-signature="[^"]*"', f'data-signature="{signature}"', replaced_string)
    content = replaced_string

    # 保证文档至少有文字,不是空
    content = content.replace('<p style="display: none;"><mp-style-type data-value="3"></mp-style-type></p>',
                              '<p style="display: none;"><mp-style-type data-value="3"></mp-style-type></p><span>关注领取哈</span>')

    # 标题:
    print(f'原标题:{weixin_title}')

    # 3.保存至本地 文档
    with open(save_file_path + weixin_title + '.txt', 'w+', encoding="utf-8") as f:
        f.write(content)
    with open(save_file_path + weixin_title + '.html', 'w+', encoding="utf-8") as f:
        f.write(content)

    # 4. 转换成word文档
    try:
        output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{weixin_title}.docx'
        # docx_util.convert_html_to_docx(input_html=save_file_path + weixin_title + '.html', output_docx=output_docx)
        file_html = save_file_path + weixin_title + '.html'
        # docx_util.convert_html_to_docx_table_image(input_html_file=file_html, output_docx=output_docx)
        docx_util.convert_html_to_docx_image(input_html_file=file_html, output_docx=output_docx)

        size_in_bytes = os.path.getsize(output_docx)
        size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
        if size_in_mb < 15:
            print("下载到：" + save_file_path)
            print("文件下载到：" + output_docx)
            return True
        else:
            print(f'超过15MB,文件大小:{size_in_mb},删除文件:{output_docx}')
            os.remove(output_docx)
    except Exception as e:
        print(f'文档异常{e}')
    return False


def run_notion():
    """通过Notion获取数据"""
    try:
        notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
        params = {
            'readcount': 10000,
            'author': '茶茶头像',  # 茶茶头像  念九头像 暖言文案
        }
        contentDict = notion.get_content_by_condition_coommon(params=params)
        if contentDict is None: return None
        page_id = contentDict['page_id']
        page_url = contentDict['page_url']
        page_title = contentDict['page_title']
        print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

        flag = general_touxiangbizhi(weixin_url=page_url, area='头像')
        notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")

    except Exception as e:
        print(f"头像异常:{e}")


def main():
    destination_folder = "/Volumes/文章存档/头像/待发布/"  # 目标文件夹
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 8: break  # 限定二十次
        if len(docx_files) < 21:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    main()
    # gzh_article_clean_tools.clean()
