import os
import re
import time
from dotenv import load_dotenv

from llm import llm_poe
from util import article_util
from util import notion_util
from util import system_util
from util import re_util

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")


def general_zhichang(page_title: str, page_url: str, area: str):
    # 3. 生成内容:
    try:
        contents = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
        if contents is None or len(contents) < 500: return False

        content = re.sub(r'^.*?一、', '一、', contents, flags=re.DOTALL)  # 去除一\前面冗余内容
        parts = re_util.parse_text(text=content)
        # 打印解析后的三个部分
        for i, part in enumerate(parts, 1):
            title, content = re_util.handle_content(text=part)
            # title, content = re_util.handle_content_muit(text=content)
            # new_weixin_title = re.sub(r'[^\w\s,，、!！？？]', '', title)
            new_weixin_title = re.sub(r'^[一二三四五六七八九十]、', '', title)

            # 4. 转换成word文档
            os_type = system_util.get_os()
            output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
            at = article_util.article()
            """注意:图片素材来源"""
            # TODO: 职场图片,用作封面
            # temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\性感美女' if os_type == "Windows" else f'/Volumes/文章存档/Images/性感美女'
            temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\文字字体' if os_type == "Windows" else f'/Volumes/文章存档/Images/文字字体'
            at.create_word_doc_content_zhichang(file_path=output_docx, contents=content, temp_dir=temp_dir)
        return True
    except Exception as e:
        print(f'内容异常:{e}')
        return False


def run_notion():
    try:
        """通过Notion获取数据"""
        notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
        params = {
            'readcount': 10000,
            'author': '职场一只猹',  # 职场一只猹
        }
        contentDict = notion.get_content_by_condition_coommon(params=params)
        if contentDict is None: return None
        page_id = contentDict['page_id']
        page_url = contentDict['page_url']
        page_title = contentDict['page_title']
        print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

        flag = general_zhichang(page_title=page_title, page_url=page_url, area='职场')
        notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")
    except Exception as e:
        print(f"职场异常:{e}")


def main():
    destination_folder = "/Volumes/文章存档/职场/待发布/"  # 目标文件夹
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 10: break  # 限定二十次
        if len(docx_files) < 8:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    """职场"""
    main()

    # run_notion()

    # gzh_article_clean_tools.main()
