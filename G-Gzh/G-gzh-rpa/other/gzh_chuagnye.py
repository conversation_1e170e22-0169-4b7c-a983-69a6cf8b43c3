import re
import os
import time

from dotenv import load_dotenv

from llm import llm_poe
from util import article_util
from util import notion_util
from util import re_util
from util import system_util
from util import time_util
import gzh_article_clean_tools

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")


def general_chuangye(page_title: str, page_url: str, area: str):
    # 3. 生成内容:
    try:
        content = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
        if content is None: return False
        title, content = re_util.handle_content(text=content)
        new_weixin_title = re.sub(r'[^\w\s,，、"!！？？]', '', title)
    except Exception as e:
        print(f'生成内容异常:{e}')
        return False

    # 4. 转换成word文档
    try:
        os_type = system_util.get_os()
        output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
        at = article_util.article()
        temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\黑白氛围\\' if os_type == "Windows" else f'/Volumes/文章存档/Images/黑白氛围/'
        at.create_word_doc_content_chuangye(file_path=output_docx, contents=content, temp_dir=temp_dir)
        print("原标题：《" + page_title + "》")
        print("新标题：《" + new_weixin_title + "》")
        print('下载的Word文件:' + output_docx)
        return True
    except Exception as e:
        print(f'文档异常:{e}')
    return False


def get_url_from_notion():
    """Notion检索数据"""
    day = time_util.get_yesterday(30)
    params = {
        'yesterday': day,
        # 'area': 'chuangye',
        'readcount': 10000,
        'author': '大橘创业说',
    }
    contentDict = notion.get_content_by_condition_qinggan(params=params)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_url = contentDict['page_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_url:{page_url}')
    return page_id, page_title, page_url, notion


def main():
    print('开始执行: 创业')
    for i in range(3):
        # 1. 获取数据源
        page_id, page_title, page_url, notion = get_url_from_notion()
        if page_id is None: return

        # 2. 生成文案
        result = general_chuangye(page_title=page_title, page_url=page_url, area='创业')

        # 3. 更新数据源状态
        if result:
            notion.update_page_properties(page_id=page_id, tags='格式化', area='创业')
        else:
            notion.update_page_content(page_id=page_id, properties_params='生成失败')


def run_notion():
    """通过Notion获取数据"""
    notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
    params = {
        'readcount': 10000,
        'author': '大橘创业说',
    }
    contentDict = notion.get_content_by_condition_coommon(params=params)
    if contentDict is None: return None
    page_id = contentDict['page_id']
    page_url = contentDict['page_url']
    page_title = contentDict['page_title']
    print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

    flag = general_chuangye(page_title=page_title, page_url=page_url, area='创业')
    notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")


def main():
    destination_folder = "/Volumes/文章存档/创业/待发布/"  # 目标文件夹
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 8: break  # 限定二十次
        if len(docx_files) < 4:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    """创业"""
    # main()
    # run_notion()

    gzh_article_clean_tools.clean()
