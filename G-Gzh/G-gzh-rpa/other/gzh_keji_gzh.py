import requests
from re import findall
from bs4 import BeautifulSoup
import time
import os
from util import article_util
from util import system_util
from util import github_util
from util import docx_util
from llm import llm_poe
import re
from util import system_util
from util import re_util
from util import request_util
from llm import llm_coze
from util import notion_util
from util import time_util
from dotenv import load_dotenv

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)


def download_pic(content, pic_path):
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)
    # 使用正则表达式查找所有需要下载的图片链接
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)

    count = 1
    for index, item in enumerate(pic_list, 1):
        # count = 1
        flag = True
        pic_url = str(item)

        while flag and count <= 10:
            try:
                if pic_url.find('res.wx.qq.com') > 0:
                    flag = False
                    content = content.replace(
                        'https://res.wx.qq.com/t/wx_fed/we-emoji/res/v1.3.10/assets/Expression/<EMAIL>',
                        '')  # 去除底部图片-Word不需要
                    break
                elif pic_url.find('png') > 0:
                    file_name = str(index) + '.png'

                elif pic_url.find('gif') > 0:
                    file_name = str(index) + '.gif'

                elif pic_url.find('jpg') > 0:
                    file_name = str(index) + '.jpg'
                else:
                    flag = False
                    break

                data = requests.get(pic_url);
                image_path = pic_path + file_name
                with open(pic_path + file_name, "wb") as f:
                    f.write(data.content)

                # 将图片链接替换为本地链接     
                '''
                1. 下载到本地,生成word有图片. 但是HTML文件打开时候Windows无法连接Mac生成的图片路径, 打开Word可以因为已经把图片内置到Word中. 目前是公众号导入Word报错问题
                2. 选择GitHub上传,转换word时候图片无法正常, 但支持HTML复制到公众号
                '''
                content = content.replace(pic_url, image_path)

                # 将图片链接为GitHub图床
                # picture_url = github_util.upload_image_to_github(image_path, None)  # TODO: 下载到GitHub上
                # content = content.replace(pic_url, picture_url)

                content = content.replace('&amp;from=appmsg', '')
                content = content.replace('data-src=', 'src=')
                content = content.replace('border-width: 1px;', 'border-width: 2px;')
                content = content.replace('border-color: rgb(149, 55, 52);', 'border-color: rgb(211, 13, 211);')

                flag = False
                print('已下载第' + str(count) + '张图片.')
                count += 1
                time.sleep(1)

            except:
                count += 1
                time.sleep(1)

        if count > 10:
            print("下载出错：", pic_url)

    return content


def get_weixin_html(url):
    """解析个公众号文章的html"""
    global weixin_time, weixin_title
    res = requests.get(url)
    soup = BeautifulSoup(res.text, "html.parser")

    # 获取标题
    temp = soup.find('h1')
    if temp is None:
        res = requests.get(url)
        soup = BeautifulSoup(res.text, "html.parser")
        temp = soup.find('h1')
    weixin_title = temp.string.strip()

    # 使用正则表达式获取时间
    result = findall(r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}', res.text)
    weixin_time = result[0]

    # 获取正文html并修改
    content = soup.find(id='js_content')
    soup2 = BeautifulSoup((str(content)), "html.parser")
    soup2.div['style'] = 'visibility: visible;'
    html = str(soup2)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    result = findall(pattern, html)

    # 将data-src修改为src
    for url in result:
        html = html.replace('data-src="' + url + '"', 'src="' + url + '"')

    return html


def general_keji(page_title: str, page_url: str, area: str):
    # 文章图片下载
    content = get_weixin_html(url=page_url)
    pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
    pic_list = findall(pattern, content)
    pics = []
    for img in pic_list:
        if img.startswith('http://'): continue  # 作者头像图片不要
        pics.append(img)

    # 3. 生成内容:
    content = llm_poe.call_content_common(query=page_title, origin_url=None, area=area)
    if content is None or len(content) < 100: return False
    title, content = re_util.handle_content(text=content)
    new_weixin_title = re.sub(r'[^\w\s,，、"!！？？]', '', title)

    # 4. 转换成word文档
    try:
        os_type = system_util.get_os()
        output_docx = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\{new_weixin_title}.docx' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/{new_weixin_title}.docx'
        at = article_util.article()
        temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\手机图片' if os_type == "Windows" else f'/Volumes/文章存档/Images/手机图片'

        at.create_word_doc_content_keji01(file_path=output_docx, contents=content, temp_dir=temp_dir, image_urls=pics)
        print()
        print("原标题：《" + page_title + "》")
        print("新标题：《" + new_weixin_title + "》")
        print('下载的Word文件:' + output_docx)
        return True
    except Exception as e:
        print(f'文档异常:{e}')
        return False


def get_url_from_notion():
    day = time_util.get_yesterday(30)
    params = {
        # 'yesterday': day,
        # 'area': 'yuer',
        'readcount': 1000,
        'author': '科技闲述',
    }
    contentDict = notion.get_content_by_condition_qinggan(params=params)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_url = contentDict['page_url']
    page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_url:{page_url}')
    return page_id, page_title, page_url, notion


def main(weixin_url: None):
    # 1. 获取数据源
    if weixin_url is None:
        page_id, page_title, page_url, notion = get_url_from_notion()
        if page_id is None: return

    # 2. 生成文案
    area = '科技'
    result = general_keji(page_title=page_title, page_url=page_url, area=area)

    # 3. 更新数据源状态
    if result:
        notion.update_page_properties(page_id=page_id, tags='格式化', area=area)
    else:
        notion.update_page_content(page_id=page_id, properties_params='生成失败')


def run_notion():
    """通过Notion获取数据"""
    try:
        notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_YZCM)
        params = {
            'readcount': 10000,
            'author': '科技闲述',
        }
        contentDict = notion.get_content_by_condition_coommon(params=params)
        if contentDict is None: return None
        page_id = contentDict['page_id']
        page_url = contentDict['page_url']
        page_title = contentDict['page_title']
        print(f'待处理:page_title:{page_title},page_id:{page_id},page_url:{page_url},')

        result = general_keji(page_title=page_title, page_url=page_url, area='科技')
        if result:
            notion.update_page_content(page_id=page_id, properties_params='格式化')
        else:
            notion.update_page_content(page_id=page_id, properties_params='生成失败')
    except Exception as e:
        print(f'科技异常:{e}')


def main():
    destination_folder = "/Volumes/文章存档/科技/待发布/"  # 目标文件夹
    count = 0
    while True:
        docx_files = [f for f in os.listdir(destination_folder) if f.endswith('.docx')]
        count += 1
        if count > 8: break  # 限定二十次
        if len(docx_files) < 4:  # 文件小于4篇持续
            run_notion()
        else:
            print(f"{destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
            break
        time.sleep(1)  # Wait for 1 second before checking again


if __name__ == "__main__":
    """科技-公众号"""
    main()
