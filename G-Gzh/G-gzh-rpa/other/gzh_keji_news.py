import requests
import json,time
from llm.llm_groq_tiyu import call_content_common

from bs4 import BeautifulSoup
from groq import Groq
import re
from util import  article_util, re_util,system_util
from util import notion_util,feed_util,time_util

from docx import Document
from docx.shared import Inches
from dotenv import load_dotenv
import os
from llm import llm_poe

load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_MRRS = os.environ.get("NOTION_DATABASE_MRRS")
NOTION_DATABASE_DWFB = os.environ.get("NOTION_DATABASE_DWFB")
notion_mrrs = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_MRRS)
notion_dwfb = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_DWFB)

def get_news_list(url):
    """获取腾讯主页新闻列表"""
    response = requests.get(url)
    data = json.loads(response.text)
    news_list = []
    
    # 解析JSON数据,提取id、url和标题
    if 'newslist' in data:
        for item in data['newslist']:
            news_item = {
                'id': item.get('id'),
                'title': item.get('title'),
                'url': item.get('url'),
                'comments': item.get('comments'),
                'image_url': item.get('thumbnails_big')[0],
                'time': item.get('time')
            }
            news_list.append(news_item)
    
    return news_list

def get_article_content(url):
    """获取文章详细内容"""
    response = requests.get(url)
    script_content=response.text
    # 使用正则表达式提取JSON内容
    match = re.search(r'window\.DATA\s*=\s*(\{.*?\});', script_content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            data = json.loads(json_str)
            text_content = data['originContent']['text']
            # 使用BeautifulSoup解析HTML内容
            soup = BeautifulSoup(text_content, 'html.parser')
            all_text = soup.get_text(separator='\n')
            # print(all_text)

            # 提取originAttribute中的列表-图片列表
            origin_attribute_dict = data.get('originAttribute', [])
            origurl_objects = []
            for key, value in origin_attribute_dict.items():
                if 'origUrl' in value:
                    origurl_objects.append(value['origUrl'])
            
            # print(origurl_objects)

            return {
                # 'title': title_text,
                'text': all_text,
                'images': origurl_objects,
                'links': url
            }

        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
    else:
        print("未找到JSON内容")
    return None


def summarize_with_groq(content):
    """使用Groq总结文章中心思想和提取图片"""
    try:
        return call_content_common(content)
    except Exception as e:
        print(e)
    return None
    

def generate_article_with_poe(content):
    """用Poe生成新文章"""
   


def create_word_document(content, images):   
    """生成Word文档"""
    if content is None or len(content) == 0: return False
    title, content = re_util.handle_content(text=content)
    title = re.sub(r'[^\w\s,，、!！？？]', '', title)
    title=title.strip()

    # 4.生成word文档:
    os_type = system_util.get_os()
    file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}\\待发布\\' if os_type == "Windows" else f'/Volumes/文章存档/{area}/待发布/'
    ty = TiYu()
    return ty._get_general_word(title=title, content=content, file_path=file_path, image_urls=images)


def general_word_auto():
    """自动生成文章-llm大模型"""
    # 1. Notion获取条件数据
    day = time_util.get_yesterday(2)
    params = {
        'Tags': '初始化',
        'yesterday': day,
        'area': 'tiyu',
        'datasource': 'i.news.qq.com'
    }
    contentDict = notion.get_content_by_condition_nba(params)
    if contentDict is None: return
    page_id = contentDict['page_id']
    page_chinese_title = contentDict['page_chinese_title']
    page_url = contentDict['page_url']
    # image_url = contentDict['image_url']
    # page_title = contentDict['page_title']
    print(f'待处理: page_id:{page_id},page_chinese_title:{page_chinese_title},page_url:{page_url}')
    content = get_article_content(page_url)
    images=content['images']
    content_llm=summarize_with_groq(content['text'])
    if content_llm is None: return
    flag=create_word_document(content_llm, images)
    notion.update_page_content(page_id=page_id, properties_params="格式化" if flag else "发布失败")



def save_notion():
    # # 1. 获取新闻用户主页最新数据
    ty = TiYu()
    urls = [
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QIf3n9d6oIcsDja5gM%3D&tabId=om_article&caller=1&from_scene=103",  # 大秦壁虎
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMf3ndf7YAYuD7a&tabId=om_article&caller=1&from_scene=103",  # 追球者
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa2Xde6IMfuDc%3D&tabId=om_article&caller=1&from_scene=103"  # 篮球教学论坛
    ]
    ids = feed_util.get_exist_ids('ids-newqq.json')
    for url in urls:    
        contents = get_news_list(url)
        if contents is None or len(contents) == 0: continue
        for content in contents:
            if content.get('id') in ids: continue
            if content.get('image_url') is None: continue
            image_url = content['image_url']
            if image_url is None or len(image_url) == 0: continue
            title=content['title']
            datasource = 'i.news.qq.com'
            url=content['url']
            id=content['id']
            # 保留字段,目前不用!
            text_content = f"""你是一位擅长创作病毒式传播内容的资深文案大师。你的任务是将给定的文本改写成极具传播力的版本。请遵循以下指南：

1. 保留原文的核心主题和关键信息。
2. 使用更加吸引眼球的表述和强烈的情感表达，特别是开头部分。
3. 增强内容的口语化魅力和情感冲击力。
4. 输出限制在150字以内。
5. 最后一行与读者互动，提出问题或呼吁行动。
6. 在文末添加2-3个相关话题标签。

输出格式：
[改写后的内容，包括互动句]

#[标签1] #[标签2] #[标签3]

注意：
- 只输出改写后的内容，不要包含任何解释或元信息。
- 使用简体中文。
- 适当断行以增强可读性和感染力。
- 不要使用引号或其他特殊格式。

请基于以上要求，将我接下来提供的文本改写成病毒式传播内容。 内容链接是：{url}"""

            # # 6. 记录到Notion中
            page = {
                "title": title,
                'chinese_title': title,
                'prompt': text_content,
                'id': id,
                'area': 'tiyu',
                'datasource': datasource,
                'hmcturl': content['url'],
                'published_at': content['time'],
                'picture_url': image_url
            }
            try:
                newPage = notion.create_page(page=page)
                page_id = newPage['id']
                print(f'Save Notion: ID:{id},pageId: {page_id},title: {title}')
                feed_util.save_ids(title, id, 'success','ids-newqq.json')
            except Exception as e:
                print(e)
                continue

def save_notion_dwfb():
    """同步短文发布-微头条发布"""
    while True:
        settings = llm_poe.get_settings()  # 获取剩余积分
        if settings is None: break
        message_point_balance = settings['messagePointInfo']['messagePointBalance']
        if int(message_point_balance) < 500:
            print(f'poe 积分不足，暂停执行')
            break

        day = time_util.get_yesterday(1)
        params = {
            'Tags': '初始化',
            'yesterday': day,
            'datasource': 'ReadHub',
        }
        contentDict = notion_mrrs.get_content_by_condition_coommon_keji(params)
        if contentDict is None:
            break
        try:
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            # image_url = contentDict['image_url']
            page_title = contentDict['page_title']
            print(f'待处理: page_chinese_title:{page_title},page_url:{page_url}')

            # 保存到另一个DB中
            content=llm_poe.call_content_common(query=page_title, origin_url=page_url, area='微体育')
            if content is None or len(content)<80: 
                notion_mrrs.update_page_content_keji(page_id=page_id, properties_params="发布失败")
                continue
            
            # 6. 记录到Notion中
            page = {
                "title": page_title,
                'content': content,
                'area': '科技',
                'hmcturl': page_url,
                # 'picture_url': image_url
            }
        
            newPage = notion_dwfb.create_page_duanwen(page=page)
            new_page_id = newPage['id']
            notion_mrrs.update_page_content_keji(page_id=page_id, properties_params="微头条发布成功")
            print(f'Save Notion: pageId: {new_page_id},title: {page_title}')
        except Exception as e:
            print(f'科技微头条异常:{e}')

def main():
    try:
        save_notion_dwfb()
        print('结束:gzh_tiyu_new.py')
    except Exception as e:
        print(f"科技异常:{e}")


if __name__ == "__main__":
    """
     1. hot_readhub 采集科技排行榜到  热搜数据库
     2. 这里洗稿热搜数据内容, 存储到手写
     3. 9223 定时发布手写内容
    """
    main()