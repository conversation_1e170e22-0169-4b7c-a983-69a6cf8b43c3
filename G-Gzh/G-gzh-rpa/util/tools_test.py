import os
import re
import sys

from llm import llm_coze
from llm import llm_deepseek

sys.path.append(os.path.dirname(__file__))

import time


def main(areaen: str, area: str):
    # 内容生成三篇文章
    for i in range(3):
        print('[程序运行时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))

        '''1. Notion获取数据'''
        from util import notion_util
        token = "**************************************************"
        database_id = 'c495d8a81ed54703b333d2404e1c625a'  # 易撰
        notion = notion_util.notion_client(token=token, database_id=database_id)

        from util import time_util
        yeterday = time_util.get_yesterday(days=2)  # 时间线
        params = {'area': areaen, 'yeterday': yeterday, 'readcount': 5000}  # 筛选条件
        contentDict = notion.get_content_by_condition(params=params, start_cursor=None)
        if contentDict is not None:
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_readcount = contentDict['page_readcount']
            page_title = contentDict['page_title']
            print(f'page_title:{page_title},page_readcount:{page_readcount}, page_url:{page_url}')
        else:
             # 二次获取
            if area =='体育':
                from util import feed_util
                title, page_url,id=feed_util.check_feed_bleacherreport()#英文标题需要翻译
            else:
                print(f'{area} 没有符合数据.')
                continue

       


        '''2. 文档数据解析'''
        from util import ddg_util
        ddg = ddg_util.ddgs_client()
        try:
            from util import reader_ai_util
            data=reader_ai_util.parse_url_data(url=page_url)
            if data is None:
                data = ddg.search_crawler(url=page_url)
                origin_title = data['title']
                content = data['content']
                if '离线日志' in origin_title:
                    print(f'{origin_title} 跳过. url:{page_url}')
                    continue
            else:
                origin_title = data['title']
                content = data['content']
                
            images = []
            from util import re_util
            images = re_util.get_img_urls(content)
            content = re_util.get_content(content)
            print(f'内容解析成功')
            # 搜索关联内容
            if area in ['体育', '娱乐', '社会', '科技']:
                results = ddg.search(query=origin_title)
                article_snippets = [result['snippet'] for result in results]  # 将结果的'snippet'字段放入列表
                article_draft = '\n'.join(article_snippets)  # 使用换行符分隔每个snippet
                content = f'{article_draft}\n{content}'

            
            '''3. 生成文档'''
            # 3.1 标题
            title = llm_deepseek.call_title(origin_title)
            if title is None or len(title) == 0:
                print('内容风控处理')
                notion.update_page_content(page_id=page_id, properties_params='发布失败')
                continue
            title = re.sub(r'^\d+\.?\s*', '', title.split("\n")[1])
            title = title.replace('_', '')
        pattern = r'[^\w\s,，、!！？？]'，、!！？？]'
            title = re.sub(pattern, '', title)  # 使用正则表达式去除特殊标点符号
            print(f'新文章标题是:{title}')
            print(f'开始生成正文')

            # 3.2 文章正文
            query = f'[{content}]根据提供的信息内容,以人类的口吻,写一篇公众号文章，开篇段落要足够引起读者人兴趣，至少800字'
            '''Siliconflow大模型调用'''
            from llm import llm_siliconflow_http
            content=llm_siliconflow_http.call_content_commmon(area=area,content_user=query)

            '''Coze大模型调用'''
            # content = llm_coze.call_content_common(query=query,area=area)  # 科技
            # query = f'[{origin_title}]根据信息内容写一篇公众号文章，开篇段落要足够引起读者人兴趣，至少850字'

            if content is None or len(content) < 500: print('大模型没有生成新数据'); continue
            # 3.3 生成word文档
            from util import system_util
            os_type = system_util.get_os()
            # path=rf'\\192\\fwh\\fuwenhao\\BaiduSyncdisk\\文章存档\\markdown_to_docx.doc'  #windows 存储到Mac
            # /Volumes/文章存档/        #Mac文件同步到Windows
            # save_file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}' if os_type == "Windows" else f'/Users/<USER>/fuwenhao/BaiduSyncdisk/文章存档/{area}'   #百度云盘同步-1G流量/月
            save_file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}' if os_type == "Windows" else f'/Volumes/文章存档/{area}'   #局域网同步
            strings = ['zhaiyao', content]

            from util import article_util
            article = article_util.article()
            article.create_word_doc(file_path=save_file_path, strings=strings, image_urls=images, name=title,
                                    origin_title=title, area=area)

            notion.update_page_content(page_id=page_id, properties_params='格式化')  # 生成文章成功
            # 3.4 清理问题word
            article.delete_empty_docs_in_folder(os.path.join(save_file_path, '待发布'))
            article.delete_empty_folder(os.path.join(save_file_path, '待发布'))
        except Exception as e:
            print(f'{page_url},{e}')




if __name__ == '__main__':
    page_url='https://bleacherreport.com/articles/10125317-nba-rumors-ex-lakers-dwight-howard-demarcus-cousins-quinn-cook-to-join-taiwan-team'
    from util import reader_ai_util
    data=reader_ai_util.parse_url_data(url=page_url)
    origin_title = data['title']
    content = data['content']
    
    from util import re_util
    images = re_util.get_img_urls(content)
    content = re_util.get_content(content)

    # import transaction_tengxun_util
    # contentzn=transaction_tengxun_util.translate_text(content)
    # print(contentzn)
    
    area='体育'
    query = f'[{content}]根据提供的信息内容,以人类的口吻,写一篇公众号文章，开篇段落要足够引起读者人兴趣，至少800字'
    from llm import llm_siliconflow_http
    content=llm_siliconflow_http.call_content_commmon(area=area,content_user=query)
    print(content)
