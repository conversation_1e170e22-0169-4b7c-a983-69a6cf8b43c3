import os
import cv2
import numpy as np
from PIL import Image

def handle_image(path: str):
    try:
        # 读取原始图像
        img = cv2.imread(path, 1)
        if img is None:
            raise Exception("Failed to read the original image")

        # 分离通道并在频域中处理
        def process_channel(channel):
            # 转换到频域
            dft = cv2.dft(np.float32(channel), flags=cv2.DFT_COMPLEX_OUTPUT)
            dft_shift = np.fft.fftshift(dft)
            
            # 创建频域掩码
            rows, cols = channel.shape
            crow, ccol = rows//2, cols//2
            mask = np.ones((rows, cols, 2), np.uint8)
            
            # 去除对角线频率分量
            d = 30  # 带宽
            for i in range(-d, d+1):
                mask[crow-d:crow+d, ccol+i:ccol+i+1] = 0
                mask[crow+i:crow+i+1, ccol-d:ccol+d] = 0
            
            # 应用掩码并反变换
            fshift = dft_shift * mask
            f_ishift = np.fft.ifftshift(fshift)
            img_back = cv2.idft(f_ishift)
            img_back = cv2.magnitude(img_back[:,:,0], img_back[:,:,1])
            
            # 归一化
            img_back = cv2.normalize(img_back, None, 0, 255, cv2.NORM_MINMAX)
            return img_back.astype(np.uint8)

        # 在多个颜色空间中处理
        # 1. BGR空间
        b, g, r = cv2.split(img)
        b_processed = process_channel(b)
        g_processed = process_channel(g)
        r_processed = process_channel(r)
        bgr_processed = cv2.merge([b_processed, g_processed, r_processed])

        # 2. LAB空间
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        l_processed = process_channel(l)
        lab_processed = cv2.merge([l_processed, a, b])
        lab_processed = cv2.cvtColor(lab_processed, cv2.COLOR_LAB2BGR)

        # 3. YUV空间
        yuv = cv2.cvtColor(img, cv2.COLOR_BGR2YUV)
        y, u, v = cv2.split(yuv)
        y_processed = process_channel(y)
        yuv_processed = cv2.merge([y_processed, u, v])
        yuv_processed = cv2.cvtColor(yuv_processed, cv2.COLOR_YUV2BGR)

        # 合并所有处理结果
        result = cv2.addWeighted(bgr_processed, 0.4, 
                               cv2.addWeighted(lab_processed, 0.3, yuv_processed, 0.3, 0), 
                               0.6, 0)

        # 使用双边滤波替代导向滤波进行细节增强
        for i in range(2):  # 进行两次迭代
            result = cv2.bilateralFilter(result, 9, 75, 75)

        # 最后的细节增强
        kernel_sharpen = np.array([[-1,-1,-1],
                                 [-1, 9,-1],
                                 [-1,-1,-1]])
        result = cv2.filter2D(result, -1, kernel_sharpen)

        # 保存结果
        cv2.imwrite(path, result)
        return path

    except Exception as e:
        print(f'水印处理异常返回原图, {e}')
        return path

def handle_image_advanced(path: str):
    """
    使用更复杂的方法处理水印，结合多个处理步骤
    """
    try:
        # 读取图像
        img = cv2.imread(path, 1)
        if img is None:
            raise Exception("Failed to read the original image")

        # 1. 预处理：去噪
        img_denoised = cv2.fastNlMeansDenoisingColored(img, None, 10, 10, 7, 21)

        # 2. 自适应直方图均衡化
        lab = cv2.cvtColor(img_denoised, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        lab = cv2.merge([l,a,b])
        img_clahe = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

        # 3. 使用Retinex算法增强
        def retinex(img):
            img_float = img.astype(np.float32) / 255.0
            result = np.zeros_like(img_float)
            for i in range(3):
                blur = cv2.GaussianBlur(img_float[:,:,i], (0,0), 50)
                result[:,:,i] = cv2.log(img_float[:,:,i] + 1.0) - cv2.log(blur + 1.0)
            result = (result - result.min()) / (result.max() - result.min())
            return (result * 255).astype(np.uint8)

        img_retinex = retinex(img_clahe)

        # 4. 合并处理结果
        result = cv2.addWeighted(img_clahe, 0.5, img_retinex, 0.5, 0)

        # 5. 使用双边滤波进行边缘保持的平滑
        result = cv2.bilateralFilter(result, 9, 75, 75)

        # 6. 最终的细节增强
        kernel_sharpen = np.array([[-1,-1,-1],
                                 [-1, 9,-1],
                                 [-1,-1,-1]])
        result = cv2.filter2D(result, -1, kernel_sharpen)

        # 保存结果
        cv2.imwrite(path, result)
        return path

    except Exception as e:
        print(f'水印处理异常返回原图, {e}')
        return path

# 测试代码
if __name__ == "__main__":
    image_path = '/Users/<USER>/Downloads/1936179.jpg'
    # 尝试基础方法
    new_path = handle_image(image_path)
    print(f'Basic processed image saved at: {new_path}')
    
    # 如果基础方法效果不理想，可以尝试高级方法
    # new_path = handle_image_advanced(image_path)
    # print(f'Advanced processed image saved at: {new_path}')