import re


# 提取图片url
def get_img_urls(text):
    imgs = []
    # 使用正则表达式匹配图片链接
    pattern = r'\!\[.*?\]\((.*?)\)'
    matches = re.findall(pattern, text)

    # 打印所有匹配的图片链接
    for match in matches:
        # print(match)
        match = str(match).replace('w_40,h_27', 'w_800,h_533')  # 针对NBA图片做大小处理,其他不用
        imgs.append(match)
    return imgs


# 获取纯文本
def get_content(text):
    # 使用正则表达式匹配图片链接
    pattern_image = r'\!\[.*?\]\((.*?)\)'

    # 找到所有图片链接
    matches_image = re.findall(pattern_image, text)

    # 从原始文本中移除图片链接
    for match in matches_image:
        text = re.sub(pattern_image, '', text)

    # 打印非图片格式的内容文本
    # print(text)
    return text
    
def parse_text(text:str):
    """一二三解析"""
    # 使用正则表达式匹配「一、二、三」等大写中文序号
    pattern = re.compile(r"([一二三])、")
    # 找到所有匹配的序号位置
    matches = list(pattern.finditer(text))
    # 根据匹配的序号位置分割文本
    parts = []
    start = 0
    for match in matches:
        end = match.start()
        parts.append(text[start:end].strip())
        start = end
    parts.append(text[start:].strip())
    # 去除空字符串
    parts = [part for part in parts if part]
    return parts

def handle_content(text: str):
    # 使用正则匹配首行内容
    pattern = r'^(.+?)\n'
    match = re.search(pattern, text, re.MULTILINE)
    if match:
        first_line = match.group(1)
        print("首行内容:", first_line)

    # 获取剩余内容
    pattern = r'\n\n(.+)'
    match = re.search(pattern, text, re.DOTALL)
    if match:
        remaining_content = match.group(1)
        print("剩余内容:\n", remaining_content)
    else:
        remaining_content = text.replace(first_line, '')
    return first_line, remaining_content
def remove_leading_number_and_period(text):
    pattern = r'^(\d+\.\s*)(.+)$'
    match = re.match(pattern, text)
    if match:
        return match.group(2)
    else:
        return text

def handle_content_muit(text: str):
    # 使用正则匹配首行内容
    pattern = r'^(\d+\.\s*.*?)(?=\n\n|$)'
    match = re.search(pattern, text, re.MULTILINE)
    if match:
        first_line = match.group(1)
        first_line = remove_leading_number_and_period(first_line)
        print("首行内容:", first_line)

   # 获取剩余内容
    remaining_content = text.split('\n', 1)
    if len(remaining_content) > 1:
        remaining_content = remaining_content[1].strip()
        print("剩余内容:\n", remaining_content)
    else:
        remaining_content = ''

    return first_line, remaining_content



def extract_data(text):
    data = []
    lines = text.split('\n')
    for line in lines:
        if line.strip().startswith(('1', '2', '3', '4', '5')):
            data.append(line.strip())
            if len(data) == 5:
                break
    return data





# text="""1. 外企⾼薪 3 个⽉，回国后发现部⻔只剩 2 ⼈，客户竟点名让我留下！
# 俗话说："机会总是留给有准备的⼈。"
# 刚⼊职外企就被派去国外培训，这本是⼀件令⼈兴奋的事情。然⽽，3 个⽉后回到公司，却发现整个部⻔只剩下我和领导两个⼈。
# 其他同事都已离职，令我感到⼗分诧异。更没想到的是，领导竟然告诉我，客户点名要我留下。
# **变⾰中的不确定性**
# ⾯对部⻔突如其来的变化，难免会感到不安和迷茫。公司的战略调整，同事的⼤批离职，都让⼈对未来充满了不确定性。
# 正如鲁迅先⽣所说："**⽆穷的远⽅，⽆数的⼈们，都和我有关。**"在这个变⾰的时代，我们每个⼈都难以置身事外。
# **客户的信任是最⼤的肯定**
# 然⽽，客户点名让我留下，这无疑是对我能⼒的认可，更是对我个⼈品质的信任。
# 俗话说："**⾦⽟其外，败絮其中。**"客户看重的，不仅仅是表⾯的技能，更是内在的品质。
# 这份信任，让我在变⾰中找到了前进的⽅向和动⼒。
# **把握机遇，勇于担当**
# ⾯对变⾰，我们要学会把握机遇，勇于担当。正如孟⼦所说："**天将降⼤任于斯⼈也，必先苦其⼼志，劳其筋⾻。**"
# 变⾰带来的挑战，正是我们成⻓和发展的契机。唯有在困境中历练，才能真正提升⾃⼰的能⼒和⼼智。
# 作为部⻔的核⼼成员，我更要勇于承担起责任，带领团队渡过难关，实现公司的战略⽬标。
# **砥砺前⾏，不负韶华**
# 变⾰的路上，难免会有荆棘和坎坷。但正如泰⼽尔所说："**⽣活不是等待⻛暴过去，⽽是学会在⾬中起舞。**"
# 我们要以积极的⼼态⾯对变⾰，在挑战中不断学习和成⻓，以⾃⼰的努⼒和汗⽔，谱写⼈⽣的华彩乐章。
# 跳槽只是职业⽣涯的⼀个新的起点，未来的路还⻓着呢。让我们携⼿并肩，在变⾰的洪流中砥砺前⾏，共创美好的明天！
# 你准备好接受变⾰的挑战了吗？

# 2. 外企梦碎！3个月国外培训回来，部门只剩我和领导，客户却让我留下
# 古人云："天将降大任于斯人也，必先苦其心志，劳其筋骨。"
# 加入外企，本以为是实现职业梦想的开始。没想到，刚入职就被派去国外培训，3个月后回来，整个部门竟然只剩下我和领导两个人。
# 其他同事都已离职，这让我感到无比震惊。更令我意外的是，领导告诉我，客户点名要我留下。
# **巨变中的迷茫**
# 面对部门的突然变动，内心难免会感到彷徨和无助。公司的决策调整，同事的集体离去，都让人对未来充满了不安。
# 正如鲁迅先生所说："**⻛萧萧兮易⽔寒，壮⼠⼀去兮不复还。**"在这个充满变数的时代，我们每个人都身不由己。
# **客户的青睐是最大的鼓励**
# 然而，客户点名让我留下，这无疑是对我能力的肯定，更是对我个人品行的认可。
# 俗话说："**⼈⽆信不⽴，业⽆信不兴。**"客户看重的，不仅仅是表面的技能，更是内在的素质。
# 这份信任，让我在变革中找到了奋斗的意义和动力。
# **化危为机，勇挑重担**
# 面对变革，我们要学会化危为机，勇于担当。正如马云所说："**今天很残酷，明天更残酷，后天很美好，但绝大部分人都死在明天晚上。**"
# 变革带来的压力，正是我们磨练自己的机会。唯有在逆境中成长，才能真正锻造出不凡的气质和能力。
# 作为部门的中流砥柱，我更要勇于承担起责任，带领团队突破困境，实现公司的发展目标。
# **砥砺奋进，不负使命**
# 变革的道路上，难免会有坎坷和荆棘。但正如海明威所说："**世界从不眷顾弱者，而是把机会留给那些勇敢的人。**"
# 我们要以昂扬的斗志面对变革，在挑战中不断超越自己，以自己的汗水和智慧，谱写人生的绚丽篇章。
# 跳槽只是职业生涯的又一个起点，未来的征程还很漫⻓。让我们携手并进，在变革的洪流中砥砺前行，共创辉煌的事业！
# 你是否有勇气接受这个挑战?

# 3. 3个月外企培训，回国发现整个部门只剩我和领导，客户却让我独自留下
# 孔子曰：学而时习之，不亦说乎？投身外企，本以为是一场美好的学习之旅。谁知刚入职就被派去国外培训，3个月后回到公司，竟发现整个部门只剩下我和领导两个人。其他同事都已陆续离职，这让我感到措手不及。更出乎意料的是，领导告诉我，客户特意要求我继续留下。
# **巨变中的不安**
# 部门的突然变动，难免让人感到不安和困惑。公司的人事变革，同事的集体离开，都让人对未来充满了疑虑。正如屈原所言："**路漫漫其修远兮，吾将上下而求索。**"在这个充满不确定性的时代，我们每个人都身不由己。
# **客户的信赖是最大的肯定**
# 然而，客户点名让我留下，这无疑是对我能力的认可，更是对我个人品德的信任。俗话说："**修身、齐家、治国、平天下。**"客户看重的，不仅仅是表面的技艺，更是内在的修养。这份信任，让我在动荡中找到了前进的方向和力量。
# **化险为夷，勇担重任**
# 面对变革，我们要学会化险为夷，勇于担当。正如老子所说："**大器晚成，大音希声。**"变革带来的挑战，正是我们淬炼自己的契机。唯有在逆境中历练，才能真正锻造出非凡的气度和能力。作为部门的中坚力量，我更要勇于肩负起责任，带领团队渡过难关，实现公司的战略目标。
# **砥砺前行，不负韶华**
# 变革的路上，难免会有艰辛和坎坷。但正如尼采所说："**凡是没有毁灭我的，都使我更加强大。**"我们要以积极的心态面对变革，在挑战中不断成长进步，以自己的努力和智慧，谱写人生的华美乐章。跳槽只是职业生涯的一个新的起点，未来的道路还很漫长。让我们携手共进，在变革的浪潮中砥砺前行，共创美好的明天！
# 你准备好迎接这个全新的挑战了吗？

# 4. 外企培训归来，部门突变只剩2人！客户点名让我留下，难道是机会？
# 孟子曰："天将降大任于是人也，必先苦其心志，劳其筋骨，饿其体肤。"加入外企，本以为是实现梦想的开始。没想到，刚入职就被派去国外培训，3个月后回来，整个部门竟然只剩下我和领导两个人。其他同事都已离职，这让我感到十分震惊。更令我惊讶的是，领导告诉我，客户点名要我留下。
# **变革中的迷茫**
# 面对部门的剧变，内心难免会感到彷徨和无助。公司的战略调整，同事的集体离去，都让人对未来充满了不安。正如王勃所言："**海内存知己，天涯若比邻。**"在这个充满变数的时代，我们每个人都身不由己。
# **客户的重托是最大的鼓舞**
# 然而，客户点名让我留下，这无疑是对我能力的肯定，更是对我个人品行的认可。俗话说："**德不孤，必有邻。**"客户看重的，不仅仅是表面的技艺，更是内在的品德。这份信任，让我在变革中找到了奋斗的方向和动力。 
# **转危为安，勇担重任**
# 面对变革，我们要学会转危为安，勇于担当。正如郭沫若所说："**历尽天华成此景，人间万事出艰辛。**"变革带来的压力，正是我们磨砺自己的良机。唯有在困境中成长，才能真正锤炼出不凡的品格和能力。作为部门的中流砥柱，我更要勇于承担起责任，带领团队攻坚克难，实现公司的发展目标。
# **砥砺前行，不负使命**
# 变革的征途上，难免会有风雨和坎坷。但正如李大钊所说："**为有牺牲多壮志，敢教日月换新天。**"我们要以昂扬的斗志面对变革，在挑战中不断超越自我，以自己的汗水和智慧，书写人生的灿烂篇章。跳槽只是职业生涯的又一个起点，未来的旅程还很漫长。让我们携手并肩，在变革的洪流中砥砺前行，共创美好的事业！
# 你是否已经做好准备迎接机会的青睐？

# 5. 历经外企培训，回国竟成部门"唯二"？客户力挺，我该如何抉择？
# 庄子云："天地有大美而不言，四时有明法而不议。"投身外企，本以为是开启人生新篇章的良机。谁知刚入职就被派去国外培训，3个月后回到公司，竟发现整个部门只剩下我和领导两个人。其他同事都已相继离职，这让我感到无比诧异。更让我意外的是，领导告诉我，客户特意要求我继续留下。
# **剧变中的彷徨**
# 部门的突然变动，难免让人感到茫然和困惑。公司的组织调整，同事的集体离开，都让人对未来充满了疑虑。正如苏轼所言："**人有悲欢离合，月有阴晴圆缺。**"在这个瞬息万变的时代，我们每个人都身不由己。
# **客户的信任是最大的鼓励**
# 然而，客户点名让我留下，这无疑是对我能力的认可，更是对我个人品质的信任。俗话说："**大丈夫立于天地之间，当有一番作为。**"客户看重的，不仅仅是表面的技能，更是内在的气度。这份信任，让我在纷乱中找到了前进的方向和力量。
# **化险为夷，勇担重任**
# 面对变革，我们要学会化险为夷，勇于担当。正如毛泽东所说："**世界上没有坐享其成的好事，要幸福就要奋斗。**"变革带来的挑战，正是我们淬炼自己的契机。唯有在逆境中历练，才能真正锻造出非凡的胸怀和能力。作为部门的中坚力量，我更要勇于肩负起责任，带领团队渡过难关，实现公司的战略目标。
# **砥砺前行，不负韶华**
# 变革的路上，难免会有艰辛和坎坷。但正如鲁迅所说："**哪里有天才，我是把别人喝咖啡的时间都用在工作上的。**"我们要以积极的心态面对变革，在挑战中不断学习进步，以自己的努力和汗水，谱写人生的绚丽乐章。跳槽只是职业生涯的一个新的起点，未来的旅
# """
# groups = text.split("\n\n")

# group1 = groups[0]
# group2 = groups[1]
# handle_content_muit(group1)
# handle_content_muit(group2)
# # extracted_data = extract_data(text)
# # for item in extracted_data:
#     # handle_content(item)
#     # print(item)


# text="""\n\n哪些体育项目可以参加？\n-----------\n\n![Image 3: 残疾人攀岩将在2028年奥运会上首次亮相。](https://ichef.bbci.co.uk/ace/ws/640/cpsprodpb/15953/production/_133530488_whatsubject.jpg.webp)\n\n图像来源，Getty Image\n\n图像加注文字，残疾人攀岩将在2028年奥运会上首次亮相。"""
# print(get_img_urls(text))