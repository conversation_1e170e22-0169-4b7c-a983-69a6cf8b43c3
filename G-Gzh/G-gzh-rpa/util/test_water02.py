import cv2
import numpy as np
import pytesseract
from PIL import Image
import re
import os
#pip install opencv-python numpy pytesseract pillow
def remove_watermarks(input_path):
    # 读取图像
    image = cv2.imread(input_path)
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 使用Tesseract进行OCR  #brew install tesseract
    text = pytesseract.image_to_string(Image.fromarray(gray))
    
    # 查找可能的网址
    # urls = re.findall(r'https?://\S+|www\.\S+', text)
    urls = re.findall(r'(?:https?://)?(?:www\.)?[a-zA-Z0-9-]+(?:\.[a-zA-Z]{2,})+', text)

    
    if not urls:
        print("No watermarks found.")
        return input_path
    
    # 对每个检测到的URL进行处理
    for url in urls:
        # 在图像中查找URL文本
        boxes = pytesseract.image_to_boxes(Image.fromarray(gray))
        
        for b in boxes.splitlines():
            b = b.split(' ')
            if url.lower() in b[0].lower():
                x, y, w, h = int(b[1]), int(b[2]), int(b[3]), int(b[4])
                
                # 使用周围的像素进行插值来移除水印
                roi = image[y:h, x:w]
                mask = np.zeros(roi.shape[:2], np.uint8)
                cv2.rectangle(mask, (0,0), (w-x, h-y), (255,255,255), -1)
                
                # 使用Inpainting算法填充水印区域
                result = cv2.inpaint(roi, mask, 3, cv2.INPAINT_TELEA)
                
                # 将处理后的区域放回原图
                image[y:h, x:w] = result
    
    # 保存处理后的图像
    output_path = os.path.splitext(input_path)[0] + "_no_watermark" + os.path.splitext(input_path)[1]
    cv2.imwrite(output_path, image)
    
    print(f"Watermarks removed. New image saved as: {output_path}")
    return output_path

# 使用示例
input_image_path = "/Users/<USER>/Downloads/1936179.jpg"
output_image_path = remove_watermarks(input_image_path)