from datetime import datetime, timedelta,timezone
import pytz

import time
from functools import wraps

class TimeoutUtils:
    @staticmethod
    def timeout(timeout_seconds=600): #10分钟之后超时
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                except Exception as e:
                    raise e
                end_time = time.time()
                execution_time = end_time - start_time
                if execution_time > timeout_seconds:
                    print(f"函数 {func.__name__} 执行时间超过 {timeout_seconds} 秒")
                    return None
                return result
            return wrapper
        return decorator
    

def get_yesterday(days:int):
    # 获取当前时间
    current_time = datetime.now()
    # 计算前一天的时间
    yesterday = current_time - timedelta(days=days)
    formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
    print(formatted_time)
    return formatted_time

def date_format(date_str:str):
    """1. 东部时间格式化"""
    # 输入日期时间字符串
    # 定义输入日期时间字符串的格式
    input_format = "%a, %d %b %Y %H:%M:%S %Z"

    # 将字符串解析为 datetime 对象
    dt = datetime.strptime(date_str, input_format)

    # 定义输出日期时间字符串的格式
    output_format = "%Y/%m/%d %H:%M:%S"

    # 将 datetime 对象格式化为字符串
    formatted_date_str = dt.strftime(output_format)

    print(formatted_date_str)
    return formatted_date_str

def formatted_date(date_str:str):
    # 解析日期字符串并设定为UTC时区
    date_obj = datetime.strptime(date_str, "%a, %d %b %Y %H:%M:%S %Z").replace(tzinfo=timezone.utc)
    # 转换为年月日格式
    formatted_date = date_obj.strftime("%Y-%m-%d")

    print(formatted_date)
    return formatted_date




# formatted_date(date_str = "Fri, 21 Jun 2024 11:00:00 GMT")
# formatted_date(date_str="Wed, 3 Jul 2024 21:02:18 EST")


def convert_est_to_cst(est_time_str):
    # 定义EST时区
    est_tz = pytz.timezone('America/New_York')
    # 定义北京时间
    cst_tz = pytz.timezone('Asia/Shanghai')

    # 将EST时间字符串转换为datetime对象
    est_time = datetime.strptime(est_time_str, "%a, %d %b %Y %H:%M:%S EST")
    # 将datetime对象转换为EST时区的本地时间
    est_time = est_tz.localize(est_time)

    # 将EST时间转换为UTC时间
    utc_time = est_time.astimezone(pytz.utc)
    # 将UTC时间转换为北京时间
    cst_time = utc_time.astimezone(cst_tz)

    # 格式化输出北京时间年月日
    cst_date_str = cst_time.strftime("%Y-%m-%d")
    return cst_date_str

def data_format(date_str):
    from datetime import datetime
    date_obj = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %z')
    formatted_date = date_obj.strftime('%Y-%m-%d %H:%M:%S')
    return formatted_date

def convert_to_iso_date_string(date_str):
    # 将输入的日期字符串转换为 datetime 对象
    date_obj = datetime.strptime(date_str, "%Y年%m月%d日")

    # 将 datetime 对象转换为 ISO 8601 date string
    iso_date_str = date_obj.strftime("%Y-%m-%d")

    return iso_date_str

# 示例调用
# est_time_str = "Wed, 3 Jul 2024 21:02:18 EST"
# cst_date = convert_est_to_cst(est_time_str)
# print(cst_date)
# date_str = 'Fri, 05 Jul 2024 11:16:00 +0000'

# data_format(date_str)