import requests
import os
from dotenv import load_dotenv
load_dotenv()
# 替换为你的订阅密钥
subscription_key = os.environ.get("BING_API_KEY")
"""
https://learn.microsoft.com/en-us/bing/search-apis/bing-image-search/overview
"""


def search_images(search_term, count=10, offset=0, mkt="en-us"):
    """
    调用 Bing 图像搜索 API 并返回结果
    
    Args:
        subscription_key (str): 有效的订阅密钥
        search_term (str): 搜索查询关键词
        count (int, optional): 返回结果数量,默认为 10
        offset (int, optional): 起始偏移量,默认为 0
        mkt (str, optional): 市场代码,默认为 "zh-CN"
        
    Returns:
        dict: 解析后的 JSON 响应数据
    """
    headers = {"Ocp-Apim-Subscription-Key": subscription_key}
    params = {
        "q": search_term,
        "count": count,
        "offset": offset,
        "mkt": mkt
    }
    
    url = "https://api.bing.microsoft.com/v7.0/images/search"
    response = requests.get(url, headers=headers, params=params)
    response.raise_for_status()  # 检查请求是否成功
    results=response.json()
    # print(f"前 5 个搜索结果图片 URL 为:")
    images=[]
    for image in results["value"][:5]:
        # print(image["contentUrl"])
        images.append(image["contentUrl"])
    return images
    # return response.json()

def search_images_five(search_term, count, offset=0, mkt="en-us"):
    results = search_images(subscription_key, search_term,count,offset,mkt)
    print(f"前 5 个搜索结果图片 URL 为:")
    images=[]
    for image in results["value"][:5]:
        print(image["contentUrl"])
        images.append(image["contentUrl"])
    return images

def main():
    
    
    # 搜索查询关键词
    search_term = "勒布朗-詹姆斯"
    
    # 调用 search_images 函数
    results = search_images(subscription_key, search_term)
    
    # 打印部分结果图片 URL
    print(f"前 5 个搜索结果图片 URL 为:")
    images=[]
    for image in results["value"][:5]:
        print(image["contentUrl"])
        images.append(image["contentUrl"])
    return images

if __name__ == "__main__":
    main()