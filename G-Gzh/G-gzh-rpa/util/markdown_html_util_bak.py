import random
from bs4 import BeautifulSoup
import markdown

def markdown_to_html(markdown_content, output_file_path, image_urls):
    try:
        # 使用 Python-Markdown 库将 Markdown 转换为基本 HTML
        html_content = markdown.markdown(markdown_content)

        # 创建 BeautifulSoup 对象
        soup = BeautifulSoup(f'<html><head><title>文章</title></head><body><section id="nice" data-tool="wenhaofree" data-website="https://wenhaofree.com">{html_content}</section></body></html>', 'html.parser')
        section = soup.find('section')

        # 设置 section 的样式
        section['style'] = 'margin: 0; padding: 10px; background: none; width: auto; font-family: Optima, "Microsoft YaHei", PingFangSC-regular, serif; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em; word-spacing: 0; letter-spacing: 0; word-break: break-word; overflow-wrap: break-word; text-align: left;'

        # 添加全局样式
        style = soup.new_tag('style')
        style.string = """
        h2, h3 { margin: 30px 0 15px; padding: 0; display: flex; }
        h2 .content { font-size: 22px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block; }
        h3 .content { font-size: 20px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block; }
        p { color: rgb(89, 89, 89); font-size: 15px; line-height: 1.8em; letter-spacing: 0.04em; padding: 8px 0; }
        figure { margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center; }
        figure img { display: block; max-width: 100%; margin: 0 auto; border: none; border-radius: 0; object-fit: fill; box-shadow: none; }
        figcaption { color: rgb(136, 136, 136); font-size: 14px; line-height: 1.5em; text-align: center; font-weight: normal; margin: 5px 0 0; padding: 0; }
        ul { list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: rgb(0, 0, 0); }
        li section { margin: 5px 0; color: rgb(89, 89, 89); font-size: 15px; line-height: 1.8em; letter-spacing: 0.04em; text-align: left; font-weight: normal; }
        a { color: rgb(53, 179, 120); font-weight: bold; text-decoration: none; border-bottom: 1px solid rgb(53, 179, 120); }
        """
        soup.head.append(style)

        # 获取所有段落
        paragraphs = section.find_all('p')

        # 随机选择要插入图片的段落
        num_images = min(len(image_urls), len(paragraphs))
        selected_paragraphs = random.sample(paragraphs, num_images)

        # 插入图片
        for i, paragraph in enumerate(selected_paragraphs):
            figure = soup.new_tag('figure')
            figure['data-tool'] = 'wenhaofree'
            figure['style'] = 'margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center;'
            img = soup.new_tag('img', src=image_urls[i])
            img['style'] = 'display: block; margin: 0 auto; max-width: 100%; border: none; border-radius: 0; object-fit: fill; box-shadow: none;'
            figcaption = soup.new_tag('figcaption')
            # figcaption.string = f'图片 {i + 1}'
            figcaption['style'] = 'color: rgb(136, 136, 136); font-size: 14px; line-height: 1.5em; text-align: center; font-weight: normal; margin: 5px 0 0; padding: 0;'
            figure.append(img)
            figure.append(figcaption)
            paragraph.insert_before(figure)

        # 处理标题
        for tag in section.find_all(['h2', 'h3']):
            tag['data-tool'] = 'wenhaofree'
            tag['style'] = 'margin: 30px 0 15px; padding: 0; display: flex;'
            content = tag.string
            tag.string = ''
            span = soup.new_tag('span', **{'class': 'content'})
            span.string = content
            if tag.name == 'h2':
                span['style'] = 'font-size: 22px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block;'
            else:
                span['style'] = 'font-size: 20px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block;'
            tag.append(span)

        # 处理段落
        for p in section.find_all('p'):
            p['data-tool'] = 'wenhaofree'
            p['style'] = 'color: rgb(89, 89, 89); font-size: 15px; line-height: 1.8em; letter-spacing: 0.04em; text-align: left; text-indent: 0; margin: 0; padding: 8px 0;'

        # 处理列表
        for ul in section.find_all('ul'):
            ul['data-tool'] = 'wenhaofree'
            ul['style'] = 'list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: rgb(0, 0, 0);'
            for li in ul.find_all('li'):
                li_section = soup.new_tag('section')
                li_section['style'] = 'margin: 5px 0; color: rgb(89, 89, 89); font-size: 15px; line-height: 1.8em; letter-spacing: 0.04em; text-align: left; font-weight: normal;'
                li_section.extend(li.contents)
                li.clear()
                li.append(li_section)

        # 处理链接
        for a in section.find_all('a'):
            a['style'] = 'color: rgb(53, 179, 120); font-weight: bold; text-decoration: none; border-bottom: 1px solid rgb(53, 179, 120);'

        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup.prettify()))
        
        print(f'HTML文件已保存至: {output_file_path}')

        return True
    except Exception as e:
        print(f"生成HTML文件时发生错误: {e}")
        return False


def markdown_to_html_with_images(markdown_content, output_file_path, image_urls):
    """针对图片格式的生成HTML"""
    try:
        # 创建BeautifulSoup对象
        soup = BeautifulSoup('<html><head></head><body></body></html>', 'html.parser')
        
        # 添加样式
        style = soup.new_tag('style')
        style.string = '''
        body {font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji";}
        .markdown-body {box-sizing: border-box;min-width: 200px;max-width: 980px;margin: 0 auto;padding: 45px;}
        @media (max-width: 767px) {.markdown-body {padding: 15px;}}
        '''
        soup.head.append(style)
        
        # 创建主体部分
        section = soup.new_tag('section', **{'class': 'markdown-body'})
        soup.body.append(section)
        
        # 处理Markdown内容
        if markdown_content.strip():
            html_content = markdown.markdown(markdown_content)
            section.append(BeautifulSoup(html_content, 'html.parser'))
        
        # 处理图片
        for idx, img_url in enumerate(image_urls):
            figure = soup.new_tag('figure')
            figure['style'] = 'margin: 0; padding: 0; text-align: center;'
            
            img = soup.new_tag('img')
            img['src'] = img_url
            img['style'] = 'max-width: 100%; height: auto; margin: 0 auto; display: block;'
            
            figcaption = soup.new_tag('figcaption')
            figcaption['style'] = 'color: #999; font-size: 14px; text-align: center; margin-top: 5px;'
            # figcaption.string = f'图 {idx + 1}'
            
            figure.append(img)
            figure.append(figcaption)
            section.append(figure)
        
        # 处理标题
        for tag in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            tag['style'] = 'margin: 30px 0 15px; padding: 0; color: #35b378; font-weight: bold;'
        
        # 处理段落
        for p in section.find_all('p'):
            p['style'] = 'color: #595959; font-size: 15px; line-height: 1.8em; margin: 0; padding: 8px 0;'
        
        # 处理列表
        for ul in section.find_all('ul'):
            ul['style'] = 'list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: #000;'
            for li in ul.find_all('li'):
                li['style'] = 'margin: 5px 0; color: #595959; font-size: 15px; line-height: 1.8em;'
        
        # 处理链接
        for a in section.find_all('a'):
            a['style'] = 'color: #35b378; font-weight: bold; text-decoration: none; border-bottom: 1px solid #35b378;'
        
        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup.prettify()))
        
        print(f'HTML文件已保存至: {output_file_path}')
        return True
    except Exception as e:
        print(f"生成HTML文件时发生错误: {e}")
        return False


def main():
    markdown_content = """
近日，WTA1000中国网球公开赛第三轮比赛中，中国老将张帅依靠着出色的技巧和经验，与比利时球员明尼恩展开了一场激烈的拼搏。**张帅在两盘比赛中发挥出色，以6-2和6-3的比分战胜对手，成功挺进16强。**

福地中网结束了张帅的24连败，很多人以为这是她职业生涯的最后一刻。但是，张帅并没有因此而放弃，她在次轮比赛中爆出大冷门，直落两盘淘汰了美国富家女兼6号种子纳瓦罗。这场比赛不仅让张帅重拾信心，也间接帮助了郑钦文冲击年终总决赛资格。

在接受采访时，张帅曾表示："如果能够给郑钦文冲击年终总决赛起到作用，自己非常高兴。"这句话表明她仍然具备着极强的竞技意识和团队精神。现在，张帅已经实现了她重新崛起的目标，成为了中国网坛的一颗巨星。

**第三轮的对手明尼恩，并不寻常。**她在上一轮比赛中爆冷淘汰了种子球员波塔波娃，因此遭到了外界的关注。但是，相比于比利时人，张帅的经验和实力更为出色。显然，她很可能迎来连胜的步伐。与此同时，网坛也在期待着中国金花的复苏。

比赛开始，明尼恩先发球，前四局双方均未失发球局。第5局，张帅在对手发球局逼出两个连续破发点，高效兑现率先取得破发。这个关键局战至5-2领先，将局分优势扩大至6-2。如此多变的场面变化，让大家深刻领悟到张帅为何能取得最终的胜利。

进入第二盘比赛，两人便展开破发大战。第一次发球局，张帅立即取得破发得手。明尼恩也在次个 发球局中回敬破发力，并将比赛推入到白热化。第四发球局，让人眼前一亮；张帅又得到领先二次破发局完成大逆转。随后，在张帅的防守面前，保发建立3-1优势。

张帅始终紧握着主动权，不断地将自己所积累的优势推向对手。明尼恩一次次地破发，但她一次次地被对手挡下。这让观众们始终处于紧张的状态。

第七局，张帅再次破发成功。发球局，尽管浪费两个盘点，她仍然把握住第四个机会，以6-3的比分拿下比赛。最终经过两盘比赛，张帅再次战胜了对手。**她一次次地顶住压力，创造出中国网坛史上的奇迹。**此次表现，她完成了对自己职业生涯的救赎，也使得人们对中国女子网球寄予了厚望。

那么，中国金花的张帅还会如何继续发挥？她将如何面对下一轮的对手呢？我们的答案无疑是：**继续进取，创造奇迹！**

**你能预测张帅下一轮的对手吗？她是否能够再创奇迹呢？**
"""
    output_path = "output.html"
    image_urls = ['http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSyV1M9L1G8VgIibQ7lFX2a4wLoicdKEg1RHNRSqEYGfMbiavah689L04TA/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSia3ef27qq994qkibWKTGSS9Oyq6ichzLr7304SgvAXgxe1rjju8YBbuNQ/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSqBNeibM20ia7M5QvpYmiaF4ad6V4b59uOl1jSO278icuvZQEsdeZT2mRxA/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSR2pZEvV5BIPVbtgLH3G2HHIN2p5U7jcNyhib0GFCv8vYdhSia7IxABHw/0?from=appmsg']
    result = markdown_to_html(markdown_content, output_path, image_urls)
    print(f"HTML生成{'成功' if result else '失败'}")

# 示例使用
if __name__ == "__main__":
    main()