import reader_ai_util,re_util,downimg_util,ddg_util,os
'''下载公众号文章内的图片'''

def down_img(page_url:str,save_file_path:str):
    data=reader_ai_util.parse_url_data(url=page_url)
    # data=None
    if data is None:
        ddg = ddg_util.ddgs_client()
        data = ddg.search_crawler(url=page_url)
        origin_title = data['title']
        content = data['content']
        if '离线日志' in origin_title:
            print(f'{origin_title} 跳过. url:{page_url}')
            return
        
    origin_title = data['title']
    content = data['content']
        
    images = re_util.get_img_urls(content)
    content = re_util.get_content(content)
    save_file_path=f'{save_file_path}/{origin_title}'
    # 如果文件夹不存在就创建
    if not os.path.exists(save_file_path):
        os.makedirs(save_file_path)
    for img in images:
        downimg_util.save_image_from_url(url=img,save_path=save_file_path)
    print('下载完成')

if __name__ == '__main__':
    '''解析文章方式下载,不全面,参照获取HTML方式'''
    page_url='http://mp.weixin.qq.com/s?__biz=Mzk0NDY5ODUyMg==&mid=2247484879&idx=2&sn=3c1fcdc38affebe1de81543ee5759ffc&chksm=c2acdff82cfa74c17e19b11d684581969e7a4dc64f03d177982a72d092d79c318557969e751f&scene=0&xtrack=1#rd'
    save_file_path = f'/Users/<USER>/temp/images/9224'
    down_img(page_url=page_url,save_file_path=save_file_path)





