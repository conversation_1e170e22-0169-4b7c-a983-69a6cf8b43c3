import os
import re
import sys
import uuid
import random
import codecs

from PIL import Image
from docx import Document
from docx.shared import Inches
from docx.shared import Pt, RGBColor
# from watermark_util import handle_image
# from compress_image_util import process_image
from .watermark_util import handle_image
from .compress_image_util import process_image


sys.path.append(os.path.dirname(__file__))

import requests
from bs4 import BeautifulSoup


def get_os():
    if os.name == 'nt':
        return 'Windows'
    elif os.name == 'posix':
        return 'macOS'
    else:
        return 'Unknown'


class article:
    def __init__(self) -> None:
        print("article init")
        pass

    def get_img_url(self, keyword):
        img_url_list = []
        try:
            """发送请求，获取接口中的数据"""
            # 接口链接
            url = 'https://image.baidu.com/search/acjson?'
            # 请求头模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36'}
            # 构造网页的params表单
            params = {
                'tn': 'resultjson_com',
                'logid': '6918515619491695441',
                'ipn': 'rj',
                'ct': '201326592',
                'is': '',
                'fp': 'result',
                'queryWord': f'{keyword}',
                'word': f'{keyword}',
                'cl': '2',
                'lm': '-1',
                'ie': 'utf-8',
                'oe': 'utf-8',
                'adpicid': '',
                'st': '-1',
                'z': '',
                'ic': '',
                'hd': '',
                'latest': '',
                'copyright': '',
                's': '',
                'se': '',
                'tab': '',
                'width': '',
                'height': '',
                'face': '0',
                'istype': '2',
                'qc': '',
                'nc': '1',
                'fr': '',
                'expermode': '',
                'force': '',
                'cg': 'girl',
                'pn': 1,
                'rn': '30',
                'gsm': '1e',
            }
            # 携带请求头和params表达发送请求
            response = requests.get(url=url, headers=headers, params=params)
            # 设置编码格式
            response.encoding = 'utf-8'
            # 转换为json
            json_dict = response.json()
            # 定位到30个图片上一层
            data_list = json_dict['data']
            # 删除列表中最后一个空值
            del data_list[-1]
            # 用于存储图片链接的列表

            for i in data_list:
                img_url = i['thumbURL']
                # 打印一下图片链接
                # print(img_url)
                img_url_list.append(img_url)
            # 返回图片列表
            return img_url_list[:6]
        except Exception as e:
            print(f'获取图片异常:{e}')
            return img_url_list

    def save_image_from_url(self, url, save_path):
        """从URL保存图片到本地"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36'
        }
        response = requests.get(url, stream=True, headers=headers, timeout=5)
        # response.raise_for_status()
        # 检查请求是否成功
        if response.status_code == 200:
            # 打开文件并以二进制模式写入
            with open(save_path, 'wb') as file:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        file.write(chunk)
            print(f'下载图片成功: {url}, 保存:{save_path}')
            return True
        else:
            return False

    def crop_image(self, image_path):
        # 打开图片
        img = Image.open(image_path)
        width, height = img.size

        # 计算裁剪的边界
        left = width * 0.01
        top = height * 0.1
        right = width * 0.9
        bottom = height * 0.9

        # 裁剪图片
        img_cropped = img.crop((left, top, right, bottom))

        img_cropped.save(image_path)

    def add_paragraph_with_heading(self, doc, string):
        # 使用正则表达式查找井号的数量
        match = re.match(r'^(#+)\s*(.*)$', string)
        if match:
            # 提取井号的数量和文本
            hash_count = len(match.group(1))
            text = match.group(2).strip()
            # 添加标题
            if hash_count > 0:
                # self.add_heading(doc, text, hash_count)
                heading = doc.add_heading(text, hash_count)
                for run in heading.runs:
                    run.font.name = 'SimSun'  # 设置字体为正体宋体
                    run.font.size = Pt(16)  # 设置字体大小为10.5磅
                    run.font.color.rgb = RGBColor(53, 179, 120)  # 设置颜色为RGB(53, 179, 120)
                    run.bold = True  # 设置为粗体
            else:
                # 如果没有井号，则添加普通段落
                # self.add_paragraph(doc, text)
                doc.add_paragraph(text)
        else:
            # 如果没有匹配到井号，则添加普通段落
            if string.startswith('****') and string.endswith('****'):
                paragraph = doc.add_paragraph()
                bold_text = string[4:-4]
                run = paragraph.add_run(bold_text)
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                # run.font.color.rgb = RGBColor(53, 179, 120) 绿色
                run.font.color.rgb = RGBColor(50, 143, 48)
                paragraph.add_run(' ')  # 添加空格
            elif string.startswith('**') and string.endswith('**'):
                paragraph = doc.add_paragraph()
                bold_text = string[2:-2]
                run = paragraph.add_run(bold_text)
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                # run.font.color.rgb = RGBColor(53, 179, 120) 绿色
                run.font.color.rgb = RGBColor(50, 143, 48)
                paragraph.add_run(' ')  # 添加空格
            elif '**' in string:
                pattern = r'\*\*(.*?)\*\*'
                paragraph = doc.add_paragraph()  # 不需要添加空格,需要紧凑
                matches = re.findall(pattern, string)
                for stringCont in re.split(pattern, string):
                    if stringCont:
                        if stringCont in matches:
                            # bold_text = re.match(pattern, stringCont).group(1)
                            bold_text = matches[0]
                            run = paragraph.add_run(bold_text)
                            run.font.name = u"SimSun"  # 正体宋体
                            run.bold = True
                            run.font.size = Pt(10.5)
                            run.font.color.rgb = RGBColor(50, 143, 48)
                            paragraph.add_run(' ')
                        else:
                            run = paragraph.add_run(stringCont + ' ')
                            run.font.name = u"SimSun"  # 正体宋体
                            run.font.size = Pt(11.5)
                            gray_color = RGBColor(89, 89, 89)  # 灰色-65%的RGB值
                            run.font.color.rgb = gray_color

                # self.add_bold_text(doc=doc, text=string)
            else:
                paragraph = doc.add_paragraph()  # 不需要添加空格,需要紧凑
                run = paragraph.add_run(string)
                # 设置字体为宋体
                run.font.name = u"SimSun"  # 正体宋体
                # 设置字号为11.5磅
                run.font.size = Pt(11.5)
                # 设置字体颜色为灰色-65%
                # RGB颜色值范围是0-255，所以这里我们需要计算出灰色-65%对应的RGB值
                gray_color = RGBColor(89, 89, 89)  # 灰色-65%的RGB值
                run.font.color.rgb = gray_color
                # doc.add_paragraph(string)

    def add_bold_text(self, doc, text):
        # 初始化一个段落
        paragraph = doc.add_paragraph()
        run = paragraph.add_run()
        # 遍历文本中的每个字符
        i = 0
        while i < len(text):
            # 检查是否遇到加粗标记
            if text[i:i + 2] == '**':
                # 切换加粗状态
                run.bold = not run.bold
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                run.font.color.rgb = RGBColor(53, 179, 120)
                # 跳过加粗标记
                i += 2
            else:
                # 添加字符到运行对象
                run.add_text(text[i])
                i += 1

    def create_word_doc(self, file_path, strings, image_urls, name, origin_title, area):
        try:
            """创建Word文档并插入字符串和图片"""
            # 创建一个新的Word文档
            strings = strings[1]
            doc = Document()
            # doc.add_picture("/Users/<USER>/temp/images/guanzhu.png", width=Inches(6.5))#添加关注图片   影刀添加图片会首选这第一个

            name = name.strip()
            # 临时文件夹用于存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            sections = strings.split("\n\n")
            # 对每个分割后的部分，使用split("\n")分割
            for section in sections:
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 添加百度搜索图片
            baidu_urls = self.get_img_url(origin_title)
            image_urls.extend(baidu_urls)
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                        # 添加图片到Word文档
                        # doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                # 如果图片URL的数量足够，从URL保存图片到临时文件夹
                try:
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                # doc.add_picture("./公众号下方广告.png", width=Inches(6.5))
                save_doc_path = file_path.format(area) + "/待发布/" + name + ".docx"
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
                name = re.sub(r'[/:*?"<>|rn]+', "_", name)
                print("num_and_punctuations:" + str(name))
                doc.save(file_path.format(area) + "/待发布/" + name + ".docx")
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False


    def create_word_doc_content_tiyu(self, file_path, contents, image_urls):
        try:
            """创建Word文档并插入字符串和图片"""
            doc = Document()
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        # self.crop_image(image_save_path)#裁剪
                        handle_image(path=image_save_path)  # 去水印
                    contents = contents.replace(img, image_save_path)
                except Exception:
                    continue

            # 遍历每个字符串和对应的图片URL
            imglist = os.listdir(temp_dir)
            imgidx = 0
            doccontlist = [line for section in contents.split("\n\n") for line in section.split("\n")]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                if '[Image]' not in string and '图片' != string:
                    self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    # 手动:
                    if '[Image]' in string:
                        pattern = r'\!\[.*?\]\((.*?)\)'
                        match = re.search(pattern, string)
                        if match:
                            image_link = match.group(1)
                            print("图像链接:", image_link)
                            doc.add_picture(image_link, width=Inches(6.5))  # 可以调整宽度                        
                    # 自动:
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    elif imgidx == 1 and len(imglist) == 1:
                        doc.add_picture(temp_dir + "/" + imglist[0], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False


    def create_html_content_tiyu(self, file_path, contents, image_urls):
        try:
            """创建HTML文档并插入内容和图片"""
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)

            # 下载并处理图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        handle_image(path=image_save_path)  # 去水印
                    contents = contents.replace(img, image_save_path)
                except Exception:
                    continue

            # 创建HTML文档
            soup = BeautifulSoup('<html><head><title>体育新闻</title></head><body></body></html>', 'html.parser')
            section = soup.new_tag('section', id='nice', **{
                'data-tool': 'mdnice编辑器',
                'data-website': 'https://www.mdnice.com',
                'style': 'margin: 0; padding: 10px; background: none; width: auto; font-family: Optima, "Microsoft YaHei", PingFangSC-regular, serif; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em; word-spacing: 0; letter-spacing: 0; word-break: break-word; overflow-wrap: break-word; text-align: left;'
            })
            soup.body.append(section)

            # 添加全局样式
            style = soup.new_tag('style')
            style.string = """
            h2, h3 { margin: 30px 0 15px; padding: 0; display: flex; }
            h2 .content { font-size: 22px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block; }
            h3 .content { font-size: 20px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block; }
            p { color: rgb(89, 89, 89); font-size: 15px; line-height: 1.8em; letter-spacing: 0.04em; padding: 8px 0; }
            figure { margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center; }
            figure img { display: block; max-width: 100%; margin: 0 auto; border: none; border-radius: 0; object-fit: fill; box-shadow: none; }
            figcaption { color: rgb(136, 136, 136); font-size: 14px; line-height: 1.5em; text-align: center; font-weight: normal; margin: 5px 0 0; padding: 0; }
            ul { list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: rgb(0, 0, 0); }
            li section { margin: 5px 0; color: rgb(89, 89, 89); font-size: 15px; line-height: 1.8em; letter-spacing: 0.04em; text-align: left; font-weight: normal; }
            a { color: rgb(53, 179, 120); font-weight: bold; text-decoration: none; border-bottom: 1px solid rgb(53, 179, 120); }
            """
            soup.head.append(style)

            # 处理内容
            lines = contents.split('\n')
            for line in lines:
                if line.startswith('## '):
                    h2 = soup.new_tag('h2')
                    span = soup.new_tag('span', **{'class': 'content'})
                    span.string = line[3:]
                    h2.append(span)
                    section.append(h2)
                elif line.startswith('### '):
                    h3 = soup.new_tag('h3')
                    span = soup.new_tag('span', **{'class': 'content'})
                    span.string = line[4:]
                    h3.append(span)
                    section.append(h3)
                elif line.startswith('!['):
                    img_match = re.search(r'\!\[(.*?)\]\((.*?)\)', line)
                    if img_match:
                        figure = soup.new_tag('figure')
                        img = soup.new_tag('img', src=img_match.group(2), alt=img_match.group(1))
                        figcaption = soup.new_tag('figcaption')
                        figcaption.string = img_match.group(1)
                        figure.append(img)
                        figure.append(figcaption)
                        section.append(figure)
                elif line.startswith('- '):
                    if not section.find('ul'):
                        ul = soup.new_tag('ul')
                        section.append(ul)
                    li = soup.new_tag('li')
                    li_section = soup.new_tag('section')
                    li_section.string = line[2:]
                    li.append(li_section)
                    section.find('ul').append(li)
                elif line.strip():
                    p = soup.new_tag('p')
                    p.string = line
                    section.append(p)

            # 保存HTML文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(str(soup.prettify()))
            print(f'保存路径:{file_path}')

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_keji(self, file_path, contents, image_urls):
        try:
            """创建Word文档并插入字符串和图片"""
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                    contents = contents.replace(img, image_save_path)
                except Exception:
                    continue

            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                if '[Image]' not in string and '图片' != string:
                    self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    # 手动:
                    if '[Image]' in string:
                        pattern = r'\!\[.*?\]\((.*?)\)'
                        match = re.search(pattern, string)
                        if match:
                            image_link = match.group(1)
                            print("图像链接:", image_link)
                            doc.add_picture(image_link, width=Inches(6.5))  # 可以调整宽度                        
                    # 自动:
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    elif imgidx == 1 and len(imglist) == 1:
                        doc.add_picture(temp_dir + "/" + imglist[0], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    '''
    路径
    Markdown正文
    图片URLs
    标题
    领域:体育
    '''

    def create_word_doc_content(self, file_path, contents, image_urls, title, area):
        try:
            """创建Word文档并插入字符串和图片"""
            strings = contents
            doc = Document()

            name = title.strip()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            sections = strings.split("\n\n")
            # 对每个分割后的部分，使用split("\n")分割
            for section in sections:
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                        # 添加图片到Word文档
                        # doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception:
                    continue

            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                # 如果图片URL的数量足够，从URL保存图片到临时文件夹
                try:
                    # 一行添加一张图片
                    # if imgidx<len(imglist):
                    #     doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    # 添加两个空段落以实现两个换行
                    # doc.add_paragraph()
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            # 剩余图片也添加进去
            if imgidx < len(imglist):
                for i in range(imgidx, len(imglist)):  # 从 start_number 开始，到 9 结束
                    doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    # 添加两个空段落以实现两个换行
                    doc.add_paragraph()
                    imgidx += 1

            try:
                # doc.add_picture("./公众号下方广告.png", width=Inches(6.5))
                # save_doc_path = file_path.format(area) + "/待发布/" + name + ".docx"
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
                name = re.sub(r'[/:*?"<>|rn]+', "_", name)
                print("num_and_punctuations:" + str(name))
                doc.save(file_path.format(area) + "/待发布/" + name + ".docx")
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def get_max_number_image(self, img_list):
        """返回最大值的图片-获取封面图片"""
        max_number = -1
        max_image = None

        for img in img_list:
            # 使用正则表达式提取图片名称中的数字
            match = re.search(r'\d+', img)
            if match:
                number = int(match.group())
                if number > max_number:
                    max_number = number
                    max_image = img

        return max_image

    def create_word_doc_content_wenan(self, file_path, contents, image_urls):
        """文案定制文档生成"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            imgidx = 0

            # 添加字符串到Word文档
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                if idx == 1:
                    imgname = self.get_max_number_image(img_list=imglist)
                    doc.add_picture(temp_dir + "/" + imgname, width=Inches(6.5))  # 先添加封面,最后一张是封面

                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx <= len(imglist) and idx % 2 == 0:
                        # random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                        # doc.add_picture(temp_dir + "/" + imglist[random_index], width=Inches(6.5))  # 可以调整宽度
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            # 剩余图片也添加进去
            if imgidx < len(imglist):
                for i in range(imgidx, len(imglist)):  # 从 start_number 开始，到 9 结束
                    doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    # 添加两个空段落以实现两个换行
                    doc.add_paragraph()
                    imgidx += 1

            try:
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
                # 计算Word文件大小
                size_in_bytes = os.path.getsize(save_doc_path)
                size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
                if size_in_mb > 15:
                    print(f'文件大小超过15MB,删除文件:{save_doc_path}')
                    os.remove(save_doc_path)
                else:
                    print(f'文件大小:{size_in_mb}MB')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Cleaning up temporary successfully")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_meinv(self, file_path, contents, image_urls):
        """美女定制文档生成-图文"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave: self.crop_image(image_save_path)
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            imgidx = 0

            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx < len(doccontlist) - 1 and idx > len(doccontlist) - 2:
                        continue  # 最后一行不添加图片了
                    # 如果图片URL的数量足够，从URL保存图片到临时文件夹
                    if idx <= len(imglist) and idx % 2 == 0:
                        random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                        doc.add_picture(temp_dir + "/" + imglist[random_index], width=Inches(6.5))  # 可���调整宽度
                        # doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            # 剩余图片也添加进去
            if imgidx < len(imglist):
                for i in range(imgidx, len(imglist)):  # 从 start_number 开始，到 9 结束
                    doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    # 添加两个空段落以实现两个换行
                    doc.add_paragraph()
                    imgidx += 1

            try:
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
                # 计算Word文件大小
                size_in_bytes = os.path.getsize(save_doc_path)
                size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
                if size_in_mb > 15:
                    print(f'文件大小超过15MB,删除文件:{save_doc_path}')
                    os.remove(save_doc_path)
                else:
                    print(f'文件大小:{size_in_mb}MB')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Cleaning up temporary successfully")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_meinv_onlyImage(self, file_path, image_urls):
        """美女定制文档生成-只要图片"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            file_name_with_ext = os.path.basename(file_path)
            title = os.path.splitext(file_name_with_ext)[0]
            temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\公众号\\美女\\{title}' if get_os == "Windows" else f'/Volumes/文章存档/Images/公众号/美女/{title}'
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    # if ifsave: self.crop_image(image_save_path) #不裁剪了 走水印
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            # 添加图片
            if 0 < idx <= len(imglist):
                for i in range(len(imglist) - 1, -1, -1):  # 从最后一张开始到第一张
                    img_path = temp_dir + "/" + imglist[i]
                    # 压缩图片后再添加
                    process_image(img_path,img_path)
                    # img_path = handle_image(path=img_path) #底部水印处理
                    doc.add_picture(img_path, width=Inches(6.5))  # 可以调整宽度

            doc.save(file_path)
            print("下载到：" + file_path)
            # 计算Word文件大小
            size_in_bytes = os.path.getsize(file_path)
            size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
            if size_in_mb > 15:
                print(f'文件大小:{size_in_mb},超过15MB,删除文件:{file_path}')
                os.remove(file_path)
            else:
                print(f'{file_path}的文件大小:{size_in_mb}MB')
            return True
        except Exception as e:
            print(f'文档异常:{e}')
        return False

    def create_word_doc_content_yuer(self, file_path, contents, temp_dir: str):
        """育儿定制文档生成"""
        try:
            doc = Document()
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)
            imglist = os.listdir(temp_dir)
            imageFlag = False
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    # 如果下一个是**开头的,就插入图片
                    if imageFlag: continue
                    if 0 <= idx < len(doccontlist):
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                            imageFlag = True
                except Exception as e:
                    print("peitu.py---146" + str(e))
            doc.save(file_path)
            print(f'保存路径:{file_path}')
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_qinggan(self, file_path, contents, temp_dir: str):
        """情感定制文档生成"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            # temp_dir = "./temp_images/" + str(uuid.uuid4())
            # if not os.path.exists(temp_dir):os.makedirs(temp_dir)

            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 下载图片图片
            # for idx, img in enumerate(image_urls):
            #     image_save_path = os.path.join(temp_dir, f"_{idx}.png")
            #     try:
            #         ifsave = self.save_image_from_url(img, image_save_path)
            #         if ifsave:
            #             self.crop_image(image_save_path)
            #             # 添加图片到Word文档
            #             # doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
            #     except Exception:
            #         continue
            # temp_dir='/Users/<USER>/temp/images/插画情侣'
            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx < len(doccontlist) - 1 and idx > len(doccontlist) - 2:
                        continue  # 最后一行不添加图片了
                        # 如果下一个是**开头的,就插入图片
                    if 0 <= idx < len(doccontlist):
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            self.crop_image(image_save_path)
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                        # 如果图片URL的数量足够，从URL保存图片到临时文件夹-偶数才插入图片
                        # if idx <= len(imglist) and idx % 2 == 0:
                        #     # doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_keji01(self, file_path, contents, temp_dir: str, image_urls: list):
        """科技定制文档生成"""
        try:
            doc = Document()
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave: self.crop_image(image_save_path)
                except Exception:
                    continue

            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if len(imglist) != 0:  # 图片不为空
                        if idx <= len(imglist):
                            doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            doc.save(file_path)
            print(f'保存路径:{file_path}')
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_zhichang(self, file_path, contents, temp_dir: str):
        """职场定制文档生成"""
        try:
            doc = Document()
            imglist = os.listdir(temp_dir)
            imgidx = 0

            doccontlist = [line for section in contents.split("\n\n") for line in section.split("\n")]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                self.add_paragraph_with_heading(doc=doc, string=string)  # 添加到文档
                try:
                    if len(imglist) != 0:  # 图片不为空
                        if idx < len(doccontlist) and idx == 1:  # 只插入一张-用作封面  第一行段落之后添加图片
                            # if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            # self.crop_image(image_save_path) # 不裁剪图片了!! 有水印在处理!!
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            doc.save(file_path)
            print(f'保存路径:{file_path}')
            print("Word document created successfully!")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_xingzuo(self, file_path, contents, temp_dir: str):
        """星座命理定制文档生成"""
        try:
            doc = Document()
            imglist = os.listdir(temp_dir)
            imgidx = 0

            doccontlist = [line for section in contents.split("\n\n") for line in section.split("\n")]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                self.add_paragraph_with_heading(doc=doc, string=string)  # 添加到文档
                try:
                    if len(imglist) != 0:  # 图片不为空
                        if idx < len(doccontlist) and idx == 1:  # 只插入一张-用作封面  第一行段落之后添加图片
                            # if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            # self.crop_image(image_save_path) # 不截图了
                            handle_image(image_save_path)  # 去除水印
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_chuangye(self, file_path, contents, temp_dir: str):
        """创业定制文档生成"""
        try:
            doc = Document()
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            imglist = os.listdir(temp_dir)
            imgidx = 0
            image_flog = False
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if 0 <= idx < len(doccontlist):
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):  # 只在小标题前面插入 一个
                            if image_flog:
                                continue
                            # if imgidx == 1:  # 只插入一张-用作封面
                            if imglist:
                                random_index = random.randint(0, len(imglist) - 1)
                                image_save_path = temp_dir + "/" + imglist[random_index]
                                # self.crop_image(image_save_path)
                                doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                                image_flog = True

                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")
            # 清理临时图片文件夹
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_image(self, file_path, image_urls, title, area):
        try:
            """创建Word文档并插入字符串和图片"""
            doc = Document()
            name = title.strip()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                        # 添加图片到Word文档
                        doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                        # 添加两个空段落以实现两个换行
                        doc.add_paragraph()
                        doc.add_paragraph()
                except Exception:
                    continue

            try:
                # doc.add_picture("./公众号下方广告.png", width=Inches(6.5))
                save_doc_path = file_path.format(area) + "/待发布/" + name + ".docx"
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
                name = re.sub(r'[/:*?"<>|rn]+', "_", name)
                print("num_and_punctuations:" + str(name))
                doc.save(file_path.format(area) + "/待发布/" + name + ".docx")
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def delete_empty_docs_in_folder(self, folder_path):
        # 遍历文件夹中的所有文件
        for filename in os.listdir(folder_path):
            # if filename.endswith('.doc'):
            file_path = os.path.join(folder_path, filename)
            # 如果文件小于10kb
            threshold = 10 * 1024
            file_size = os.path.getsize(file_path)
            if file_size == 0 or file_size < threshold or filename == '.DS_Store':
                # 删除空文件
                os.remove(file_path)
                print(f"Deleted empty .doc file: {file_path}")

    def delete_empty_folder(self, folder_path):
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            # 如果是文件夹就删除
            if os.path.isdir(file_path):
                os.rmdir(file_path)
                print(f"Deleted empty folder: {file_path}")


# 清理空文件夹
if __name__ == '__main__':
    data = [
        {'keji': '科技'},
        {'tiyu': '体育'},
        {'tiyu': '体育01'},
        {'shehui': '社会'},
        {'qinggan': '情感'},
        {'yule': '娱乐'},
        {'sannong': '三农'},
        {'wenan': '文案'},
        {'touxiang': '头像'},
    ]
    at = article()
    content = """
在当今快速发展的手机市场中,芯片性能无疑是决定手机整体表现的关键因素之一。近日,高通公司发布了最新旗舰芯片天玑9400,而苹果公司也推出了M2芯片,二者的对比引发了业界的广泛关注。

天玑9400采用了先进的4nm工艺,集成了更多的transistor,CPU性能相比上一代提升了**35%**,GPU性能提升了**25%**,AI性能提升了**60%**。与此同时,天玑9400还支持第五代AI引擎和第七代AI引擎,可以带来更加智能化的用户体验。此外,天玑9400还集成了第四代5G基带,支持全球各地的5G网络。

而苹果公司的M2芯片则采用了**5nm**工艺,其CPU和GPU的性能相比M1芯片分别提升了**18%**和**35%**。M2芯片还集成了神经网络引擎,可以在机器学习任务上带来更加出色的表现。值得一提的是,M2芯片还支持统一内存架构,可以让CPU、GPU和神经网络引擎共享内存,从而提高整体性能和效率。

不可否认,天玑9400和M2芯片都是当前移动芯片领域的佼佼者。它们的性能提升和技术创新,将为消费者带来更加流畅、智能、高效的使用体验。但是,我们也要看到,芯片性能的提升往往伴随着**更高的成本**和**更高的售价**。手机厂商为了追求更好的性能,不得不采用更加昂贵的芯片,而这些成本最终都会转嫁到消费者身上。

有人可能会说,性能提升带来的体验改善是值得付出更高价格的。但是,我们也要考虑到,手机作为一种快速迭代的消费电子产品,其更新换代的周期越来越短。消费者往往还没有充分享受到当前手机的性能,就又被新一代产品所吸引。这种快速迭代的模式,不仅加重了消费者的经济负担,也造成了巨大的资源浪费。

另一方面,我们也要思考,手机性能的提升是否已经到了一个瓶颈期。对于大多数普通用户来说,当前主流手机的性能已经足够应对日常使用需求。过度追求性能提升,可能带来的是边际效用递减。手机厂商和芯片厂商,是否应该将更多的精力放在**用户体验的优化**、**软件生态的建设**、**创新应用的开发**等方面,而不是一味地追求硬件性能的提升呢?

天玑9400和M2芯片的对比,给了我们一个思考手机行业发展方向的机会。在这场芯片之战的背后,是手机厂商对市场份额的争夺,是芯片厂商对技术领先的追求,更是消费者对更好体验的向往。但是,我们也要警惕性能提升背后的**成本上升**、**资源浪费**、**边际效用递减**等问题。

手机,究竟应该走向何方?是继续追求性能的提升,还是转向体验的优化?是加速产品的迭代,还是放缓新的步伐?这些问题,值得每一个手机厂商、芯片厂商、以及消费者深思。
"""
    # at.create_word_doc_content_keji(file_path='/Volumes/文章存档/情感/待发布/ll.docx', contents=content,
    #                                    temp_dir='/Users/<USER>/temp/images/插画情侣')
    doccontlist = []
    sections = content.split("\n\n")
    # 对每个分割后的部分，使用split("\n")分割
    for section in sections:
        lines = section.split("\n")
        for line in lines:
            doccontlist.append(line)
    doc = Document()
    for string in doccontlist:
        at.add_paragraph_with_heading(doc, string)

    save_doc_path = '/Volumes/文章存档/情感/待发布/ll.docx'
    doc.save(save_doc_path)
    print(f'保存路径:{save_doc_path}')
    # import system_util
    # os_type = system_util.get_os()
    # # 遍历数据
    # for item in data:
    #     for key, value in item.items():
    #         print(f"键: {key}, 值: {value}")
    #         area=value   
    #         save_file_path = rf'D:\\BaiduSyncdisk\\文章存档\\{area}' if os_type == "Windows" else f'/Volumes/文章存档/{area}'   #局域网同步
    #         at.delete_empty_folder(os.path.join(save_file_path, '待发布'))
    #         at.delete_empty_docs_in_folder(os.path.join(save_file_path, '待发布'))
    # print("删除空文件夹和空文件完成")
