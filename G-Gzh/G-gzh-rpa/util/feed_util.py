import json
import os
import re
import subprocess
import sys
import tempfile
import uuid
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET

sys.path.append(os.path.dirname(__file__))

import feedparser
import time
import requests
from requests.exceptions import RequestException


def get_exist_ids(file_path_json:None):
    # 根据ID校验是否已经保存过
    message_ids = set()
    if file_path_json is None:
        file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', f'ids-kejixun.json')
    else:
        file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', file_path_json)
    if os.path.exists(file_path) is False:
        print(f'文件不存在:{file_path}')
        return message_ids
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                json_data = json.loads(line)
                message_ids.add(json_data['id'])
            f.close()
    except Exception as e:
        print(f'更新异常:{e}')
        pass
    print(f'已保存{len(message_ids)}条数据')
    return message_ids


def save_fail_ids(title: str, hmctdocid: str, reseaon: str):
    file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', f'ids-kejixun.json')
    with open(file_path, 'a') as f:
        try:
            insert_notion_data = {'id': hmctdocid, 'title': title, 'msg': reseaon}
            json.dump(insert_notion_data, f)
            f.write('\n')
            f.close()
        except Exception as e:
            print(f'保存Notion异常:{e}')
            f.close()

def save_ids(title: str, hmctdocid: str, reseaon: str,file_path_json:str):
    file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', file_path_json)
    with open(file_path, 'a', encoding='utf-8') as f:
        try:
            insert_notion_data = {'id': hmctdocid, 'title': title, 'msg': reseaon}
            json.dump(insert_notion_data, f, ensure_ascii=False)
            f.write('\n')
            f.close()
        except Exception as e:
            print(f'保存Notion异常:{e}')
            f.close()

def check_feed_bleacherreport():
    # RSS feed URL
    # feed_url = 'https://bleacherreport.com/sitemaps/feed?tag_id=182'
    feed_url = 'https://rss.app/feeds/gBpGCA1y9V4l8tG7.xml'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    try:
        # 使用代理发送请求
        response = requests.get(feed_url, headers=headers)
        response.raise_for_status()  # 如果响应状态码不是200，将抛出HTTPError异常
        # 解析feed
        feed = feedparser.parse(response.text)
        # 检查是否有新的条目
        if 'entries' in feed and feed.entries:
            ids = get_exist_ids(None)
            for item in feed.entries:
                id = item.id
                if id in ids:
                    continue
                title = item.title
                link = item.link
                published = item.published
                image_url = item.media_content[0]['url']
                print(f'\n标题:{title},  时间:{item.published}')
                content = {
                    'id': id,
                    'title': title,
                    "link": link,
                    "published": published,
                    "image_url": image_url
                }
                return content
        return None, None, None
    except RequestException as e:
        print(f"Request error: {e}")


def check_feed_bleacherreport_list():
    """RSS feed URL"""
    feed_url = 'https://bleacherreport.com/sitemaps/feed?tag_id=19'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    try:
        response = requests.get(feed_url, headers=headers)
        response.raise_for_status()

        # 使用 XML 解析获取数据
        root = ET.fromstring(response.text)
        contents = []
        ids = get_exist_ids(None)

        # 遍历所有的 item 元素
        for item in root.findall('.//item'):
            id = item.find('guid').text
            if id in ids:
                continue

            title = item.find('title').text
            link = item.find('link').text
            published = item.find('pubDate').text
            
            # 获取图片URL
            image_url = None
            image_elem = item.find('.//image/link')
            if image_elem is not None:
                image_url = image_elem.text

            content = {
                'id': id,
                'title': title,
                'link': link,
                'published': published,
            }

            if image_url:
                content['image_url'] = image_url

            print(f'\n标题: {title}\n时间: {published}\n图片: {image_url if image_url else "无"}\n')
            contents.append(content)

        return contents
    except RequestException as e:
        print(f"Request error: {e}")
        return []
    except ET.ParseError as e:
        print(f"XML parsing error: {e}")
        return []


def check_feed_espn_list():
    """RSS feed URL"""
    feed_url = 'https://www.espn.com/espn/rss/nba/news'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    try:
        # 使用代理发送请求
        response = requests.get(feed_url, headers=headers)
        response.raise_for_status()  # 如果响应状态码不是200，将抛出HTTPError异常
        # 解析feed
        feed = feedparser.parse(response.text)
        # 检查是否有新的条目
        contents = []
        if 'entries' in feed and feed.entries:
            ids = get_exist_ids(None)
            for item in feed.entries:
                id = item.id
                if id in ids:
                    continue
                title = item.title
                link = item.link
                published = item.published
                # image_url=item.media_content[0]['url']
                print(f'\n标题:{title},  时间:{item.published}')
                content = {
                    'id': id,
                    'title': title,
                    "link": link,
                    "published": published,
                }
                if 'image/jpg' == item.links[0]['type']:
                    image_url = item.links[0]['href']
                    content = {
                        'id': id,
                        'title': title,
                        "link": link,
                        "published": published,
                        "image_url": image_url
                    }
                contents.append(content)
        return contents
    except RequestException as e:
        print(f"Request error: {e}")


def parse_image(content):
    """图片解析"""
    # 正则表达式匹配Markdown格式的图片链接
    image_url_pattern = re.compile(r'src=[\'"]?([^\'" >]+)')
    # 找到所有匹配的URL
    image_urls = re.findall(image_url_pattern, content)
    # 打印所有图片URL
    for url in image_urls:
        print(url)
        return url
    # return image_urls


def check_feed_kejixun_list():
    """ Kejixun RSS feed URL"""
    feed_url = 'https://www.kejixun.com/feed'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    # 代理设置
    proxies = {
        'http': 'http://127.0.0.1:7890',
        'https': 'http://127.0.0.1:7890',
    }
    try:
        # 使用代理发送请求
        # response = requests.get(feed_url, proxies=proxies)
        response = requests.get(feed_url, headers=headers)
        response.raise_for_status()  # 如果响应状态码不是200，将抛出HTTPError异常
        feed = feedparser.parse(response.text)
        # 检查是否有新的条目
        contents = []
        if 'entries' in feed and feed.entries:
            ids = get_exist_ids(None)
            for item in feed.entries:
                id = item.id
                if id in ids:
                    continue
                tag = item.tags[0].term
                if tag in ['手机']:
                    continue
                title = item.title
                link = item.link
                image_url = parse_image(content=item.content[0].value)
                if 'image/jpg' == item.links[0]['type']:
                    image_url = item.links[0]['href']
                published = item.published
                # image_url=item.media_content[0]['url']
                print(f'\n标题:{title},  时间:{item.published}')
                content = {
                    'id': id,
                    'title': title,
                    "link": link,
                    "published": published,
                    "image_url": image_url,
                    "tag": tag
                }
                contents.append(content)
        return contents
    except Exception as e:
        print(f"Request error: {e}")
        return None

# check_feed_kejixun_list()
# check_feed_bleacherreport()
# check_feed_bleacherreport_list()
