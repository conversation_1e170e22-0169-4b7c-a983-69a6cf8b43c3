import os
import requests
from urllib.parse import urlparse
import random
def image_download(page_picture:str,store_path:str):

    # Create an array with the URL of the image
    url_array = [page_picture]

    # Define the directory where you want to save the image
    # port = '9224'  # Replace with the actual port number or any other identifier
    # store_path = f'/Users/<USER>/temp/images/{port}/'

    # Create the directory if it doesn't exist
    os.makedirs(store_path, exist_ok=True)

    # Download the image
    response = requests.get(url_array[0])
    # Generate a random 12-digit number
    random_number = random.randint(10**11, 10**12 - 1)

    # Check if the request was successful
    if response.status_code == 200:
        # Extract the filename from the URL
        # filename = os.path.basename(urlparse(page_picture).path)
        filename = f"{random_number}.png"

        
        # Define the full path where the image will be saved
        image_path = os.path.join(store_path, filename)
        
        # Save the image to the specified directory
        with open(image_path, 'wb') as file:
            file.write(response.content)
        
        print(f"Image downloaded and saved to {image_path}")
        return image_path
    else:
        print("Failed to download the image")
        return None

    # If you need to return the path of the saved image, you can do so like this:
    # image_path = image_path