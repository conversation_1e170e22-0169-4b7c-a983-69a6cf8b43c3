import cv2
import numpy as np

def handle_image(path: str):
    """Remove WeChat article watermark including QR code and logo"""
    try:
        # Read the original image
        img = cv2.imread(path, 1)
        if img is None:
            raise Exception("Failed to read the original image")

        height, width = img.shape[:2]
        
        # Define smaller watermark region
        w_width = int(width * 0.4)  # Reduced from 0.4 to 0.25
        w_height = int(height * 0.05)  # Reduced from 0.3 to 0.2
        x = width - w_width
        y = height - w_height
        
        # Create main mask (single channel)
        mask = np.zeros((height, width), dtype=np.uint8)
        mask[y:height, x:width] = 255
        
        # Pre-process the watermark region
        roi = img[y:height, x:width]
        
        # Enhance contrast in ROI to better identify watermark
        lab = cv2.cvtColor(roi, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        lab = cv2.merge((l,a,b))
        roi_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        
        # Detect edges in the watermark region
        gray_roi = cv2.cvtColor(roi_enhanced, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray_roi, 100, 200)
        
        # Create detailed mask for QR code and logo region (single channel)
        qr_mask = np.zeros((height, width), dtype=np.uint8)
        qr_mask[y:height, x:width] = cv2.threshold(edges, 30, 255, cv2.THRESH_BINARY)[1]
        
        # Multiple pass inpainting
        # First pass: Remove strong edges (QR code and logo)
        img = cv2.inpaint(img, qr_mask, 15, cv2.INPAINT_TELEA)  # Reduced radius from 21 to 15
        
        # Second pass: Handle the semi-transparent background
        img = cv2.inpaint(img, mask, 7, cv2.INPAINT_NS)  # Reduced radius from 11 to 7
        
        # Third pass: Final refinement
        img = cv2.inpaint(img, mask, 3, cv2.INPAINT_TELEA)
        
        # Smooth the transition
        blur_mask = np.zeros((height, width, 3), dtype=np.uint8)
        border_width = 20  # Reduced from 30 to 20
        blur_mask[y-border_width:y+border_width, x-border_width:width] = 255
        blur_mask[y:height, x-border_width:x+border_width] = 255
        
        # Apply blurring for better blending
        blurred = cv2.GaussianBlur(img, (5,5), 0)  # Reduced kernel from 7x7 to 5x5
        mask_blur = cv2.GaussianBlur(blur_mask.astype(np.float32)/255, (5,5), 0)
        
        # Blend the edges
        for c in range(3):
            img[:,:,c] = img[:,:,c] * (1 - mask_blur[:,:,c]) + blurred[:,:,c] * mask_blur[:,:,c]
            
        # Optional: Final color correction
        roi = img[y:height, x:width]
        roi = cv2.fastNlMeansDenoisingColored(roi, None, 10, 10, 7, 21)
        img[y:height, x:width] = roi
        
        # Save the watermark-free image
        cv2.imwrite(path, img)
        
        return path
    except Exception as e:
        print(f'Error removing watermark: {e}')
        return path

# Test function
# new_path = handle_image('/Users/<USER>/Downloads/000af29869481ac262ecd1c3c085d101.png')
# print(f'Processed image saved at: {new_path}')