import cv2
import numpy as np

def handle_image(path: str):
    """Remove watermark"""
    try:
        # Read the original image
        img = cv2.imread(path, 1)
        if img is None:
            raise Exception("Failed to read the original image")

        height, width, depth = img.shape[0:3]

        # Adaptive thresholding for watermark detection
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Find contours of watermark regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Create a mask for inpainting
        mask = np.zeros_like(img)

        # Inpaint watermark regions
        for cnt in contours:
            x, y, w, h = cv2.boundingRect(cnt)
            roi = img[y:y+h, x:x+w]
            mask_roi = mask[y:y+h, x:x+w]
            cv2.drawContours(mask_roi, [cnt], 0, (255, 255, 255), -1)
            img[y:y+h, x:x+w] = cv2.inpaint(roi, mask_roi, 3, cv2.INPAINT_NS)

        # Save the watermark-free image to the original file path
        cv2.imwrite(path, img)

        return path
    except Exception as e:
        print(f'Error removing watermark: {e}')
        return path

# Test function
new_path = handle_image('/Users/<USER>/Downloads/000af29869481ac262ecd1c3c085d101.png')
print(f'Processed image saved at: {new_path}')