import pymysql

class MySQLConnector:
    def __init__(self, mysql_config):
        self.host = mysql_config['host']
        self.port = mysql_config['port']
        self.user = mysql_config['user']
        self.password = mysql_config['password']
        self.db = mysql_config['db']
        self.connection = None

    def connect(self):
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                db=self.db,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print("Connected to the database")
        except pymysql.Error as e:
            print(f"Error connecting to the database: {e}")
    def close_connection(self):
        if self.connection:
            self.connection.close()
            print("Database connection closed.")

    def execute_sql(self, sql, params=None):
        if not self.connection:
            self.connect()

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
            self.connection.commit()
            print("SQL executed successfully")
        except pymysql.Error as e:
            print(f"Error executing SQL: {e}")

    def insert_data(self, table, data):
        keys = ', '.join(data.keys())
        values = ', '.join(['%s'] * len(data))
        sql = f"INSERT INTO {table} ({keys}) VALUES ({values})"
        self.execute_sql(sql, list(data.values()))


    def insert_data_batch(self, table, data_list):
        if not data_list:
            return
        print(f'待批量插入数据:{len(data_list)}')
        cursor=self.connection.cursor()
        for data in data_list:
            keys = ', '.join(data.keys())
            values = ', '.join(['%s'] * len(data))
            sql = 'INSERT INTO {table}({keys}) VALUES ({values}) ON DUPLICATE KEY UPDATE'.format(table=table, keys=keys, values=values) #保证数据插入唯一
            update = ','.join([" {key} = %s".format(key=key) for key in data])
            sql += update
            try:
                cursor.execute(sql, tuple(data.values())*2)
                self.connection.commit()
            except:
                print('Failed')
                self.connection.rollback()
        print(f'已批量插入数据:{len(data_list)}')
        cursor.close()


        # keys = ', '.join(data_list[0].keys())
        # placeholders = ', '.join(['%s'] * len(data_list[0]))
        # values = ', '.join(f'({placeholders})' for _ in range(len(data_list)))
        # sql = f"INSERT INTO {table} ({keys}) VALUES {values}"
        # params = []
        # for data in data_list:
        #     params.extend(list(data.values()))
        # try:
        #     self.execute_sql(sql, params)
        # except Exception as e:
        #     print(f'异常:{e}')


            




    def select_data(self, table, columns, condition=None):
        sql = f"SELECT {', '.join(columns)} FROM {table}"
        if condition:
            sql += f" WHERE {condition}"
        with self.connection.cursor() as cursor:
            cursor.execute(sql)
            result = cursor.fetchall()
        return result
    def update_data(self, table, data, condition):
        """
        更新数据库中指定表的数据
        Args:
            table (str): 表名
            data (dict): 要更新的数据,格式为 {column1: value1, column2: value2, ...}
            condition (str): 更新条件,如 "id=1"
        Returns:
            int: 更新的记录数
        """
        columns = ', '.join([f"{col}='{val}'" for col, val in data.items()])
        sql = f"UPDATE {table} SET {columns} WHERE {condition}"
        
        with self.connection.cursor() as cursor:
            updated = cursor.execute(sql)
            self.connection.commit()
            
        return updated
    
    def get_biz_list_from_article(self):
        biz_list=[]
        sql = "SELECT biz FROM wjh_articles GROUP BY biz"
        with self.connection.cursor() as cursor:
            cursor.execute(sql)
            rows = cursor.fetchall()
            # biz_list = [row[0]['biz'] for row in cursor.fetchall()]
            for row in rows:
                biz_value = row.get('biz')
                if biz_value:
                    biz_list.append(biz_value)
        return biz_list
    
    def get_author_list(self):
        biz_list=[]
        sql = "SELECT biz,nickname,account_id,category FROM wjh_authors"
        with self.connection.cursor() as cursor:
            cursor.execute(sql)
            rows = cursor.fetchall()
            # biz_list = [row[0]['biz'] for row in cursor.fetchall()]
            for row in rows:
                biz_value = row.get('biz')
                nickname = row.get('nickname')
                account_id = row.get('account_id')
                category = row.get('category')
                content={
                    'biz':biz_value,
                    'nickname':nickname,
                    'account_id':account_id,
                    'category':category
                }
                biz_list.append(content)
        return biz_list
    
    def get_account_ids_by_biz(self, biz):
        sql = "SELECT account_id FROM wjh_articles WHERE biz=%s GROUP BY account_id"
        account_ids = []
        with self.connection.cursor() as cursor:
            cursor.execute(sql, (biz,))
            # account_ids = [row[0] for row in cursor.fetchall()]
            rows = cursor.fetchall()
            for row in rows:
                if row and len(row) > 0:
                    account_id = row.get('account_id')
                    if account_id:
                        account_ids.append(account_id)
        return account_ids
    
    def get_non_existing_article_ids(self,account_id_list:list):
        """检查哪些ID不在文章表中"""
        # 构建查询语句
          # 构建查询语句
        # 构建查询语句
        # 构建查询语句
        union_query = ' UNION ALL '.join([f"SELECT '{account_id}' AS id" for account_id in account_id_list])
        query = "SELECT id FROM ({}) AS input_accounts WHERE id NOT IN (SELECT article_id FROM wjh_articles);".format(union_query)
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query)
                result=cursor.fetchall()
            return result
                # non_existing_account_ids = [row[0] for row in cursor.fetchall()]
            # return non_existing_account_ids
        except Exception as e:
            print(f"Error executing query: {e}")
            return  None