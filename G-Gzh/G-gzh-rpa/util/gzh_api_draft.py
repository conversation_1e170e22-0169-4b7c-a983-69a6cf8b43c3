from flask import Flask, request, jsonify
import requests
from flask import Flask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
from flask import Flask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
from dotenv import load_dotenv
import re
from bs4 import BeautifulSoup
from dotenv import load_dotenv
import json
app = Flask(__name__)

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET')

# 创建微信客户端
# client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

class WECHATCLIENT():
    def __init__(self,WECHAT_APP_ID,WECHAT_APP_SECRET):
        self.client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)
        print(f'wechat client init')
    def get_access_token(self):
        try:
            return self.client.access_token
        except WeChatException as e:
            print(f"获取access_token失败: {e}")
            return None
    


    def create_draft(self,articles):
        """创建草稿内容（支持多篇文章）"""
        if not articles or not isinstance(articles, list):
            raise ValueError("articles must be a non-empty list")

        processed_articles = []

        for article in articles:
            title = article.get('title')
            author = article.get('author', '')
            content = article.get('content')
            content_source_url = article.get('content_source_url', '')
            thumb_media_id = article.get('thumb_media_id')
            digest = article.get('digest')

            # 验证必要参数
            if not all([title, content, thumb_media_id]):
                raise ValueError(f"title, content, and thumb_media_id are required for all articles")

            # 验证内容长度和大小
            content_bytes = content.encode('utf-8')
            if len(content_bytes) > 1000000:
                raise ValueError(f"Content for '{title}' must be less than 1M")

            # 移除 JavaScript
            soup = BeautifulSoup(content, 'html.parser')
            for script in soup(["script", "style"]):
                script.decompose()
            content = str(soup)

            # 验证图片 URL
            img_pattern = re.compile(r'<img[^>]+src=["\'](.*?)["\']', re.IGNORECASE)
            img_urls = img_pattern.findall(content)
            for url in img_urls:
                if not url.startswith('http://mmbiz.qpic.cn/') and not url.startswith('https://mmbiz.qpic.cn/'):
                    raise ValueError(f"Invalid image URL in '{title}': {url}. All image URLs must be from the WeChat server.")

            # 处理摘要
            if not digest:
                text_content = soup.get_text()
                digest = text_content[:120]
            else:
                digest = digest[:120]

            processed_articles.append({
                "title": title,
                "author": author,
                "digest": digest,
                "content": content,
                "content_source_url": content_source_url,
                "thumb_media_id": thumb_media_id,
                "need_open_comment": 1,
                "only_fans_can_comment": 0,
                "recommend": 1 #// 设置为1表示推荐，0表示不推荐
            })

        access_token = self.get_access_token()
        if not access_token:
            raise Exception("Failed to get access token")

        url = f"https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}"
        
        payload = {
            "articles": processed_articles
        }
        
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        try:
            response = requests.post(
                url=url,
                headers=headers,
                data=json.dumps(payload, ensure_ascii=False).encode('utf-8')
            )
            response.raise_for_status()
            result = response.json()
            if 'media_id' in result:
                return result['media_id']
                # return {"message": "Draft(s) created successfully", "media_id": result['media_id']}
            else:
                return None
                # raise Exception(f"Failed to create draft(s): {result.get('errmsg', 'Unknown error')}")
        except requests.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        return None


    



@app.route('/api/get_draft_list', methods=['GET'])
def get_draft_list():
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/batchget?access_token={access_token}"
    
    payload = {
        "offset": 0,
        "count": 20,  # 每次获取20条，可以根据需要调整
        "no_content": 1  # 不需要正文
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'total_count' in result:
            return jsonify({"message": "Draft list retrieved successfully", "drafts": result['item'], "total_count": result['total_count']}), 200
        else:
            return jsonify({"error": f"Failed to get draft list: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/get_draft_count', methods=['GET'])
def get_draft_count():
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/count?access_token={access_token}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        result = response.json()
        if 'total_count' in result:
            return jsonify({"message": "Draft count retrieved successfully", "total_count": result['total_count']}), 200
        else:
            return jsonify({"error": f"Failed to get draft count: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/delete_draft', methods=['POST'])
def delete_draft():
    data = request.json
    media_id = data.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/delete?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if result.get('errcode') == 0:
            return jsonify({"message": "Draft deleted successfully"}), 200
        else:
            return jsonify({"error": f"Failed to delete draft: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/get_draft', methods=['GET'])
def get_draft():
    media_id = request.args.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/get?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'news_item' in result:
            # 解码所有可能包含中文的字段
            for item in result['news_item']:
                item['title'] = item['title'].encode('iso-8859-1').decode('utf-8')
                item['author'] = item['author'].encode('iso-8859-1').decode('utf-8')
                item['digest'] = item['digest'].encode('iso-8859-1').decode('utf-8')
                item['content'] = item['content'].encode('iso-8859-1').decode('utf-8')
                item['content_source_url'] = item['content_source_url'].encode('iso-8859-1').decode('utf-8')
            return jsonify({"message": "Draft retrieved successfully", "draft": result['news_item']}), 200
        else:
            return jsonify({"error": f"Failed to get draft: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500
   

# Example usage:
# try:
#     result = create_draft(articles)
#     print(result)
# except Exception as e:
#     print(f"Error: {str(e)}")
    
    

@app.route('/api/update_draft', methods=['POST'])
def update_draft():
    data = request.json
    media_id = data.get('media_id')
    index = data.get('index', 0)
    articles = data.get('articles')
    
    if not media_id or not articles:
        return jsonify({"error": "media_id and articles are required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/update?access_token={access_token}"
    
    payload = {
        "media_id": media_id,
        "index": index,
        "articles": articles
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if result.get('errcode') == 0:
            return jsonify({"message": "Draft updated successfully"}), 200
        else:
            return jsonify({"error": f"Failed to update draft: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

def delete_all_drafts():
    access_token = get_access_token()
    if not access_token:
        print("Failed to get access token")
        return

    url = f"https://api.weixin.qq.com/cgi-bin/draft/batchget?access_token={access_token}"
    
    payload = {
        "offset": 0,
        "count": 20,
        "no_content": 1
    }
    
    try:
        while True:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'item' not in result or not result['item']:
                break
            
            for item in result['item']:
                delete_url = f"https://api.weixin.qq.com/cgi-bin/draft/delete?access_token={access_token}"
                delete_payload = {"media_id": item['media_id']}
                # print(delete_payload)
                delete_response = requests.post(delete_url, json=delete_payload)
                delete_response.raise_for_status()
                delete_result = delete_response.json()
                if delete_result.get('errcode') == 0:
                    print(f"Deleted draft with media_id: {item['media_id']}")
                else:
                    print(f"Failed to delete draft with media_id: {item['media_id']}")
            
            if len(result['item']) < 20:
                break
            
            payload['offset'] += 20
        
        print("All drafts have been processed")
    except requests.RequestException as e:
        print(f"Request failed: {str(e)}")



if __name__ == '__main__':
    delete_all_drafts()
    # app.run(debug=True)  # 如果您想运行Flask应用，取消这行的注释
    # app.run(host='0.0.0.0', port=5001)
