import requests,re,json
def parse_url_data(url: str):
    try:
        url = f'https://r.jina.ai/{url}'
        res = requests.get(url=url)
        input_str = res.text
        # 使用正则表达式提取Title和URL Source后的值
        title_match = re.search(r'Title:\s*(.*)', input_str)
        url_match = re.search(r'URL Source:\s*(.*)', input_str)
        # markdown_match = re.search(r'Markdown Content:\s*(.*)', input_str)

        # 定义正则表达式模式，匹配Markdown Content后面的内容
        # pattern = r'Markdown Content:(.*?)\((.*?)\)'
        pattern = r'Markdown Content:(.*?)\n\n(.*)'

        # 使用正则表达式匹配文本
        # markdown_match = re.search(pattern, input_str)
        markdown_match = re.search(pattern, input_str, re.DOTALL)
        # 检查是否找到了匹配项
        if title_match and url_match:
            title = title_match.group(1).strip()
            url_source = url_match.group(1).strip()
            markdown_content = markdown_match.group(2)

            # 创建字典
            data = {
                "title": title,
                "url_source": url_source,
                "markdown_content": markdown_content
            }

            # 转换为JSON字符串
            # json_data = json.dumps(data, ensure_ascii=False)
            json_data={
                'title': data['title'],
                'content':data['markdown_content']
            }
            return json_data
        else:
            print("Title或URL Source未找到")
    except Exception as e:
        print(f"解析URL异常:{e}")
        return None
# parse_url_data(url='https://new.qq.com/rain/a/20240616A04MLE00')