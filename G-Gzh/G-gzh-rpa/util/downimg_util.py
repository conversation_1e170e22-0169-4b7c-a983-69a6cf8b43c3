import requests,os,uuid
def save_image_from_url(url, save_path):
    """从URL保存图片到本地"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36'
    }
    response = requests.get(url, stream=True, headers=headers)
    save_path = os.path.join(save_path, f"_{str(uuid.uuid4())}.png")

    # 检查请求是否成功
    if response.status_code == 200:
        # 打开文件并以二进制模式写入
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    file.write(chunk)
        return True
    else:
        return False
    
def down_image_from_url(url, save_path):
    """从URL保存图片到本地"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36'
    }
    response = requests.get(url, stream=True, headers=headers)
    save_path = os.path.join(save_path, f"_{str(uuid.uuid4())}.png")

    # 检查请求是否成功
    if response.status_code == 200:
        # 打开文件并以二进制模式写入
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    file.write(chunk)
        return save_path
    else:
        return None