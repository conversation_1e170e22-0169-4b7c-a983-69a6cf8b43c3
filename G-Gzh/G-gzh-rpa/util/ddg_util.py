import json
import os
import re
import sys

from docx.shared import Pt, RGBColor

sys.path.append(os.path.dirname(__file__))

import requests


class ddgs_client:
    def __init__(self) -> None:
        print("ddgs_client init")
        pass

    # 查询内容
    def search_news(self, query: str):
        duckduckgo_api_url = "https://ddg.search2ai.online/searchNews"
        body = {
            "q": query,
            "max_results": os.getenv("MAX_RESULTS", "10")
        }
        headers = {
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(duckduckgo_api_url, headers=headers, data=json.dumps(body))
            response.raise_for_status()  # 如果响应状态码不是200，则抛出异常

            data = response.json()
            results = [
                {
                    "title": item["title"],
                    "link": item["url"],
                    "snippet": item["body"]
                }
                for item in data["results"]
            ]
            return results
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
        except ValueError as e:
            print(f"JSON解析错误: {e}")
            return None

    # 查询内容
    def search(self, query: str):
        duckduckgo_api_url = "https://ddg.search2ai.online/search"
        body = {
            "q": query,
            "max_results": os.getenv("MAX_RESULTS", "10")
        }
        headers = {
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(duckduckgo_api_url, headers=headers, data=json.dumps(body))
            response.raise_for_status()  # 如果响应状态码不是200，则抛出异常

            data = response.json()
            results = [
                {
                    "title": item["title"],
                    "link": item["href"],
                    "snippet": item["body"]
                }
                for item in data["results"]
            ]
            return results
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
        except ValueError as e:
            print(f"JSON解析错误: {e}")
            return None

    # 爬取内容
    def search_crawler(self, url):
        print(f"正在使用 URL 进行自定义爬取: {json.dumps(url)}")
        try:
            response = requests.post('https://crawler.search2ai.one',
                                     headers={"Content-Type": "application/json"},
                                     data=json.dumps({"url": url}))

            if response.status_code != 200:
                print(f"API 请求失败, 状态码: {response.status_code}")
                return f"API 请求失败, 状态码: {response.status_code}"

            content_type = response.headers.get("content-type")
            if not content_type or 'application/json' not in content_type:
                print("收到的响应不是有效的 JSON 格式")
                return "收到的响应不是有效的 JSON 格式"

            data = response.json()
            print('自定义爬取服务调用完成')
            return data
            # return json.dumps(data)
        except Exception as e:
            print(f"在 crawler 函数中捕获到错误: {e}")
            return f"在 crawler 函数中捕获到错误: {e}"

    def delete_empty_docs_in_folder(self, folder_path):
        # 遍历文件夹中的所有文件
        for filename in os.listdir(folder_path):
            # if filename.endswith('.doc'):
            file_path = os.path.join(folder_path, filename)
            if os.path.getsize(file_path) == 0:
                # 删除空文件
                os.remove(file_path)
                print(f"Deleted empty .doc file: {file_path}")

                # 生成doc文档
    def add_paragraph_with_heading(self, doc, string):
        # 使用正则表达式查找井号的数量
        match = re.match(r'^(#+)\s*(.*)$', string.strip())
        if match:
            # 提取井号的数量和文本
            hash_count = len(match.group(1))
            text = match.group(2).strip()
            # 添加标题
            if hash_count > 0:
                # self.add_heading(doc, text, hash_count)
                heading = doc.add_heading(text, hash_count)
                for run in heading.runs:
                    run.font.name = u'SimSun'  # 设置字体为正体宋体
                    run.font.size = Pt(18)  # 设置字体大小为10.5磅
                    run.font.color.rgb = RGBColor(53, 179, 120)  # 设置颜色为RGB(53, 179, 120)
                    run.bold = True  # 设置为粗体
            else:
                # 如果没有井号，则添加普通段落
                # self.add_paragraph(doc, text)
                doc.add_paragraph(text)
        else:
            # 如果没有匹配到井号，则添加普通段落
            if string.startswith('**') and string.endswith('**'):
                paragraph = doc.add_paragraph()
                bold_text = string[2:-2]
                run = paragraph.add_run(bold_text)
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                run.font.color.rgb = RGBColor(53, 179, 120)
                paragraph.add_run(' ')  # 添加空格
            else:
                paragraph = doc.add_paragraph()
                run = paragraph.add_run(string)
                # 设置字体为宋体
                run.font.name = u"SimSun"  # 正体宋体
                # 设置字号为11.5磅
                run.font.size = Pt(11.5)
                # 设置字体颜色为灰色-65%
                # RGB颜色值范围是0-255，所以这里我们需要计算出灰色-65%对应的RGB值
                gray_color = RGBColor(89, 89, 89)  # 灰色-65%的RGB值
                run.font.color.rgb = gray_color
                # doc.add_paragraph(string)
