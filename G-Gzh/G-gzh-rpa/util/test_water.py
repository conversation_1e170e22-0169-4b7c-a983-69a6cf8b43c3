import os
import cv2
import numpy as np
from PIL import Image

def handle_image(path: str):
    try:
        # 读取原始图像
        img = cv2.imread(path, 1)
        if img is None:
            raise Exception("Failed to read the original image")

        height, width, depth = img.shape[0:3]

        # 截取底部左下角的水印区域
        cropped_left = img[int(height * 0.8):height, 0:int(width * 0.3)]
        # 截取底部中间的水印区域
        cropped_middle = img[int(height * 0.8):height, int(width * 0.35):int(width * 0.65)]
        # 截取底部右下角的水印区域
        cropped_right = img[int(height * 0.8):height, int(width * 0.7):width]

        # 处理底部左下角的水印
        thresh_left = cv2.inRange(cropped_left, np.array([200, 200, 200]), np.array([250, 250, 250]))
        kernel = np.ones((3, 3), np.uint8)
        hi_mask_left = cv2.dilate(thresh_left, kernel, iterations=10)
        specular_left = cv2.inpaint(cropped_left, hi_mask_left, 5, flags=cv2.INPAINT_TELEA)

        # 处理底部中间的水印
        thresh_middle = cv2.inRange(cropped_middle, np.array([200, 200, 200]), np.array([250, 250, 250]))
        hi_mask_middle = cv2.dilate(thresh_middle, kernel, iterations=10)
        specular_middle = cv2.inpaint(cropped_middle, hi_mask_middle, 5, flags=cv2.INPAINT_TELEA)

        # 处理底部右下角的水印
        thresh_right = cv2.inRange(cropped_right, np.array([200, 200, 200]), np.array([250, 250, 250]))
        hi_mask_right = cv2.dilate(thresh_right, kernel, iterations=10)
        specular_right = cv2.inpaint(cropped_right, hi_mask_right, 5, flags=cv2.INPAINT_TELEA)

        # 覆盖图片
        img[int(height * 0.8):height, 0:int(width * 0.3)] = specular_left
        img[int(height * 0.8):height, int(width * 0.35):int(width * 0.65)] = specular_middle
        img[int(height * 0.8):height, int(width * 0.7):width] = specular_right

        # 保存处理后的图像到原路径
        cv2.imwrite(path, img)

        return path
    except Exception as e:
        print(f'水印异常返回原图, {e}')
        return path

# 测试函数
new_path = handle_image('/Users/<USER>/Downloads/1936179.jpg')
print(f'Processed image saved at: {new_path}')