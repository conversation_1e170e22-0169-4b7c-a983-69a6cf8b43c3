import cv2
import numpy as np
import pytesseract
from PIL import Image
import re
import os

def remove_watermarks(input_path):
    # 读取图像
    image = cv2.imread(input_path)
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 使用Tesseract进行OCR
    text = pytesseract.image_to_string(Image.fromarray(gray))
    print("OCR结果:", text)
    
    # 查找可能的水印文本
    watermark_pattern = r'freexcomic\.com'
    matches = list(re.finditer(watermark_pattern, text, re.IGNORECASE))
    
    if not matches:
        print("没有找到水印。")
        return input_path
    
    print(f"找到 {len(matches)} 个潜在的水印")
    
    # 获取每个字符的边界框
    boxes = pytesseract.image_to_boxes(Image.fromarray(gray))
    
    height, width = gray.shape
    
    for match in matches:
        start, end = match.span()
        watermark = match.group()
        print(f"处理水印: {watermark}")
        
        # 查找水印文本的坐标
        x_min, y_min, x_max, y_max = width, height, 0, 0
        for box in boxes.splitlines():
            b = box.split()
            if len(b) == 6:
                char, x1, y1, x2, y2, _ = b
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                if start <= text.index(char) < end:
                    x_min = min(x_min, x1)
                    y_min = min(y_min, height - y2)
                    x_max = max(x_max, x2)
                    y_max = max(y_max, height - y1)
        
        if x_min < x_max and y_min < y_max:
            # 扩大ROI以确保覆盖整个水印
            padding = 10
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(width, x_max + padding)
            y_max = min(height, y_max + padding)
            
            print(f"水印坐标: x={x_min}, y={y_min}, w={x_max-x_min}, h={y_max-y_min}")
            
            # 提取ROI
            roi = image[y_min:y_max, x_min:x_max]
            
            # 创建掩码
            mask = np.zeros(roi.shape[:2], np.uint8)
            cv2.rectangle(mask, (0,0), (x_max-x_min, y_max-y_min), (255,255,255), -1)
            
            # 使用多种方法移除水印
            
            # 方法1：Inpainting
            result_inpaint = cv2.inpaint(roi, mask, 3, cv2.INPAINT_TELEA)
            
            # 方法2：模糊背景
            blurred = cv2.GaussianBlur(roi, (25, 25), 0)
            result_blur = np.where(mask[:,:,np.newaxis] == 255, blurred, roi)
            
            # 方法3：颜色填充
            bg_color = np.median(roi.reshape(-1, 3), axis=0).astype(np.uint8)
            result_fill = np.where(mask[:,:,np.newaxis] == 255, bg_color, roi)
            
            # 选择最佳结果（这里我们使用inpainting的结果，您可以根据实际效果调整）
            result = result_inpaint
            
            # 将处理后的区域放回原图
            image[y_min:y_max, x_min:x_max] = result
            
            print(f"水印 '{watermark}' 已被移除。")
    
    # 保存处理后的图像
    output_path = os.path.splitext(input_path)[0] + "_no_watermark" + os.path.splitext(input_path)[1]
    cv2.imwrite(output_path, image)
    
    print(f"水印已移除。新图像保存为: {output_path}")
    return output_path

# 使用示例
input_image_path = "/Users/<USER>/Downloads/1936179.jpg"
output_image_path = remove_watermarks(input_image_path)