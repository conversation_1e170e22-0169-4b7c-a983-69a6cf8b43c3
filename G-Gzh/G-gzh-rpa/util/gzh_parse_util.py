import re
import requests
from bs4 import BeautifulSoup
import logging
logger = logging.getLogger(__name__)

def get_weixin_content(url):
    """
    Retrieves the title, timestamp, text content, and images from a WeChat article.

    Args:
        url (str): The URL of the WeChat article.

    Returns:
        tuple: A tuple containing the title (str), timestamp (str), text content (str), and a list of image URLs.
    """
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # Raises an HTTPError for bad responses
    except requests.RequestException as e:
        logger.error(f"Error fetching URL {url}: {str(e)}")
        return None, None, None
    soup = BeautifulSoup(response.text, "html.parser")

    # Get the title
    title_elem = soup.find('h1')
    if title_elem is None:
        print(f'Error: Unable to open {url}')
        return None, None, None
    weixin_title = title_elem.string.strip()

    # Get the timestamp
    time_pattern = r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}'
    weixin_time = re.findall(time_pattern, response.text)
    weixin_time = weixin_time[0] if weixin_time else None

    # Get the content HTML and extract text and images
    content_elem = soup.find(id='js_content')
    if content_elem:
        content_soup = BeautifulSoup(str(content_elem), "html.parser")
        content_soup.div['style'] = 'visibility: visible;'
        html = str(content_soup)
        text_content = content_elem.get_text()
        image_pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
        image_urls = re.findall(image_pattern, html)
    else:
        text_content = ''
        image_urls = []
    return weixin_title,text_content, image_urls

