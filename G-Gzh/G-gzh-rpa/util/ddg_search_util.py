from duckduckgo_search import DDGS, AsyncDDGS
import asyncio
import time
from duckduckgo_search.exceptions import RatelimitException

# 代理配置
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 5  # 重试等待时间（秒）
REQUEST_DELAY = 2  # 请求间隔时间（秒）

def retry_on_ratelimit(func):
    """处理速率限制的装饰器"""
    def wrapper(*args, **kwargs):
        for attempt in range(MAX_RETRIES):
            try:
                result = func(*args, **kwargs)
                time.sleep(REQUEST_DELAY)  # 每次请求后添加延迟
                return result
            except RatelimitException as e:
                if attempt == MAX_RETRIES - 1:
                    raise e
                print(f"触发速率限制，等待 {RETRY_DELAY} 秒后重试...")
                time.sleep(RETRY_DELAY)
        return None
    return wrapper

class DDGSTextSearch:
    @staticmethod
    @retry_on_ratelimit
    def run(query, max_results=10):
        """执行文本搜索"""
        with DDGS() as ddgs:
            results = ddgs.text(query, max_results=max_results,timelimit='w',backend='html')
            return [result for result in results]  # 转换为列表并返回

class DDGSAnswers:
    @staticmethod
    @retry_on_ratelimit
    def run(query):
        """获取即时答案"""
        with DDGS(proxies=PROXIES) as ddgs:
            return ddgs.answers(query)

class DDGSNews:
    @staticmethod
    @retry_on_ratelimit
    def run(keywords, region="wt-wt", safesearch="off", timelimit="w", max_results=5):
        """搜索新闻"""
        with DDGS(proxies=PROXIES) as ddgs:
            results = ddgs.news(
                keywords=keywords, 
                region=region, 
                safesearch=safesearch, 
                timelimit=timelimit,
                max_results=max_results
            )
            return [result for result in results]

class DDGSImages:
    @staticmethod
    @retry_on_ratelimit
    def run(keywords, region="wt-wt", safesearch="off", size=None, color="Monochrome", 
            type_image=None, layout=None, license_image=None, max_results=10):
        """搜索图片"""
        with DDGS(proxies=PROXIES) as ddgs:
            results = ddgs.images(
                keywords=keywords,
                region=region,
                safesearch=safesearch,
                size=size,
                color=color,
                type_image=type_image,
                layout=layout,
                license_image=license_image,
                max_results=max_results,
            )
            return [result for result in results]

# 异步版本的重试装饰器
def async_retry_on_ratelimit(func):
    """处理异步操作的速率限制装饰器"""
    async def wrapper(*args, **kwargs):
        for attempt in range(MAX_RETRIES):
            try:
                result = await func(*args, **kwargs)
                await asyncio.sleep(REQUEST_DELAY)  # 异步延迟
                return result
            except RatelimitException as e:
                if attempt == MAX_RETRIES - 1:
                    raise e
                print(f"触发速率限制，等待 {RETRY_DELAY} 秒后重试...")
                await asyncio.sleep(RETRY_DELAY)
        return None
    return wrapper

class AsyncDDGSNews:
    @staticmethod
    @async_retry_on_ratelimit
    async def run(keywords, region="wt-wt", safesearch="off", timelimit="d", max_results=10):
        """异步搜索新闻"""
        async with AsyncDDGS(proxies=PROXIES) as ddgs:
            results = await ddgs.anews(
                keywords, 
                region=region, 
                safesearch=safesearch, 
                timelimit=timelimit,
                max_results=max_results
            )
            return results

if __name__ == "__main__":
    query = "农民副县长被抓 老百姓放鞭炮庆祝"

    try:
        # 测试文本搜索
        results = DDGSTextSearch.run(query)
        for result in results:
            print(result)
            
        # 测试新闻搜索
        # news_results = DDGSNews.run(query)
        # for result in news_results:
        #     print(result)
            
        # 测试异步新闻搜索
        # async def test_async():
        #     results = await AsyncDDGSNews.run(query)
        #     print(results)
        # asyncio.run(test_async())
            
    except RatelimitException as e:
        print(f"达到速率限制：{e}")
    except Exception as e:
        print(f"发生错误：{e}")
