import requests, re
from bs4 import BeautifulSoup


def get_kejixun_ai():
    url = "https://www.kejixun.com/ai"
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    contents = []
    items = soup.select('li.item')
    for item in items:
        img_url = item.select_one('.item-img-inner').find('img').get('src')
        title = item.find('a', class_='item-img-inner').get('title')
        href = item.find('a', class_='item-img-inner').get('href')

        # img_element = item.select_one('.item-img-inner > img.j-lazy')
        # img_url = img_element['data-original'] if img_element else ''
        # title = img_element.find_parent('a').get('title')
        # href = img_element.find_parent('a').get('href')

        date_element = item.select_one('.item-meta-li.date')
        date_text = date_element.text if date_element else ''

        author_link = item.select_one('.item-meta-li.author > a')
        author_name = author_link.text if author_link else ''
        author_href = author_link['href'] if author_link else ''

        pattern = r'\d+'
        match = re.search(pattern, href)
        article_id = match.group()
        content = {
            'id': article_id,
            'title': title,
            'link': href,
            'published': date_text,
            'author': author_name,
            'author_link': author_href,
            'image_url': img_url,
            'tag': 'AI'
        }
        contents.append(content)
    return contents

get_kejixun_ai()
