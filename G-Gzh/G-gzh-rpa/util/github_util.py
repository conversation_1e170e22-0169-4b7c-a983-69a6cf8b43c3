import base64
import re

import requests

from . import system_util

token = '*********************************************************************************************'


def upload_image_to_github(image_path: str, sha_value: None):
    if system_util.get_os() == 'Windows':
        folder_name = image_path.split('\\\\')[-2]
        image_name = image_path.split('\\\\')[-1]
    else:
        folder_name = image_path.split('/')[-2]
        image_name = image_path.split('/')[-1]
    cleaned_sentence = re.sub(r'[^\w\s]', '', folder_name)
    # image_name=f'{str(uuid.uuid4())}_{image_name}'
    image_name = f'{cleaned_sentence}_{image_name}'
    url = f'https://api.github.com/repos/wenhaofree/Image/contents/blog/{image_name}'
    # url = "https://cdn.wenhaofree.com/gh/wenhaofree/Image/contents/image001.png"
    headers = {
        "Authorization": "token " + token
    }

    with open(image_path, 'rb') as file:
        image_data = file.read()
        image_data = base64.b64encode(image_data).decode('utf-8')

    data = {
        "message": "Upload image",
        "content": image_data,
        "sha": sha_value if sha_value is not None else None
    }

    response = requests.put(url, headers=headers, json=data)

    if response.status_code == 201:
        sha = response.json()["content"]['sha']
        image_url = response.json()["content"]["download_url"]
        print("Image uploaded successfully. URL:", image_url)
        image_url = f'https://cdn.wenhaofree.com/gh/wenhaofree/Image/blog/{image_name}'
        print('替换域名:', image_url)
        return image_url
    elif response.status_code == 422:
        url = f'https://api.github.com/repos/wenhaofree/Image/contents/blog/{image_name}'
        response = requests.get(url)
        if response.status_code == 200:
            sha = response.json()["sha"]
            image_url = response.json()["download_url"]
            print("Image already exists. URL:", image_url)
            return image_url
        else:
            print("Failed to upload image. Status code:", response.status_code)
            return None

    else:
        print("Failed to upload image. Status code:", response.status_code)
        print("Response:", response.text)
        return None

# 使用您的GitHub个人访问令牌和图片路径调用函数
# upload_image_to_github("*********************************************************************************************",
#                        "/Users/<USER>/Downloads/0.png")
# upload_image_to_github('/Users/<USER>/Downloads/0.png', None)
