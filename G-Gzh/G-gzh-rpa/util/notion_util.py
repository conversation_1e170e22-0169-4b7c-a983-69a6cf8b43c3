import os
import sys

from httpx import Timeout, Limits, Proxy
from notion_client import Client, APIResponseError
from tenacity import retry, stop_after_attempt, wait_exponential

sys.path.append(os.path.dirname(__file__))


class notion_client:
    def __init__(self, token, database_id):
        proxy = os.environ.get('HTTPS_PROXY')
        self.global_database_id = database_id
        client_options = {
            "auth": token,
            "base_url": "https://api.notion.com",
        }
        if proxy:
            client_options["proxies"] = {"https": proxy}
        
        self.global_notion = Client(**client_options)
        print(f'Notion init...{database_id}')

    # 获取数据
    def query_results_by_condication(self, params: None, start_cursor=None):
        # 定义基础过滤条件
        # 初始化过滤条件列表
        filter_conditions = []

        # 添加发布时间过滤条件（假设'yesterday'总是存在且有效）
        filter_conditions.append({
            "property": "发布时间",
            "date": {
                "after": params['yesterday']
            }
        })

        # 添加领域过滤条件（检查'area'是否存在）
        if 'area' in params and params['area']:
            filter_conditions.append({
                "property": "领域",
                "select": {
                    "equals": params['area']
                }
            })

        # 添加阅读量过滤条件（检查'readcount'是否存在）
        if 'readcount' in params and params['readcount']:
            filter_conditions.append({
                "property": "阅读量",
                "number": {
                    "greater_than": params['readcount']
                }
            })

        # 添加Tags过滤条件（假设始终包含'初始化'）
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": '初始化'
            }
        })

        # 添加作者过滤条件（检查'author'是否存在）
        if 'author' in params and params['author']:
            filter_conditions.append({
                "property": "作者",
                "rich_text": {
                    "contains": params['author']
                }
            })

        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}

        if start_cursor:
            response = self.global_notion.databases.query(
                **{
                    "database_id": self.global_database_id,
                    "start_cursor": start_cursor,
                    "filter": {
                        "and": [
                            {
                                "property": '发布时间',
                                "date": {
                                    "after": params['day']
                                }
                            },
                            {
                                "领域": {
                                    "select": {
                                        "name": params['article_flag']
                                    }
                                }
                            },
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "contains": '初始化'
                                }
                            }
                        ]
                    }
                }
            )
        else:
            response = self.global_notion.databases.query(
                **{
                    "database_id": self.global_database_id,
                    "filter": filter_obj
                }
            )
        # 获取结果和下一页的cursor
        results = response['results']
        next_cursor = response.get('next_cursor')
        return results, next_cursor

    def get_content_by_condition(self, params: None, start_cursor=None):
        results, next_cursor = self.query_results_by_condication(params, start_cursor=start_cursor)
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败']:
                        tag_flag = True
                        break
                if tag_flag:
                    continue
                page_id = page["id"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                page_url = page["properties"]['hmcturl']['url']
                page_readcount = page["properties"]['阅读量']['number']
                content = {
                    "page_id": page_id,
                    "page_title": page_title,
                    "page_url": page_url,
                    "page_readcount": page_readcount
                }
                return content
        # 如果有下一页数据，则继续查询
        if next_cursor:
            content = self.get_content_by_condition(params, next_cursor)
            if content:
                return content

    def get_content_by_condition_keji(self, day: str):
        """科技领域查询"""
        filter_conditions = []
        # 添加Tags过滤条件（假设始终包含'初始化'）
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": '初始化'
            }
        })
        filter_conditions.append({
            "property": "tag",
            "select": {
                "equals": 'AI'
            }
        })
        filter_conditions.append({
            "property": "发布时间",
            "date": {
                "after": day
            }
        })
        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        # 查询结果
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', 'Pass']:
                        tag_flag = True
                        break
                if tag_flag: continue

                page_id = page["id"]
                page_chinese_title = page["properties"]["中文标题"]["rich_text"][0]["text"]["content"]
                page_url = page["properties"]["hmcturl"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                image_value = page["properties"]["图片文件"]["files"]
                if image_value is not None and len(image_value) != 0:
                    if 'external' == image_value[0]['type']:
                        image_url = image_value[0]['external']['url']
                        image_name = image_value[0]['name'].split('/')[-1]
                    else:
                        image_name = image_value[0]['name']
                        image_url = image_value[0]['file']['url']
                else:
                    image_name = '',
                    image_url = ''
                contentDict = {
                    "page_id": page_id,
                    'page_chinese_title': page_chinese_title,
                    "page_url": page_url,
                    "image_url": image_url,
                    "page_title": page_title,
                }
                return contentDict
        return None

    def get_content_by_condition_yuer(self, day: str):
        # 初始化过滤条件列表
        filter_conditions = []
        # 添加Tags过滤条件（假设始终包含'初始化'）
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": '初始化'
            }
        })
        # 建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        # 查询结果
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', 'Pass']:
                        tag_flag = True
                        break
                if tag_flag: continue

                page_id = page["id"]
                page_chinese_title = page["properties"]["中文标题"]["rich_text"][0]["text"]["content"]
                page_url = page["properties"]["hmcturl"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                image_value = page["properties"]["图片文件"]["files"]
                if image_value is not None and len(image_value) != 0:
                    if 'external' == image_value[0]['type']:
                        image_url = image_value[0]['external']['url']
                    else:
                        image_url = image_value[0]['file']['url']
                else:
                    image_url = ''
                contentDict = {
                    "page_id": page_id,
                    'page_chinese_title': page_chinese_title,
                    "page_url": page_url,
                    "image_url": image_url,
                    "page_title": page_title,
                }
                return contentDict
        return None

    def get_content_by_condition_nba(self, params):
        """体育领域查询"""
        filter_conditions = []
        # 添加Tags过滤条件（假设始终包含'初始化'）
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": params['Tags']
            }
        })
        filter_conditions.append({
            "property": "发布时间",
            "date": {
                "after": params['yesterday']
            }
        })
        if 'area' in params and params['area']:
            filter_conditions.append({
                "property": "领域",
                "select": {
                    "equals": params['area']
                }
            })
        if 'datasource' in params and params['datasource']:
            filter_conditions.append({
                "property": "来源",
                "select": {
                    "equals": params['datasource']
                }
            })

        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        # 查询结果
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if params.get('flag','')=='wtt':
                        if item['name'] in ['发布成功', '格式化', 'Pass','微头条发布成功']:
                            tag_flag = True
                            break
                    else:
                        if item['name'] in ['发布成功', '格式化','Pass']:
                            tag_flag = True
                            break

                if tag_flag: continue

                page_id = page["id"]
                page_chinese_title = page["properties"]["中文标题"]["rich_text"][0]["text"]["content"]
                page_url = page["properties"]["hmcturl"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                image_value = page["properties"]["图片文件"]["files"]
                if image_value is not None and len(image_value) != 0:
                    if 'external' == image_value[0]['type']:
                        image_url = image_value[0]['external']['url']
                        image_name = image_value[0]['name'].split('/')[-1]
                    else:
                        image_name = image_value[0]['name']
                        image_url = image_value[0]['file']['url']
                else:
                    image_name = '',
                    image_url = ''
                contentDict = {
                    "page_id": page_id,
                    'page_chinese_title': page_chinese_title,
                    "page_url": page_url,
                    "image_url": image_url,
                    "page_title": page_title,
                    "page_tags":page_tags,
                }
                return contentDict
        return None

    def get_content_by_condition_douban(self, params):
        """体育领域豆瓣数据查询"""
        filter_conditions = []
        # 添加Tags过滤条件（假设始终包含'初始化'）
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": params['Tags']
            }
        })
        filter_conditions.append({
            "property": "发布时间",
            "date": {
                "after": params['yesterday']
            }
        })
        if 'area' in params and params['area']:
            filter_conditions.append({
                "property": "领域",
                "select": {
                    "equals": params['area']
                }
            })
        if 'datasource' in params and params['datasource']:
            filter_conditions.append({
                "property": "来源",
                "select": {
                    "equals": params['datasource']
                }
            })

        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        # 查询结果
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', 'Pass','微头条发布成功']:
                            tag_flag = True
                            break
                if tag_flag: continue

                page_id = page["id"]
                page_chinese_title = page["properties"]["中文标题"]["rich_text"][0]["text"]["content"]
                page_url = page["properties"]["hmcturl"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                # image_value = page["properties"]["图片文���"]["files"]
                # if image_value is not None and len(image_value) != 0:
                #     if 'external' == image_value[0]['type']:
                #         image_url = image_value[0]['external']['url']
                #         image_name = image_value[0]['name'].split('/')[-1]
                #     else:
                #         image_name = image_value[0]['name']
                #         image_url = image_value[0]['file']['url']
                # else:
                #     image_name = '',
                #     image_url = ''
                contentDict = {
                    "page_id": page_id,
                    'page_chinese_title': page_chinese_title,
                    "page_url": page_url,
                    # "image_url": image_url,
                    "page_title": page_title,
                    "page_tags":page_tags,
                }
                return contentDict
        return None


    def get_content_by_condition_coommon(self, params):
        """易撰次幂数据库-通用查询"""
        # 初始化过滤条件列表
        filter_conditions = []
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": '初始化'
            }
        })
        if 'yesterday' in params and params['yesterday']:
            filter_conditions.append({
                "property": "发布时间",
                "date": {
                    "after": params['yesterday']
                }
            })
        if 'area' in params and params['area']:
            filter_conditions.append({
                "property": "领域",
                "select": {
                    "equals": params['area']
                }
            })
        if 'readcount' in params and params['readcount']:
            filter_conditions.append({
                "property": "阅读量",
                "number": {
                    "greater_than": params['readcount']
                }
            })
        if 'author' in params and params['author']:
            filter_conditions.append({
                "property": "作者",
                "rich_text": {
                    "contains": params['author']
                }
            })

        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        # 添加排序条件
        sort_conditions = [
            {
                "property": "发布时间",  # 替换为你想要排序的字段名
                "direction": "descending"
            }
        ]

        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj,
                "sorts": sort_conditions
            }
        )
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', '生成失败', '内容失效']:
                        tag_flag = True
                        break
                if tag_flag:continue
                page_id = page["id"]
                page_url = page["properties"]["hmcturl"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                contentDict = {
                    "page_id": page_id,
                    "page_url": page_url,
                    "page_title": page_title,
                }
                return contentDict
        return None

    def get_content_by_condition_coommon_keji(self, params):
        """易撰次幂数据库-通用查询"""
        # 初始化过滤条件列表
        filter_conditions = []
        if 'Tags' in params and params['Tags']:
            filter_conditions.append({
                "property": "标签",
                "multi_select": {
                    "contains": params['Tags']
                }
            })
        if 'yesterday' in params and params['yesterday']:
            filter_conditions.append({
                "property": "发布时间",
                "date": {
                    "after": params['yesterday']
                }
            })
        if 'datasource' in params and params['datasource']:
            filter_conditions.append({
                "property": "来源",
                "select": {
                    "equals": params['datasource']
                }
            })
        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            if page["object"] == "page":
                page_tags = page["properties"]["标签"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', '微头条发布成功']:
                        tag_flag = True
                        break
                if tag_flag:continue
                page_id = page["id"]
                page_url = page["properties"]["主页地址"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                image_value = page["properties"]["图片文件"]["files"]
                if image_value is not None and len(image_value) != 0:
                    if 'external' == image_value[0]['type']:
                        image_url = image_value[0]['external']['url']
                    else:
                        image_url = image_value[0]['file']['url']
                    contentDict = {
                        "page_id": page_id,
                        "page_url": page_url,
                        "page_title": page_title,
                        "image_url": image_url
                    }
                else:
                    contentDict = {
                        "page_id": page_id,
                        "page_url": page_url,
                        "page_title": page_title,
                    }
                return contentDict
        return None
    
    def get_content_by_condition_redian(self, params):
        """头条热搜数据库-通用查询-热点数据"""
        # 初始化过滤条件列表
        filter_conditions = []
        # 图片文件属性不能为空
        filter_conditions.append({
            "property": "图片文件",
            "files": {
                'is_not_empty': True
            }
        })
        if 'Tags' in params and params['Tags']:
            filter_conditions.append({
                "property": "标签",
                "multi_select": {
                    "contains": params['Tags']
                }
            })
        if 'yesterday' in params and params['yesterday']:
            filter_conditions.append({
                "property": "发布时间",
                "date": {
                    "after": params['yesterday']
                }
            })
        if 'datasource' in params and params['datasource']:
            filter_conditions.append({
                "property": "来源",
                "select": {
                    "equals": params['datasource']
                }
            })
        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            if page["object"] == "page":
                page_tags = page["properties"]["标签"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', '微头条发布成功']:
                        tag_flag = True
                        break
                if tag_flag:continue
                page_id = page["id"]
                page_url = page["properties"]["主页地址"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                image_value = page["properties"]["图片文件"]["files"]
                if image_value is not None and len(image_value) != 0:
                    if 'external' == image_value[0]['type']:
                        image_url = image_value[0]['external']['url']
                    else:
                        image_url = image_value[0]['file']['url']
                    contentDict = {
                        "page_id": page_id,
                        "page_url": page_url,
                        "page_title": page_title,
                        "image_url": image_url
                    }
                else:
                    contentDict = {
                        "page_id": page_id,
                        "page_url": page_url,
                        "page_title": page_title,
                    }
                return contentDict
        return None


    def get_content_by_condition_qinggan(self, params):
        # 初始化过滤条件列表
        filter_conditions = []
        # 添加Tags过滤条件（假设始终包含'初始化'）
        filter_conditions.append({
            "property": "Tags",
            "multi_select": {
                "contains": '初始化'
            }
        })
        filter_conditions.append({
            "property": "发布时间",
            "date": {
                "after": params['yesterday']
            }
        })
        if 'area' in params and params['area']:
            filter_conditions.append({
                "property": "领域",
                "select": {
                    "equals": params['area']
                }
            })
        if 'readcount' in params and params['readcount']:
            filter_conditions.append({
                "property": "阅读量",
                "number": {
                    "greater_than": params['readcount']
                }
            })
        # 添加作者过滤条件（检查'author'是否存在）
        if 'author' in params and params['author']:
            filter_conditions.append({
                "property": "作者",
                "rich_text": {
                    "contains": params['author']
                }
            })

        # 构建最终的过滤对象
        filter_obj = {"and": filter_conditions} if filter_conditions else {}
        response = self.global_notion.databases.query(
            **{
                "database_id": self.global_database_id,
                "filter": filter_obj
            }
        )
        results = response['results']
        if len(results) == 0: return None
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['发布成功', '格式化', '发布失败', '生成失败']:
                        tag_flag = True
                        break
                if tag_flag:
                    continue
                page_id = page["id"]
                page_url = page["properties"]["hmcturl"]["url"]
                page_title = page["properties"]['标题']['title'][0]['plain_text']
                contentDict = {
                    "page_id": page_id,
                    "page_url": page_url,
                    "page_title": page_title,
                }
                return contentDict
        return None
    def update_page_content_keji(self, page_id, properties_params):
        page = self.global_notion.pages.retrieve(page_id)

        data = {"multi_select": []}
        data["multi_select"].append({"name": properties_params})
        page_tags = page["properties"]["标签"]["multi_select"]
        for item in page_tags:
            name = item['name']
            data["multi_select"].append({"name": name})
        # 更新页面的属性
        update_payload = {
            "properties": {
                "标签": {
                    "multi_select": data["multi_select"]
                }
                # 其他属性更新
            }
        }
        # 执行更新操作
        update_page = self.global_notion.pages.update(page_id=page_id, **update_payload)
        print("更新状态", properties_params)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def update_page_content(self, page_id, properties_params):
        """增加新属性并保留旧属性"""
        try:
            page = self.global_notion.pages.retrieve(page_id)

            data = {"multi_select": []}
            data["multi_select"].append({"name": properties_params})
            page_tags = page["properties"]["Tags"]["multi_select"]
            for item in page_tags:
                name = item['name']
                data["multi_select"].append({"name": name})
            # 更新页面的属性
            update_payload = {
                "properties": {
                    "Tags": {
                        "multi_select": data["multi_select"]
                    }
                    # 其他属性更新
                }
            }
            # 执行更新操作
            update_page = self.global_notion.pages.update(page_id=page_id, **update_payload)
            print("更新状态", properties_params)
        except APIResponseError as e:
            print(f"API响应错误: {e}")
            raise
        except Exception as e:
            print(f"更新页面时发生错误: {e}")
            raise

    def update_page_properties(self, page_id, tags, area):
        """直接更新新属性"""
        update_payload = {
            "properties": {
                "Tags": {
                    "multi_select": [
                        {
                            "name": tags
                        }
                    ]
                },
                "领域": {
                    "select": {
                        "name": area
                    }
                },
                # 其他属性更新
            },
        }
        # 执行更新操作
        self.global_notion.pages.update(page_id=page_id, **update_payload)
        print(f'更新状态: {tags} {area}')


    # 基础属性映射
    base_property_mapping = {
        'title': ('标题', 'title'),
        'content': ('正文内容', 'rich_text'),
        'area': ('领域', 'select'),
        'hmcturl': ('关联URL', 'url'),
        'published_at': ('发布时间', 'date'),
        'picture_url': ('图片文件', 'files'),
    }

    def create_page_properties(self, page, additional_mapping=None):
        property_mapping = self.base_property_mapping.copy()
        if additional_mapping:
            property_mapping.update(additional_mapping)

        properties = {}
        for key, (notion_key, notion_type) in property_mapping.items():
            if key in page:
                if notion_type == 'title':
                    properties[notion_key] = {'title': [{'text': {'content': page[key]}}]}
                elif notion_type == 'rich_text':
                    properties[notion_key] = {'rich_text': [{'text': {'content': str(page[key])}}]}
                elif notion_type == 'select':
                    properties[notion_key] = {'select': {'name': page[key]}}
                elif notion_type == 'url':
                    properties[notion_key] = {'url': page[key]}
                elif notion_type == 'date':
                    properties[notion_key] = {'date': {'start': page[key]}}
                elif notion_type == 'files':
                    properties[notion_key] = {
                        'files': [
                            {
                                'name': '文件1',
                                'type': 'external',
                                'external': {'url': page[key]}
                            }
                        ]
                    }

        # Always add the 'Tags' property
        properties['Tags'] = {'multi_select': [{'name': '初始化'}]}
        return properties
    
    def create_page_duanwen(self, page):
        # 短文发布-手写科技
        # keji_additional_mapping = {
        #     'tag': ('tag', 'select'),
        # }
        properties = self.create_page_properties(page, None)
        new_page = self.global_notion.pages.create(
            parent={'database_id': self.global_database_id},
            properties=properties
        )
        return new_page

    def create_page(self, page):  # NBA-Data字段
        new_page = self.global_notion.pages.create(
            parent={
                'database_id': self.global_database_id
            },
            properties={
                '标题': {
                    'title': [
                        {
                            'text': {
                                'content': page['title']
                            }
                        }
                    ]
                },
                '中文标题': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['chinese_title']
                            }
                        }
                    ]
                },
                'Prompt': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['prompt']
                            }
                        }
                    ]
                },
                'id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page['id'])
                            }
                        }
                    ]
                },
                '领域': {
                    'select': {
                        'name': page['area']
                    }
                },
                '来源': {
                    'select': {
                        'name': page['datasource']
                    }
                },
                "Tags": {
                    "multi_select": [
                        {
                            "name": "初始化"
                        }
                    ]
                },
                "hmcturl": {
                    'url': page['hmcturl']
                },
                "发布时间": {
                    "date": {
                        "start": page['published_at']
                    }
                },
                "图片文件": {
                    "files": [
                        {
                            "name": "文件1",
                            "type": "external",
                            "external": {
                                "url": page['picture_url']
                            }
                        }
                    ]
                },
            }
        )
        return new_page

    def create_page_keji(self, page):  # NBA-Data字段
        new_page = self.global_notion.pages.create(
            parent={
                'database_id': self.global_database_id
            },
            properties={
                '标题': {
                    'title': [
                        {
                            'text': {
                                'content': page['title']
                            }
                        }
                    ]
                },
                '中文标题': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['chinese_title']
                            }
                        }
                    ]
                },
                'Prompt': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['prompt']
                            }
                        }
                    ]
                },
                'id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page['id'])
                            }
                        }
                    ]
                },
                '领域': {
                    'select': {
                        'name': page['area']
                    }
                },
                'tag': {
                    'select': {
                        'name': page['tag']
                    }
                },
                '来源': {
                    'select': {
                        'name': page['datasource']
                    }
                },
                "Tags": {
                    "multi_select": [
                        {
                            "name": "初始化"
                        }
                    ]
                },
                "hmcturl": {
                    'url': page['hmcturl']
                },
                "发布时间": {
                    "date": {
                        "start": page['published_at']
                    }
                },
                "图片文件": {
                    "files": [
                        {
                            "name": "文件1",
                            "type": "external",
                            "external": {
                                "url": page['picture_url']
                            }
                        }
                    ]
                },
            }
        )
        return new_page

    def retrieve_and_convert_to_markdown(self, block_id):
        """
        递归检索Notion块并转换为Markdown格式。
        """
        markdown_content = ""
        blocks = self.global_notion.blocks.children.list(block_id=block_id)["results"]
        # 获取blocks不重复类型
        block_types = set()
        for block in blocks:
            block_types.add(block["type"])
        print(f'block_types类型有: {block_types}')

        for block in blocks:
            markdown_content += self.to_markdown(block)
            if block.get("has_children", False):
                markdown_content += self.retrieve_and_convert_to_markdown(block["id"], self.global_notion)

        return markdown_content

    def to_markdown(self, block):
        """
        将Notion块转换为Markdown格式的文本。
        """
        block_type = block["type"]
        block_content = block.get(block_type, {})
        markdown_text = ""

        if block_type in ["paragraph", "heading_1", "heading_2", "heading_3"]:
            texts = block_content.get("rich_text", [])
            for text in texts:
                content = text["plain_text"]
                if block_type == "heading_1":
                    markdown_text += f"# {content}\n"
                elif block_type == "heading_2":
                    markdown_text += f"## {content}\n"
                    # # 标题二zhong添加截断标记
                    markdown_text += "\n<!-- truncate -->\n"
                    # insert_content = "\n<!-- truncate -->"
                    # content = insert_string_at_position(content, 100, insert_content)
                elif block_type == "heading_3":
                    markdown_text += f"### {content}\n"
                else:
                    markdown_text += f"{content} "
            markdown_text += "\n\n"

        elif block_type == "text_with_link":
            texts = block_content.get("rich_text", [])
            for text in texts:
                if 'href' in text:
                    # Assuming 'href' is the key where the link is stored
                    markdown_text += f"[{text['plain_text']}]({text['href']}) "
                else:
                    markdown_text += text['plain_text']
            markdown_text += "\n\n"  # Add a newline after processing all texts in the block

        elif block_type == "bulleted_list_item" or block_type == "numbered_list_item":
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"- {text['plain_text']}\n"

        elif block_type == "to_do":
            checked = block_content.get("checked", False)
            checkbox = "[x]" if checked else "[ ]"
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"{checkbox} {text['plain_text']}\n"

        elif block_type == "toggle":
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"> {text['plain_text']}\n"

        elif block_type == "image":
            image_url = block_content.get("file", {}).get("url", "")
            if len(image_url) == 0:
                image_url = block_content.get("external").get('url')
            markdown_text += f'![Image]({image_url})\n\n'

        elif block_type == "code":
            code_text = block_content.get("rich_text", [])[0].get("plain_text", "")
            language = block_content.get("language", "")
            markdown_text += f"```{language}\n{code_text}\n```\n"

        # 针对video类型的处理
        elif block_type == "video":
            # 提取视频的 URL
            video_url = block_content.get("external", {}).get("url", "")

            # 将视频 URL 转换为 Markdown 格式
            if video_url:
                markdown_text += f"![Video]({video_url})\n"
            else:
                markdown_text += "Video URL not found\n"

        # 针对quote类型的处理
        elif block_type == "quote":
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"> {text['plain_text']}\n"

        # 针对callout类型的处理
        elif block_type == "callout":
            # 获取 'callout' 块的文本内容
            texts = block_content.get("rich_text", [])

            # 假设 'callout' 块可能包含一个表情符号或图标
            icon = block_content.get("icon", {}).get("emoji", "")

            # 将表情符号或图标添加到 Markdown 文本的开始
            markdown_text += f"{icon} "

            # 添加 'callout' 块的文本内容
            for text in texts:
                markdown_text += text.get("plain_text", "")

            # 添加换行符来结束 'callout' 块
            markdown_text += "\n"

        return markdown_text
