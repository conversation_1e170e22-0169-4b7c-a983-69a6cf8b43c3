import random
from bs4 import BeautifulSoup
import markdown

def markdown_to_html(markdown_content, output_file_path, image_urls):
    try:
        # 使用自定义扩展来处理加粗文本
        html_content = markdown.markdown(markdown_content, extensions=['extra'])
        
        # 创建 BeautifulSoup 对象
        soup = BeautifulSoup(f'<html><head><title>文章</title></head><body><section id="nice" data-tool="wenhaofree" data-website="https://wenhaofree.com">{html_content}</section></body></html>', 'html.parser')
        
        # 获取section并设置样式
        section = soup.find('section')
        section['style'] = 'margin: 0; padding: 10px; background: none; width: auto;'

        # 添加全局样式
        style = soup.new_tag('style')
        style.string = """
        h2, h3 { margin: 30px 0 15px; padding: 0; display: flex; }
        h2 .content { font-size: 22px; color: rgb(53, 179, 120); font-weight: bold; display: block; }
        h3 .content { font-size: 20px; color: rgb(53, 179, 120); font-weight: bold; display: block; }
        p { padding: 8px 0; }
        figure { margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center; }
        figure img { display: block; max-width: 100%; margin: 0 auto; border: none; border-radius: 0; }
        figcaption { color: rgb(136, 136, 136); font-size: 14px; text-align: center; margin: 5px 0 0; }
        ul { list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; }
        a { color: rgb(53, 179, 120); text-decoration: none; border-bottom: 1px solid rgb(53, 179, 120); }
        """
        soup.head.append(style)

        # 处理所有段落中的strong标签
        for p in section.find_all('p'):
            # 获取段落的内容
            contents = list(p.contents)
            for i, content in enumerate(contents):
                if isinstance(content, str):
                    # 处理strong标签前的文本
                    if i + 1 < len(contents) and contents[i + 1].name == 'strong':
                        contents[i] = content.rstrip()
                    # 处理strong标签后的文本
                    if i > 0 and contents[i - 1].name == 'strong':
                        contents[i] = content.lstrip()
            
            # 清空段落内容并重新添加处理后的内容
            p.clear()
            for content in contents:
                p.append(content)

        # 获取所有段落
        paragraphs = section.find_all('p')

        # 随机选择要插入图片的段落
        num_images = min(len(image_urls), len(paragraphs))
        selected_paragraphs = random.sample(paragraphs, num_images)

        # 插入图片
        for i, paragraph in enumerate(selected_paragraphs):
            figure = soup.new_tag('figure')
            figure['data-tool'] = 'wenhaofree'
            figure['style'] = 'margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center;'
            img = soup.new_tag('img', src=image_urls[i])
            img['style'] = 'display: block; margin: 0 auto; max-width: 100%; border: none; border-radius: 0; object-fit: fill; box-shadow: none;'
            figcaption = soup.new_tag('figcaption')
            # figcaption.string = f'图片 {i + 1}'
            figcaption['style'] = 'color: rgb(136, 136, 136); font-size: 14px; line-height: 1.5em; text-align: center; font-weight: normal; margin: 5px 0 0; padding: 0;'
            figure.append(img)
            figure.append(figcaption)
            paragraph.insert_before(figure)

        # 处理标题
        for tag in section.find_all(['h2', 'h3']):
            tag['data-tool'] = 'wenhaofree'
            tag['style'] = 'margin: 30px 0 15px; padding: 0; display: flex;'
            content = tag.string
            tag.string = ''
            span = soup.new_tag('span', **{'class': 'content'})
            span.string = content
            if tag.name == 'h2':
                span['style'] = 'font-size: 22px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block;'
            else:
                span['style'] = 'font-size: 20px; color: rgb(53, 179, 120); line-height: 1.5em; letter-spacing: 0; font-weight: bold; display: block;'
            tag.append(span)

        # 处理段落
        for p in section.find_all('p'):
            p['data-tool'] = 'wenhaofree'
            p['style'] = 'margin: 0; padding: 8px 0;'

        # 处理列表
        for ul in section.find_all('ul'):
            ul['data-tool'] = 'wenhaofree'
            ul['style'] = 'list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px;'
            for li in ul.find_all('li'):
                li_section = soup.new_tag('section')
                li_section['style'] = 'margin: 5px 0;'
                li_section.extend(li.contents)
                li.clear()
                li.append(li_section)

        # 处理链接
        for a in section.find_all('a'):
            a['style'] = 'color: rgb(53, 179, 120); font-weight: bold; text-decoration: none; border-bottom: 1px solid rgb(53, 179, 120);'

        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup.prettify()))
        
        print(f'HTML文件已保存至: {output_file_path}')

        return True
    except Exception as e:
        print(f"生成HTML文件时发生错误: {e}")
        return False


def markdown_to_html_with_images(markdown_content, output_file_path, image_urls):
    """针对图片格式的生成HTML"""
    try:
        # 创建BeautifulSoup对象
        soup = BeautifulSoup('<html><head></head><body></body></html>', 'html.parser')
        
        # 添加样式
        style = soup.new_tag('style')
        style.string = '''
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }
        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
        '''
        soup.head.append(style)
        
        # 创建主体部分
        section = soup.new_tag('section', **{'class': 'markdown-body'})
        soup.body.append(section)
        
        # 处理Markdown内容
        if markdown_content.strip():
            html_content = markdown.markdown(markdown_content)
            section.append(BeautifulSoup(html_content, 'html.parser'))
        
        # 处理图片
        for idx, img_url in enumerate(image_urls):
            figure = soup.new_tag('figure')
            figure['style'] = 'margin: 0; padding: 0; text-align: center;'
            
            img = soup.new_tag('img')
            img['src'] = img_url
            img['style'] = 'max-width: 100%; height: auto; margin: 0 auto; display: block;'
            
            figcaption = soup.new_tag('figcaption')
            figcaption['style'] = 'color: #999; font-size: 14px; text-align: center; margin-top: 5px;'
            # figcaption.string = f'图 {idx + 1}'
            
            figure.append(img)
            figure.append(figcaption)
            section.append(figure)
        
        # 处理标题
        for tag in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            tag['style'] = 'margin: 30px 0 15px; padding: 0; color: #35b378;'
        
        # 处理段落
        for p in section.find_all('p'):
            p['style'] = 'margin: 0; padding: 8px 0;'
        
        # 处理列表
        for ul in section.find_all('ul'):
            ul['style'] = 'list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: #000;'
            for li in ul.find_all('li'):
                li['style'] = 'margin: 5px 0; color: #595959; font-size: 15px; line-height: 1.8em;'
        
        # 处理链接
        for a in section.find_all('a'):
            a['style'] = 'color: #35b378; font-weight: bold; text-decoration: none; border-bottom: 1px solid #35b378;'
        
        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup.prettify()))
        
        print(f'HTML文件已保存至: {output_file_path}')
        return True
    except Exception as e:
        print(f"生成HTML文件时发生错误: {e}")
        return False


def main():
    markdown_content = """
在浙江金华，一场令人震惊的丑闻悄然上演。35岁的女教师刘某与一名年轻男学生的出轨事件被她的丈夫揭发，瞬间引发了网络热议。事情的起因要追溯到今年5月，刘某的丈夫察觉到妻子对他的态度异常冷淡，聊天时总是爱理不理。经过几个月的暗中调查，他终于在某酒店调取到了妻子与学生的亲密监控视频。

**“我绝对不能让她继续这样下去！”**丈夫愤怒地表示。于是，他将这些证据曝光到社交媒体上，甚至向学校举报，试图为自己的婚姻讨回公道。然而，事情的发展却出乎他的意料。刘某不仅没有承认错误，反而以“侵犯个人隐私”为由，将丈夫告上法庭，要求赔偿50万元的精神损失费！这场家庭悲剧迅速演变成了一场法律斗争，令人咋舌。

随着事件的发酵，刘某的身份也被扒出，她是一名颜值颇高的年轻教师，深受学生喜爱。网友们纷纷表示，**“身为老师，竟然做出如此丧失师德的事情，真是不可思议！”**而刘某的丈夫则面临着可能的行政处罚，甚至可能被拘留，真是“反转”不断。

**吃瓜群众怎么说**

这起事件在网络上引发了热烈讨论，网友们纷纷发表看法。有人愤怒地评论道：**“这女的脸皮也太厚了吧？做错事还有理了？”**也有网友从法律角度分析，认为虽然刘某的行为不当，但丈夫公开隐私同样违法，**“这事儿真是极品配极品，一个道德败坏，一个法律意识欠费。”**更有网友心疼那个16岁的男学生，**“这孩子才16岁啊，被卷入这种事对心理影响得多大？”**

在这场舆论的漩涡中，支持和反对的声音交织，形成了一个复杂的社会现象。

**类似事件大盘点**

近年来，师生不伦恋事件频频登上热搜。比如，2023年某高校女讲师与研究生的恋情被举报，最终双双开除；还有一起高中女教师与男学生交往的案件，也曾引发轩然大波。根据教育部门统计，近三年来全国共查处师德违规案件超过200起，其中师生关系违规占比达15%。这些事件的共同点在于，师德失范的背后往往隐藏着家庭危机和法律意识的缺失。

**观点**

这起事件不仅暴露出师德建设的漏洞，更折射出当代婚姻危机和法律意识的双重困境。我们不禁要问，**在信息爆炸的时代，选择以曝光为手段维权，是否真能解决问题？**或许，我们更需要思考的是，如何在维护正义的同时，不让仇恨和暴力成为解决问题的主导方式。你觉得丈夫的做法对吗？欢迎在评论区分享你的看法！
"""
    output_path = "output.html"
    image_urls = ['http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSyV1M9L1G8VgIibQ7lFX2a4wLoicdKEg1RHNRSqEYGfMbiavah689L04TA/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSia3ef27qq994qkibWKTGSS9Oyq6ichzLr7304SgvAXgxe1rjju8YBbuNQ/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSqBNeibM20ia7M5QvpYmiaF4ad6V4b59uOl1jSO278icuvZQEsdeZT2mRxA/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_jpg/wQf2dOmqSZyWV5I6ibVQS389ZS6RjicHHSR2pZEvV5BIPVbtgLH3G2HHIN2p5U7jcNyhib0GFCv8vYdhSia7IxABHw/0?from=appmsg']
    result = markdown_to_html(markdown_content, output_path, image_urls)
    print(f"HTML生成{'成功' if result else '失败'}")

# 示例使用
if __name__ == "__main__":
    main()