import pypandoc
import os
from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches

import subprocess
import tempfile

'''markdown内容转换word文档'''


def markdown_to_docx_from_string(markdown_content, output_file_path):
    # 创建临时文件并写入Markdown内容
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.md', delete=False, encoding='UTF-8') as temp_file:
        temp_file.write(markdown_content)
        temp_file_path = temp_file.name

    try:
        # 使用pandoc进行转换
        pandoc_command = [
            'pandoc',
            temp_file_path,
            '-s',
            '-o', output_file_path
        ]
        subprocess.run(pandoc_command, check=True)
    finally:
        # 删除临时文件
        import os
        os.remove(temp_file_path)


def convert_html_to_docx(input_html, output_docx, template_docx=None):
    extra_args = []
    if template_docx:
        extra_args.extend(['--reference-doc', template_docx])
    # extra_args=['--extract-media=./media']
    output = pypandoc.convert_file(input_html, 'docx', outputfile=output_docx, extra_args=extra_args)

    if output == "":
        print("文件转换成功！")
    else:
        print("文件转换失败：", output)


'''1. 并排表格输出图片文件-用于头像工具'''


def convert_html_to_docx_table_image(input_html_file, output_docx):
    """将HTML转换表格图片的docx"""
    # 读取本地HTML文件
    with open(input_html_file, 'r', encoding='utf-8') as file:
        html_code = file.read()
    # 解析HTML
    soup = BeautifulSoup(html_code, 'html.parser')
    # 创建Word文档
    doc = Document()
    # 创建表格
    table = doc.add_table(rows=1, cols=2)
    table.style = 'Table Grid'
    # TODO: 设置红色边框

    # 遍历HTML中的图片
    for i, img in enumerate(soup.find_all('img')):
        try:
            img_src = img['src']
            img_width = 257
            img_height = 257
            # 添加图片到表格单元格
            cell = table.cell(0, i % 2)
            run = cell.paragraphs[0].add_run()
            run.add_picture(img_src, width=Inches(img_width / 96), height=Inches(img_height / 96))
            
            # if len(img['style'])==0:
            #     img_width = 257
            #     img_height = 257
            #     # 添加图片到表格单元格
            #     cell = table.cell(0, i % 2)
            #     run = cell.paragraphs[0].add_run()
            #     run.add_picture(img_src, width=Inches(img_width / 96), height=Inches(img_height / 96))
            # else:
            #     # 截取异常情况: 'vertical-align: middle;max-width: 100%;width: 257px;box-sizing: border-box;height: 257px;'
            #     img_width = int(img['style'].split('width: ')[1].split('px')[0])
            #     img_height = int(img['style'].split('height: ')[1].split('px')[0])
            #     # 添加图片到表格单元格
            #     cell = table.cell(0, i % 2)
            #     run = cell.paragraphs[0].add_run()
            #     # TODO : img_src 必须是本地图片文件路径, URL不可以
            #     # img_src='/Volumes/文章存档/头像/图文下载/姓氏头像：谭、吴、李、刘、彭、张、赵、高、郑、曾、何、肖、廖、任、蒋、杜、汪、胡、朱、唐、潘、薛、李、郭、孟、雷、岳、曲……/8.jpg'
            #     run.add_picture(img_src, width=Inches(img_width / 96), height=Inches(img_height / 96))
        except Exception as e:
            print(f'头像文档异常:{e}')
            continue
        # 每两张图片后添加新行
        if (i + 1) % 2 == 0 and i + 1 < len(soup.find_all('img')):
            table = doc.add_table(rows=1, cols=2)
            table.style = 'Table Grid'

    # 保存Word文档
    doc.add_paragraph('...')  # 头像-保证文档有字
    doc.save(output_docx)


def convert_html_to_docx_image(input_html_file, output_docx):
    """直接将HTML转换docx"""
    # 读取本地HTML文件
    with open(input_html_file, 'r', encoding='utf-8') as file:
        html_code = file.read()

    # 解析HTML
    soup = BeautifulSoup(html_code, 'html.parser')

    # 创建Word文档
    doc = Document()

    # 遍历HTML中的图片
    for i, img in enumerate(soup.find_all('img')):
        try:
            img_src = img['src']
            img_width = 578 # 单图的长宽设置
            img_height = 578

            # 添加图片到Word文档
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(img_src, width=Inches(img_width / 96), height=Inches(img_height / 96))

        except Exception as e:
            print(f'头像文档异常:{e}')
            continue

    # 保存Word文档
    doc.add_paragraph('...')  # 头像-保证文档有字
    doc.save(output_docx)


# 示例调用
# convert_html_to_docx_image('/Users/<USER>/Downloads/04/04.html', 'output.docx')
