
import requests
from bs4 import BeautifulSoup
def toutiao_parese_content_images(url):  # 解析网页函数
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    cookie_string = 'store-region-src=uid; tt_webid=7301242363711194674; ttcid=0a34bccd756d4de7b89612c8cb3651e189; csrftoken=6aea9293b4b671ec05d5c61cb304ca13; _S_IPAD=0; notRedShot=1; _tea_utm_cache_24={%22utm_medium%22:%22wap_search%22}; _tea_utm_cache_2018={%22utm_medium%22:%22wap_search%22}; passport_csrf_token=498cda817eb950de6e8c90deac2c3289; passport_csrf_token_default=498cda817eb950de6e8c90deac2c3289; sso_uid_tt_ss=0a607f385e5af45e6a56bcdf45e69046; toutiao_sso_user_ss=1e2c5a94b7c8c5f99274984fb8bfca2c; n_mh=1CGLpzWC_l9GbCiLNUbgGBl5ifLLQ_xXubrJI1yNUIg; sso_uid_tt=0a607f385e5af45e6a56bcdf45e69046; toutiao_sso_user=1e2c5a94b7c8c5f99274984fb8bfca2c; sid_ucp_sso_v1=1.0.0-KDlmMWE0NGIzMDk4MDQ2NjNhNzk1YWRmMWJjOGNiNDA4ZmRiYmQyM2YKHgiUlfCDyfS-AhDOw5uvBhgYIAwwuJCk6gU4BkD0BxoCaGwiIDFlMmM1YTk0YjdjOGM1Zjk5Mjc0OTg0ZmI4YmZjYTJj; ssid_ucp_sso_v1=1.0.0-KDlmMWE0NGIzMDk4MDQ2NjNhNzk1YWRmMWJjOGNiNDA4ZmRiYmQyM2YKHgiUlfCDyfS-AhDOw5uvBhgYIAwwuJCk6gU4BkD0BxoCaGwiIDFlMmM1YTk0YjdjOGM1Zjk5Mjc0OTg0ZmI4YmZjYTJj; sid_guard=3e06a8d7ad67d30dbaf1391fc4acc598%7C1709629903%7C5184001%7CSat%2C+04-May-2024+09%3A11%3A44+GMT; uid_tt=695b7c6fbd979f2e7d13cc0578dccca4; uid_tt_ss=695b7c6fbd979f2e7d13cc0578dccca4; sid_tt=3e06a8d7ad67d30dbaf1391fc4acc598; sessionid=3e06a8d7ad67d30dbaf1391fc4acc598; sessionid_ss=3e06a8d7ad67d30dbaf1391fc4acc598; sid_ucp_v1=1.0.0-KDhkNmQ5ODVkM2VlNDRjZWZjODk2MjdlNjIwOGY2NmUyNzdhZDA4MmIKGAiUlfCDyfS-AhDPw5uvBhgYIAw4BkD0BxoCbHEiIDNlMDZhOGQ3YWQ2N2QzMGRiYWYxMzkxZmM0YWNjNTk4; ssid_ucp_v1=1.0.0-KDhkNmQ5ODVkM2VlNDRjZWZjODk2MjdlNjIwOGY2NmUyNzdhZDA4MmIKGAiUlfCDyfS-AhDPw5uvBhgYIAw4BkD0BxoCbHEiIDNlMDZhOGQ3YWQ2N2QzMGRiYWYxMzkxZmM0YWNjNTk4; odin_tt=ef68f858a0a9130653f4097b87fed7c126207fddb8872b18da00e5dec6c857504d6ce8ed36be4c097ca10e62f359b4f4; _S_DPR=1; WIN_WH=1920_934; PIXIEL_RATIO=1; FRM=new; d_ticket=fcc6c8b879b3764ae67104a24ade37eec4c41; passport_auth_status=05769abc83d9e968ec3cf612cf53beb8%2C; passport_auth_status_ss=05769abc83d9e968ec3cf612cf53beb8%2C; _ga_1Y7TBPV8DE=GS1.1.1712676595.5.0.1712676606.0.0.0; _ga=GA1.1.*********.1708497175; store-region=cn-bj; s_v_web_id=verify_lv96h3nc_smYwTxV1_6S8E_4Rlj_9iA0_ihgZwVJG0C0U; local_city_cache=%E6%B2%A7%E5%B7%9E; gfkadpd=24,6457; __feed_out_channel_key=sports; _S_WIN_WH=1920_968; csrf_session_id=02d11ad87c6dd145ae5d4539700729d3; msToken=VoiJLonJxkTZcsUHiegcVOOb2wYeoMpxo_CMScr4T-Z9gZf8LyfDESdvhZemDcEzpxcIDz5HC1wqzVagxm5YCJUxXjI6zDo1LWVAAiUdErsYa2Xi7hjF6g==; _ga_QEHZPBE5HH=GS1.1.1714189391.78.1.1714189413.0.0.0; tt_anti_token=bh3PrAhfZz2vg6-fc242828f592ee26220cfe5f3c48672446f894369a2a61ed870848af3dfd0c3d; ttwid=1%7Cd2v0AhVI3wOQX-pAxT8dZQNiSzG5AK0wXeQFmw9wbSY%7C1714189413%7C9734fb1d83821785e21ca52be0927376572c22d1cca9ff13b6c54f2b085cee5d; tt_scid=TdmnUitRNE5RByEntzfb1h4PVR0bj7zjeSy4CynzgU9EzhhdFICRDApCQ9krNXXy775a'
    cookie_dict = {}
    for cookie_item in cookie_string.split(';'):
        if '=' in cookie_item:
            key, value = cookie_item.strip().split('=', 1)
            cookie_dict[key] = value
    r = requests.get(url, headers=headers,cookies=cookie_dict)
    soup = BeautifulSoup(r.text, 'lxml')  # 使用 'lxml' 解析器，也可以用 'html.parser'
    article_content_div = soup.select_one('.article-content')
    div_text = article_content_div.get_text()
    # picture_url = article_content_div.find('img').get('src')
    picture_urls = [img.get('src') for img in article_content_div.find_all('img')]
    return picture_urls,div_text
 
if __name__ == '__main__':
    url='https://www.toutiao.com/article/7382851930441155072'
    toutiao_parese_content_images(url=url)