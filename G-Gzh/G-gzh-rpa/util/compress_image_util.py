from PIL import Image
import os

def compress_image(input_path, output_path, target_size_kb):
    # 打开图片
    with Image.open(input_path) as img:
        # 如果图片模式是RGBA，转换为RGB
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # 初始质量
        quality = 95
        # 保存压缩后的图片
        img.save(output_path, 'JPEG', quality=quality, optimize=True)
        
        # 如果大小仍然超过目标，继续压缩
        while os.path.getsize(output_path) > target_size_kb * 1024 and quality > 10:
            quality -= 5
            img.save(output_path, 'JPEG', quality=quality, optimize=True)

def process_image(input_path, output_path, max_size_mb=2, target_size_kb=500):
    # 检查输入文件大小
    if os.path.getsize(input_path) > max_size_mb * 1024 * 1024:
        compress_image(input_path, output_path, target_size_kb)
        print(f"Image compressed: {input_path} -> {output_path}")
    else:
        print(f"Image already smaller than {max_size_mb}MB: {input_path}")

# 使用示例
# input_image = "/Users/<USER>/Downloads/_0.png"
# output_image = "/Users/<USER>/Downloads/test_0.png"
# process_image(input_image, output_image)