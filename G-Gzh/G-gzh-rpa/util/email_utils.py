# 配置邮件发送者信息
sender_email = "fuwen<PERSON>@88.com"
sender_password = "cbSJBswru6HF68Yp"


def send_email(subject: str, body: str):
    import smtplib

    smtp_server = 'smtp.88.com'
    smtp_port = 465
    receiver_email = '<EMAIL>'

    # 主题和正文
    # 将主题和正文编码为 UTF-8
    subject_bytes = subject.encode('utf-8')
    body_bytes = body.encode('utf-8')
    # 创建消息
    message = f'Subject: {subject_bytes.decode()}\n\n{body_bytes.decode()}'.encode()

    # Connexion au serveur SMTP et envoi de l'e-mail
    with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
        server.login(sender_email, sender_password)
        server.sendmail(sender_email, receiver_email, message)
    print('E-mail success !')
