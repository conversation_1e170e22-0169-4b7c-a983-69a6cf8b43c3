import os
import shutil


def find_latest_docx(source_folder):
    """获取最新的docx文件"""
    docx_files = [f for f in os.listdir(source_folder) if f.endswith('.docx')]
    if not docx_files:
        raise FileNotFoundError("No .docx files found in the source folder.")

    latest_file = None
    latest_time = 0

    for file in docx_files:
        file_path = os.path.join(source_folder, file)
        file_time = os.path.getmtime(file_path)
        if file_time > latest_time:
            latest_time = file_time
            latest_file = file_path

    return latest_file


def move_file(source_file, destination_folder):
    """移动文件到指定文件夹"""
    if not os.path.exists(destination_folder):
        os.makedirs(destination_folder)

    destination_file = os.path.join(destination_folder, os.path.basename(source_file))
    shutil.move(source_file, destination_file)
    print(f"Moved '{source_file}' to '{destination_file}'")



if __name__ == "__main__":
    source_folder = "/Volumes/文章存档/体育/待发布"
    destination_folder = "/Volumes/文章存档/体育01/待发布"

    try:
        latest_docx = find_latest_docx(source_folder)
        move_file(latest_docx, destination_folder)
    except Exception as e:
        print(f"An error occurred: {e}")
