from poe_api_wrapper import PoeApi
import time

'''
1.文档: https://github.com/wenhaofree/poe-api-wrapper?tab=readme-ov-file#getting-p-b-and-p-lat-cookies-required 
2. 最好用纯净的IP才能正常使用- 最好是美国节点
3. Claude-3-<PERSON>	claude_2_1_cedar  适合情感写作   区分订阅和免费版本
4. 这边api调用会同步在poe的页面上.
5. https://poe.com/api/gql_POST  获取fromkey
6. https://poe.com/chat/3dyguwdaewr7pqcvyio  最开始的获取chatid

# bot = "claude_2_1_cedar"  # Claude-3-Opus	 2000积分 很贵! 慎用!
# bot = "gpt4_o"  # GPT-4o		 300积分
# bot = "a2"  # Claude-instant				 2000积分
# bot = "gpt3_5"  # GPT-3.5-Turbo-Raw			

'''
# 账号:<EMAIL>
tokens = {
    'p-b': 'kW2RLIjVAc2xbb_gqjm6yQ%3D%3D',
    'p-lat': 'p4dZtD8Jb29FhxdWD1GHg4OMR0rW5xhQH1koeDj0sA%3D%3D',
    'formkey': '01e4a2485180efd868bc84fd60505c9b',
    '__cf_bm': 'ykytlR8A9Pmos4xMtICzFwOrFDdNXGHykME.jhBSJYQ-1719393328-*******-w1mf2J2MuvNUsMrc2G1Q4mc4g4dZTICPvcF2qEJr9v.BZqn5qwAwKpeRDDQMzOiDc.Z8hGIg9WwA1qt7WtTMKw',
    'cf_clearance': 'kITadIpmP6Bqtr4sKE3WwHyBjcFWB0u_pRogauSHShQ-1719393334-*******-Q2N5LfVxsEqX6_U4EK4jYAKbO728KbfvC2ahZDxAFxbDEf52QIowWgOwST81E.MnGM5xnNtu4C53Bl_5ZB0.qQ'
}

# 账号:<EMAIL>
# tokens = {
#     'p-b': 'jh_badxJuAc-L7w3KahjoA%3D%3D',
#     'p-lat': '3rYN8cnwcyXig3dPuGLS8nqNdhE%2FEQXMHw8vm2ew5g%3D%3D',
#     'formkey': '7903a6d18aacf79ae42186ff1de6b2d7',
#     '__cf_bm': 'Fso7XUnzPRzbthyDSES7Q0XUSDxA_uYPs5mOJjd.6u8-1734068368-*******-F_3ZIeKXKyhUGykply_BZSMFG_aRA5omh.X47v9tuuXMG75zXOa3dGtLY5FUdx8.CP3f4hDGTSp3V8DUj28Wjg',
#     'cf_clearance': '0wOi6PgPHuRQ3TDyPixE.Rrem7N1VqR2K8k.13QRt0E-1734068370-*******-Ydfhmzxt3taRR_e9qTwofbejd9AE5m.KEw6.h4DBh4M.NPGpnc62Gmh0QKGmbRF9jUamp7bx5blucUGdqnNzYSIrF7WDWdLIzBoF.odNHqJdsd8I_I63ct8De6m9ZQOzRMgBInn_gfkWZwMdZq6K2C9CQNK_iCXKEjptsZ6JLpNv88jsnc7Vrk05MXtdkPUfThy1DtNPaHAnqQDgyCUWdnOWd.ZDmozxGkouEdBSVSXcrrXUF1_CtUAHhZjS4p30CHEg4Xpw9olOclRs7pE3xqYTsJcPQC77epe5umomHRU1Z4MAfEARmraVPDlRILQxpu85S7vOGb7MdkR7cJasZdszXQKgQ0PMFDC_iitvPifnC0B.HxiN5TxfxrOo1uXiPnnVV3KBaJJpyvNCM9M0iQ'
# }
proxy_context = [
    {"https://127.0.0.1:7890", "http://127.0.0.1:7890"},
]
client = PoeApi(tokens=tokens, proxy=proxy_context)



prompt_qinggan = """
您是一位专业的情感文章写手,擅长创作引人入胜的两性关系内容。请按照以下风格和要求创作一篇文章:

1. 文章风格:
- 直白坦率,不拐弯抹角
- 使用夸张的比喻和生动的描述
- 喜欢用数字、列表等方式归纳观点
- 善用反问和设问增加文章张力
- 适当加入对话和谚语增加趣味性
- 语言通俗易懂,符合大众表达习惯

2. 创作要求:
- 重新生成标题在首行,标题要有中文标点符号,最好包含数字、金钱、悬念、两性关系等吸引读者兴趣的元素,标题字数在30字以内
- 按照主题内容给出符合中心思想的小标题,每个小标题用**加粗**
- 每个小标题要有对应的段落内容烘托,要有男女情感对话
- 适当添加谚语增加内容的情感,具有趣味性和吸引力
- 末尾最后给一行话要有引人思考和评论的反问句,不要出现结语总结词汇
- 注意梳理人物关系,避免错误的逻辑关系
- 语言形象生动,情真意切,符合中国人的表达习惯
- 字数1200字以上,使用Markdown语法输出内容
- 内容要积极向上,传播正能量,引导社会和谐

请根据以上要求，围绕给定的标题创作出三篇文章。记住每一个开篇的文章开头必须用简体中文大写序号标记出，其他统一用数字序号,而且同时每篇文章的第一行必须要标题放在首行。文章和文章之间要换行。 无需额外解释，直接开始创作。
文章标题是:"""

# prompt_tiyu_wtt="""请充当文案高手。写作时，请保留原文的主题，并使用更加口水文和更强烈的情感表达，尤其是第一句。请勿打印文本以外的内容。中文输出时，字数限制在 150 字以内。忠实于原文的精髓，但要增强口语化的魅力和情感冲击力，尤其是一开始。没有多余的文字，只有纯粹的力量。最后一行内容和读者互动，保持在 150 字以内，末尾添加标签，使用中文，断行以增强感染力。内容是:"""
prompt_tiyu_wtt="""你是一位擅长创作病毒式传播内容的资深文案大师。你的任务是将给定的文本改写成极具传播力的版本。请遵循以下指南：

1. 保留原文的核心主题和关键信息。
2. 使用更加吸引眼球的表述和强烈的情感表达，特别是开头部分。
3. 增强内容的口语化魅力和情感冲击力。更加口语化形式阐述内容，能引起读者关注。
4. 输出限制在190字以内。
5. 最后一行与读者互动，只提出问题！
6. 在文末添加2-3个相关话题标签。

输出格式：
[改写后的内容，包括互动句]

#标签1 #标签2 #标签3

注意：
- 只输出改写后的内容，不要包含任何解释或元信息。
- 使用简体中文。
- 适当断行以增强可读性和感染力。
- 不要使用引号或其他特殊格式。

请基于以上要求，将我接下来提供的文本改写成病毒式传播内容。 内容链接是："""

prompt_tiyu = """
这是我的几篇文章，请仔细研究我之前提供的文章，学习其创作风格，然后根据下面的创作要求创作文章。

创作要求:
1. 根据原文内容重新生成新闻文章,保证消息的真实可靠性,不要随意编造;
2. 重新生成标题在首行,标题要有感叹号,包含数字、金钱、悬念等吸引读者兴趣的元素,字数在31字以内;
3. 文章的开篇段落要注明新闻报道来源出处;
4. 文章的段落要描述事件的经过和细节，给出引人深度思考的观点;
5. 部分观点内容要有情绪渲染,引起读者的共鸣，保证内容的吸引力;
6. 不需要小标题，重点内容用加粗标识;
7. 末尾给出反问的一句话,引起读者互动,但不要出现总结性话语;
8. 内容最后给出文章的消息来源出处包含来源URL；
9. 不要总结信息，不要出现未来期望，不要使用总结的词汇和段落;
10. 全文至少有1200字，使用Markdown语法输出内容，重点内容用**加粗**，不要使用##； 
11. 请直接开始创作,不需要任何解释性话语。

文章链接是:"""

prompt_keji = """
这是我的几篇文章，请你先学习我的创作风格，然后根据下面的创作要求创作文章。
创作要求:
根据原文内容重新生成新闻文章,要保证消息的真实可靠性,不要随意瞎编乱造;
重新生成标题在首行,标题要有感叹号,包含数字,金钱,悬念等吸引人读者兴趣的标题,标题字数在31字以内;
描述事件的经过和一些细节，给出引人深度思考的观点，情绪渲染引起读者的共鸣，保证标题吸引力；
不需要小标题，重点内容要用加粗标识;
末尾给出中心思想的反问句，不要总结信息，不要出现未来期望，总结的词汇和段落 全文至少1200字，Markdown语法输出内容，小标题用**加粗，不要用##；请立即创作不需要任何解释话语。
文章链接是:"""

prompt_yuer = """
这是我的几篇文章，请仔细研究我之前提供的文章，学习其创作风格，，然后根据下面的创作要求创作文章。
创作要求:
重新生成标题在首行,标题要有中文标点符号增强语气,最好包含数字,金钱,悬念,反差等吸引人读者兴趣的标题,标题字数在31字以内;
按照主题内容给出符合中心思想的小标题，每个小标题要有对应的段落内容烘托，要有情感对话，适当添加谚语增加内容的情感，具有趣味性和吸引力，这样读者看文章才会欲罢不能;
末尾最后给一行话要有引人思考评论的反问句,不要出现结语总结词汇；
注意梳理人物关系，避免错误的逻辑关系，语言形象生动，情真意切，符合中国人的表达习惯;
重点内容要用加粗标识;
字数1200字以上，Markdown语法输出内容，小标题用**加粗，不要用##；
请根据以上要求，围绕给定的标题创作出三篇文章。记住每一个开篇的文章开头必须用简体中文大写序号标记出，其他统一用数字序号,而且同时每篇文章的第一行必须要标题放在首行。文章和文章之间要换行。 无需额外解释，直接开始创作。
标题是:"""

prompt_chuangye = """
这是我的几篇文章，请你先学习我的创作风格，然后根据下面的创作要求创作文章。
创作要求:
重新生成标题在首行,标题要有中文标点符号增强语气,最好包含数字,金钱,悬念,反差等吸引人读者兴趣的标题,标题字数在31字以内;
按照主题内容给出符合中心思想的小标题，每个小标题要有对应的段落内容烘托，要有情感对话，适当添加谚语增加内容的情感，具有趣味性和吸引力，这样读者看文章才会欲罢不能;
末尾最后要有一句话，要有引人思考评论的反问句,不要出现结语总结词汇；
注意梳理人物关系，避免错误的逻辑关系，语言形象生动，情真意切，符合中国人的表达习惯;
重点内容要用加粗标识;
字数1200字以上，Markdown语法输出内容，小标题用**加粗，不要用##；
请根据下面标题，模仿风格来创作1篇文章。要求内容积极向上，传播正能量，引导社会和谐。请立即创作不需要任何解释话语。
标题是:"""

prompt_xingzuo = """
记住你是星座命理大师，擅长星座，生肖等高级专业知识。这是我的几篇文章，请你先学习我的创作风格，然后根据下面的创作要求创作文章。
创作要求:
重新生成标题在首行,标题要有中文标点符号增强语气,但不要有双引号,一定要有生肖相关,最好包含数字,金钱,悬念,反差等吸引人读者兴趣的标题,标题字数在31字以内;
开篇内容最好是引用一句谚语或者名人名言俗话说等等，要和主题相关，接切入文章正文内容，不要出现探讨探索性语句；
按照主题内容给出符合中心思想的小标题，每个小标题要有对应的段落内容烘托，要有情感对话，适当添加谚语增加内容的情感，具有趣味性和吸引力，这样读者看文章才会欲罢不能;
末尾最后给一行话要有引人思考评论的反问句,不要出现结语总结词汇；
注意梳理人物关系，避免错误的逻辑关系，语言形象生动，情真意切，符合中国人的表达习惯;
重点内容要用加粗标识;
字数1200字以上，Markdown语法输出内容，小标题用**加粗，不要用##；
请根据以上要求，围绕给定的标题创作出三篇文章。记住每一个开篇的文章开头必须用简体中文大写序号标记出，其他统一用数字序号,而且同时每篇文章的第一行必须要标题放在首行。文章和文章之间要换行。 无需额外解释，直接开始创作。

标题是:"""

prompt_wenan = """我期望你能扮演句子释义大师， 根据我输入的标题，生成相似的句子，含义不变，要押韵，要口语化，通俗易懂。
# 字符：
句子释义大师

## 技能：
1. 自主解释与用户输入的内容含义密切相关的句子
2. 擅长构建具有平行结构或押韵短语的句子
3. 确保内容含义一致

## 约束条件：
1. 改写后的句子中的字符数必须与用户的输入相匹配
2. 每次只提供一个答案，即释义的句子
不要解释，直接给出创作回答。

我的标题是:"""

prompt_zhichang = """
你是一位资深的职场专家，精通职场规则和高级管理知识。请仔细研究我之前提供的文章，学习其创作风格，然后按照以下要求创作一篇新的职场文章：

1. 标题：
   - 在首行重新生成一个吸引人的标题
   - 使用中文标点符号增强语气
   - 必须包含职场相关内容
   - 最好包含数字、金钱、悬念或反差等元素
   - 标题长度不超过60字，大于15个字。

2. 开篇：
   - 以一句与主题相关的谚语、名人名言或俗语开始
   - 直接切入正文内容，避免使用探讨性或探索性语句

3. 正文结构：
   - 创建符合中心思想的小标题，使用**加粗**标记
   - 每个小标题下应有相应的段落内容
   - 包含情感对话和适当的谚语，增加内容的趣味性和吸引力
   - 使用加粗标记突出重点内容

4. 写作风格：
   - 语言要直白犀利，不回避尖锐话题
   - 善用比喻和类比解释复杂概念
   - 确保人物关系和逻辑关系准确
   - 使用形象生动、情真意切的语言，符合中国人的表达习惯

5. 结尾：
   - 以一个引人思考的反问句结束
   - 避免使用明显的结语或总结词汇

6. 其他要求：
   - 文章长度至少1200字
   - 使用Markdown语法输出内容
   - 内容要积极向上，传播正能量，引导社会和谐

请根据以上要求，围绕给定的标题创作出三篇文章。记住每一个开篇的文章开头必须用简体中文大写序号标记出，其他统一用数字序号,而且同时每篇文章的第一行必须要标题放在首行。文章和文章之间要换行。 无需额外解释，直接开始创作。

标题是："""
# @Claude-3.5-Sonnet
prompt_redian = """ @Claude-3.5-Sonnet 你是一位擅长写爆款文章的资深媒体人，善于将热点事件转化为引人入胜的故事。请用以下方式创作：

标题（必须放在首行，30字以内,为二级标题）：
- 包含数字、金钱、悬念或反差等吸引眼球的元素
- 用中文感叹号或问号增强语气
- 直击事件最有冲突或情感的爆点

**事件始末**（400字左右）
用讲故事的方式展开，重点描述：
- 开场要直击事件最戏剧性的瞬间
- 按时间顺序还原关键场景
- 加入当事人原话和细节描写增加真实感
- 核心信息用加粗突出

**吃瓜群众怎么说**（300字左右）
像朋友圈吐槽一样展现各方观点：
- 选最有代表性的正反方评论
- 用网络热词和口语化表达
- 突出情感共鸣和争议点

**类似事件大盘点**（300字左右）
以八卦的口吻对比分析：
- 挑1-2个相似热点案例
- 找出共同规律和不同之处
- 用数据佐证，揭示社会现象

**观点**（200字左右）
像和读者聊天一样分享见解：
- 从小事件看大趋势
- 提出有深度的思考角度
- 设置悬念或争议引导读者互动

写作风格：
- 像朋友聊八卦一样自然流畅
- 用生动的比喻和网络热词
- 情感要有张力但不煽情
- 观点要有态度但不偏激
- 分段要简洁，重点突出
- 全程保持Markdown格式

直接开始创作，不需要解释, 不要有小标题, 必须大于1000字。热点事件链接是:"""


"""
{'subscription': {'isActive': True, 'planType': 'monthly', 'id': 'U3Vic2NyaXB0aW9uOjE5MjQyNTk3ODc=', 'purchaseType': 'web', 'isComplimentary': False, 'expiresTime': 1724653066000000, 'willCancelAtPeriodEnd': False, 'isFreeTrial': False, 'purchaseRevocationReason': None}, 'messagePointInfo': {'messagePointResetTime': 1724653153000000, 'messagePointBalance': 997940, 'totalMessagePointAllotment': 1000000, 'id': 'TWVzc2FnZVBvaW50SW5mbzoxOTI0MjU5Nzg3'}}
"""
def get_settings():
    """查询剩余积分"""
    data = client.get_settings()
    if data is None: 
        print('无法连接到POE的API,检查代理服务')
    else:
        message_point_balance = data['messagePointInfo']['messagePointBalance']
        print(f"POE剩余积分 的值是: {message_point_balance}")
    return data

# 配置字典
area_config = {
    '情感': {
        'prompt': prompt_qinggan,
        'chatid': '638293866',
        'chatcode': '3jnjvi07cmydt75ptcd',
        'bot': 'Claude-3.5-Sonnet',
        'author': 'claude_3_igloo',
        'sleep_time': 80
    },
    '体育': {
        'prompt': prompt_tiyu,
        'local_paths': ["/Users/<USER>/temp/篮球教学论坛无图.pdf"],
        'chatid': '562933140',
        'chatcode': '3ee3zllcabtrtvwq4dh',
        'sleep_time': 0
    },
    '微体育': {
        'prompt': prompt_tiyu_wtt,
        'chatid': '759437510',
        'chatcode': '3r3krd3ze9ivwsfy3v8',
        'bot': 'gpt4_o_mini_128k',
        'sleep_time': 0
    },
    '微热点': {
        'prompt': prompt_tiyu_wtt,
        'chatid': '618252069',
        'chatcode': '3hsphf50xgvq4uzsjef',
        'bot': 'gpt4_o_mini',
        'sleep_time': 0
    },
    '微职场': {
        'prompt': prompt_tiyu_wtt,
        'chatid': '618252069',
        'chatcode': '3hsphf50xgvq4uzsjef',
        'bot': 'gpt4_o_mini',
        'sleep_time': 0
    },
    '科技': {
        'prompt': prompt_keji,
        'local_paths': ["/Users/<USER>/temp/科技闲述无图.pdf"],
        'chatid': '566347135',
        'chatcode': '3e2mhbkjk9zbci4l1ny',
        'sleep_time': 0
    },
    '育儿': {
        'prompt': prompt_yuer,
        'local_paths': ["/Users/<USER>/temp/三好老妈无图.pdf"],
        'chatid': '566544541',
        'chatcode': '3e3b1uqp2l477cxipvq',
        'sleep_time': 80
    },
    '创业': {
        'prompt': prompt_chuangye,
        'local_paths': ["/Users/<USER>/temp/大橘创业说无图.pdf"],
        'chatid': '566658711',
        'chatcode': '3e30jaoh8iin6h5sxub',
        'sleep_time': 0
    },
    '星座': {
        'prompt': prompt_xingzuo,
        'local_paths': ["/Users/<USER>/temp/可可小缘无图.pdf"],
        'chatid': '567604581',
        'chatcode': '3e5s0evgu81mxuqvrpe',
        'sleep_time': 80
    },
    '职场': {
        'prompt': prompt_zhichang,
        'local_paths': ["/Users/<USER>/temp/职场一只猹无图.pdf"],
        'chatid': '584544969',
        'chatcode': '3fwrwjimwhxrmpj9p99',
        'sleep_time': 80
    },
    '旅行': {
        'prompt': prompt_xingzuo,
        'local_paths': ["/Users/<USER>/temp/六六旅行官无图.pdf"],
        'chatid': '567644666',
        'chatcode': '3e5lxjdp7meyidr2h8v',
        'sleep_time': 0
    },
    '文案': {
        'prompt': prompt_wenan,
        'chatid': '593875732',
        'chatcode': '3h7wlslhu8gyzyvfbvx',
        'bot': 'gpt4_o',
        'sleep_time': 0
    },
    '热点': {
        'prompt': prompt_redian,
        'chatid': '759443004',
        'chatcode': '3r3kdcocmstmzkf3fjq',
        'bot': 'websearch',
        'sleep_time': 1,
        'author':'claude_3_igloo' #'claude_3_igloo'  pacarana
    }
}


import time
from functools import wraps
class TimeoutUtils:
    @staticmethod
    def timeout(timeout_seconds=600): #10分钟之后超时
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                except Exception as e:
                    raise e
                end_time = time.time()
                execution_time = end_time - start_time
                if execution_time > timeout_seconds:
                    print(f"函数 {func.__name__} 执行时间超过 {timeout_seconds} 秒")
                    return None
                return result
            return wrapper
        return decorator
    
@TimeoutUtils.timeout(timeout_seconds=600) #超过十分钟返回None
def call_content_common(query: str, origin_url: None, area: str):
    """调用poe接口发送"""
    try:
        config = area_config.get(area)
        if not config:
            print('不可识别领域')
            return None

        bot = config.get('bot', 'claude_2_1_cedar')  # 默认使用 Claude-3-Opus	 2000积分 很贵! 慎用!
        message = f"{config['prompt']}{query}"

        if area == '体育':
            message = f"{config['prompt']}{origin_url} \n\n文章标题是:{query}"
        elif area== '微体育':
            message = f"{config['prompt']}{origin_url}"

        content=call_content(message=message, chatid=config['chatid'],chatCode=config['chatcode'], bot=bot, local_paths=config.get('local_paths', []))
        if content: return content
        time.sleep(config['sleep_time'])
        return get_history(config['chatcode'], bot,config.get('author'))
    except Exception as e:
        print(f'poe-call_content_common-调用异常:{e}')
    return None

def get_history(chatcode: str, bot: str,author:None):
    """获取最近的历史消息"""
    previous_messages = client.get_previous_messages(bot, chatCode=chatcode, count=2)  # 可以
    for message in previous_messages:
        if bot == message['author'] or author==message['author']:
            print(message['text'])
            print(len(message['text']))
            return message['text']


def call_content(message: str, chatid: str, chatCode:str, bot: str, local_paths: list = []):
    """调用发送消息"""
    try:
        if len(local_paths)==0:
            for chunk in client.send_message(bot=bot, message=message,chatCode=chatCode):
                pass
            print(chunk["text"])
            return chunk["text"]
        else:
            # 2. Using chatId   可以现在网页调教好之后通过接口获取chatid   如果选择上传附件-会导致api超时,但是页面数据正在生成
            # for chunk in client.send_message(bot, message, chatId=chatid, file_path=local_paths):  # 3.5的id  552439046
            for chunk in client.send_message(bot, message, chatCode=chatCode, file_path=local_paths):  # 3.5的id  552439046
                print(chunk["response"], end="", flush=True)
        print(chunk["text"])
        chatCode = chunk["chatCode"]
        chatId = chunk["chatId"]
        title = chunk["title"]
        msgPrice = chunk["msgPrice"]
        print(f'chatCode:{chatCode},chatId:{chatId},title:{title},msgPrice:{msgPrice}')
        return chunk["text"]
    except Exception as e:
        print(f'POE-call_content-调用异常:{e}')


def call_content_no_file(message: str, chatid: str, bot: str):
    """没有附件调用发送消息"""
    try:
        # 2. Using chatId   可以现在网页调教好之后通过接口获取chatid   如果选择上传附件-会导致api超时,但是页面数据正在生成
        for chunk in client.send_message(bot, message, chatId=chatid):  # 3.5的id  552439046
            print(chunk["response"], end="", flush=True)
        print(chunk["text"])
        print(len(chunk["text"]))
        chatCode = chunk["chatCode"]
        chatId = chunk["chatId"]
        title = chunk["title"]
        msgPrice = chunk["msgPrice"]
        print(f'chatCode:{chatCode},chatId:{chatId},title:{title},msgPrice:{msgPrice}')
        return chunk["text"]
    except Exception as e:
        print(f'调用异常:{e}')


if __name__ == "__main__":
    user_content="""
    免责声明：图片与故事无关，图片来源于网络，如有冒犯，联系删除我家是农村的，爷爷奶奶当了一辈子农民。说起来他们既是幸运的，又是不幸的。幸运的是他们有3个儿子，不幸的是，这3个儿子没一个孝顺的。我爸他们哥仨，其实过得都挺好的，但是没一个管我爷奶的。二老有事了，去找老大，老大让去找老二，老二让去找老三，老三再让去找老大。就这样，什么事都指望不上他们。我爷爷当初病死在家里，就是因为这几个儿子，互相推脱谁都不管。爷爷走后，便留下了奶奶一个人生活。即使她岁数不小，行动不便了，依然没有一个儿子站出来说给我奶奶养老。我在县城里工作，我想把她接去跟我一起生活，她怕给我添麻烦，不同意。我只能每周有空就回去，给我奶奶买些吃的，带她去看病。但是，近两年，我奶奶身体越来越不好，医生也是没办法。说我奶奶岁数大了，就这样了，保养好了能多活几年，保养不好，就不好说了，让我做好心里准备。我奶奶好像也知道自己活不了几年了，开始给自己准备寿衣、寿材，时不时的还会交代我一些事情。我每次看我奶奶这样，我心里都难受的不行，我一个快四十的人，都忍不住掉眼泪。上个月末，我回去看我奶奶，吃完饭，我奶奶给了我一个布袋，里三层外三层包的很严。我问她这是什么，她说：“这里面是17万，是奶奶这辈子的积蓄，我感觉我快要不行了，这个钱就都给你吧。”“这个钱你谁都别告诉，要是被你爸他们知道了，这个钱到不了你手里。”我奶奶就像交代后事一样，断断续续地跟我说了一大堆话。临走的时候，我奶奶拉着我说了句：“唉，也不知道你下次回来，还能不能看到活着的奶奶。”我听完眼泪止不住地流，我说：“奶奶，别瞎说，你长命百岁，且活呢。”我奶奶笑了笑，催我赶快回去，要不天黑了。谁知，这一走，就是永别。果然被我奶奶说中，我走后第三天她就没了。那天，我心里总感觉很难受，我就给我奶奶打电话，想问问她这两天身体有没有不舒服。谁知我怎么打都没人接，我心里咯噔一下，生起了不好的预感。我匆忙开车去了我奶奶家，等我到了的时候，看到大门紧闭，我敲门也没人开。翻墙进去，趴在窗户上一看，我奶奶斜着躺在炕上一动不动。我拍窗户叫她，她也不答应。我一脚踹开门，进去一看，我奶奶已经走了。我跪在地上哭了半天，我心疼我奶奶，她这一辈子，含辛茹苦养育了三个儿子。临终前，她身边却一个人都没有。我强忍住心里的悲痛，打电话通知了家里的亲属。我奶奶丧事办的很是隆重，毕竟她有三个“孝子”，为了他们自己的面子，他们也不会简单地就把我奶奶给安葬了。我那两个婶子也是哭的昏天暗地，死去活来。我站在一旁，看到如此场景，感觉很是可笑。看到我奶奶棺材前的贡品，我不禁心里发酸，我奶奶生前，都没在她这几个儿子家吃过这么丰盛的饭菜。葬礼结束后，他们就开始清理我奶奶留下的遗物，想要看看我奶奶有没有留下什么值钱的东西，还有我奶奶存折放哪了。折腾了半天，翻出了一些零钱和一张空了的存折，上面打印着，我奶奶上个月在信用社取了几笔钱，加在一起总共17万。大家就在家里搜，想要看看我奶奶把钱藏哪了。结果家里找遍了也没有，我爸他们哥仨就开始互相怀疑，都认为是他们中的谁，偷着把钱拿走了。因为这事，这三家吵得不可开交。我在旁边看着，只感觉很可笑，自己老妈死活无人关心，因为一点钱却能大打出手。让他们打去吧，也算是给我奶奶出出气。要是哪天谁敢来问我这个事，我正好借这个理由，替我奶奶教训一下他们。点【关注】好文不迷路，点亮【大拇指+小心心】
    """
    area='情感'
    call_content_common(query=user_content,origin_url='www.baidu',area=area)