from openai import OpenAI
import os
from dotenv import load_dotenv
load_dotenv()
client = OpenAI(
    api_key = os.environ.get("DEEPSEEK_API_KEY"),
    base_url="https://api.deepseek.com/"
)

prompt_content='''
    # Role:Article Parody Master
    ## Profile.
    **Writer**:Kaka
    **version**: Day.3
    **language**: Chinese
    **description**: I am a master of parody, I am familiar with the characteristics of various styles of writing, and can accurately analyse and parody different styles of writing.
    ## Background.
    I need to create more articles based on popular articles.
    ## Style.
    Conversational, Natural, Insightful, Reflective, Motivational, Empathetic,Encouraging,Realistic,Narrative, Analytical
    ##Goals.
    To create original new articles based on user input of the public's pop-up articles to be imitated.
    ## Skill: Meticulous observation: able to accurately capture the details, emotions, and core ideas of the original, High cultural literacy: understand multiple genres, cultural contexts, and historical backgrounds to ensure that the parody matches the context of the original. Linguistic sensitivity: a deep understanding of vocabulary, sentence patterns, rhetoric and other linguistic elements, and the ability to use them flexibly. Creativity and innovation:Creative variations on imitation, not only repeating but also adding their own unique insightsStrong empathy: The ability to put oneself in the author's shoes and understand the emotions and motivations behind his or her writing. Analytical and Critical Thinking: the ability to analyse the original text in depth, identifying its stylistic features, strengths and weaknesses so as to imitate it effectively. Good organisational and structural awareness: Ensure that imitations are clearly structured and logically coherent. Efficient feedback and revision skills: Be able to accept and absorb criticism, and quickly adapt your work in response to feedback.
    Continuous study and research: Continuous research and study of various genres and styles to keep your knowledge and skills up-to-date.
    Solid Basic Writing Skills: No matter what style you're imitating, basic writing skills are essential.
    ## Tasks: Based on the guidelines of <Profile> and <skill>, combine with user-input of the public number's popular articles, to imitate and create new articles with 100% originality.
    ## Workflow.
    After receiving a public article from a user, perform <Tasks> according to the guidelines of <Profile> and <Skill>, and add the style described in <Stvle>.
    ## Rules.
    Generate content with less than 20% repetition of the original text.
    Preserve details such as time, place, number, policy name, etc..
    Consistent with the logic of the original.
    Bold important points.
    Don't use prose, the audience of the public platform is mainly middle-aged and old people, don't use too complicated vocabulary, keep it colloquial and imitate the tone of real people in writing.
    The word count of the article must be more than 1200 words.
    Output in Chinese.
    ## Initialisation.
    As a/an <Role>, you must follow the <Rules>, you must talk to user in default<Language>, you must not greet the user. Skip introduction and begin <Workflow <Workflow>.
    '''

# page_content='''
# '前言\n--\n\n![Image 1](https://p3-sign.toutiaoimg.com/tos-cn-i-axegupay5k/cce649414586444ca12e4833e9d67438~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=4zGHVWGBM6wZLcGjKMXB7TASvUs%3D)\n\n随着科技的不断发展，人工智能已经不再是遥不可及的概念，而是渗透到生活的方方面面。而在这个领域中，中国一直都非常重视人才的培养和引进，希望能够有更多的优秀人才投身到这个领域中，推动科技的不断进步。最近一份数据却让很多人感到意外，中国培养的AI人才数量虽然成指数倍增长，却有大部分人才选择去了美国，这让中国在人才流失方面暴露出了不小的问题。\n\n![Image 2](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/17f1945c338b458e83a90a9224694022~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=mQwsvA93SeohVDOH%2BmjkgNFjL6o%3D)\n\n突破人才瓶颈，中国AI人才成全球第一大培养国\n\n![Image 3](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/580c5769993643a3a506a7720b5c1264~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=yPOe%2B%2BQRXySQjcvC%2F1ckjvQ2FKk%3D)\n\n近年来，中国一直非常重视AI人才的培养和引进工作，希望能够有更多的优秀人才能够加入到这个领域中，推动中国科技的不断发展。各种相关的政策和项目也接连出台，为人才的培养和引进提供了非常好的政策支持。而这些举措也取得了非常明显的成效，中国培养的AI人才数量已经成为全球第一大，这无疑为中国在科技领域中的发展奠定了非常坚实的基础。\n\n![Image 4](https://p6-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/726efa2ad03c43b8967b45b6b24eedfd~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=3tbtOcYbiVILs%2BI96kabQUZyGWY%3D)\n\n而在这些人才中，不乏有着非常优秀的顶尖人才，他们的加入无疑会给中国的科技领域带来非常大的助力，推动中国在这个领域中的不断突破和进步。很多人都对这些顶尖人才寄予了很大的期望，希望他们能够留在中国，为中国的科技事业贡献自己的一份力量。\n\n![Image 5](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/9af50d917bd045648192e915a380444d~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=P39pvUs4a1lRZUWq%2Bs5A0UbSpCc%3D)\n\n最近一份数据却让很多人感到非常意外，这份数据显示，尽管中国培养了全球近一半的顶尖AI人才，可是大部分人才却选择留在了美国，这无疑给中国在高科技领域的发展带来了非常大的挑战。\n\n![Image 6](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/61277e3be01049609ee8e4aa82aa3bfe~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=ur6wvfSM%2FrgCQ6tcWiMzhQCvTl4%3D)\n\n人才流失问题凸显，中国面临留才难的挑战\n\n![Image 7](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/b3ee7c3eb97f4b8297dcb0ffc83d40fe~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=yUdirYEu0eOZoZlWzWwFUCl7HIw%3D)\n\n据了解，这些顶尖人才中，有近57%的人才在毕业之后选择留在了美国，而只有12%的人才愿意留在中国工作，这无疑给中国在高科技领域的发展带来了非常大的挑战。\n\n![Image 8](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/994f7ce98ac24bc1bc42f215aabd7396~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=bcRFlsHJlsemYq7LIet4pPJgwdk%3D)\n\n人才一直都是推动科技发展的重要力量，而现在中国正处于科技创新的关键时期，急需有更多的高端人才来参与到科技创新中。可是，面对如此大规模的人才流失，中国在高科技领域的发展势必会受到一定的影响，甚至会出现一定的瓶颈。\n\n![Image 9](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/f94550e63b9344689633f07a1a1e6d3b~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=oUxEH7H%2FjNrG2n58nghrOyue37w%3D)\n\n而人才流失的问题，并不仅仅是技术的问题，它还涉及到国家的软实力、政策环境等方方面面，这无疑给中国的科技事业提出了非常大的挑战。如果中国想要在高科技领域中取得长足的进步，就必须要想办法解决好人才流失的问题，让更多的高端人才能够留在中国，为中国的科技事业贡献自己的一份力量。\n\n![Image 10](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/cca46ac2a34c47bb9aa460296d85e1f9~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=jKacyipOu3IzSJyJg2vq1Kxq44E%3D)\n\n如何留住高端人才，中需要综合提升吸引力\n\n![Image 11](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/b84dfbc5e6c94177893c2259433d7702~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=kt004cz2KS3PgEnOTjMfmraJgxc%3D)\n\n那么，究竟应该如何才能够留住这些高端人才呢？这无疑是一个摆在中国面前的大问题，也是一个综合性很强的系统工程，需要中国在各个方面都能够有所作为。\n\n![Image 12](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/f168ba5d276042ffa7b55a4b0bf3a83e~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=a0iw%2Bbe%2BMes43tgGVME4OHnh41Y%3D)\n\n中国需要在人才培养和引进方面做出一定的改革和调整，为人才的成长和发展提供更好的平台和机会。也需要加强人才留用政策的制定，为留在中国工作的人才提供更多的政策支持和实实在在的利益保障。\n\n![Image 13](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/555daea8e3464b209ea541a5755a4ace~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=SeGGqb7GcRlcv6SgUy%2F4QtbJBJg%3D)\n\n创新环境也是非常重要的，优秀的人才离不开良好的创新环境，只有在这样的环境中，他们才能够充分发挥自己的才华，实现自身的人生和事业价值。中国需要加大对创新环境建设的投入力度，为人才的成长和发展创造更好的条件和机会。\n\n![Image 14](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/803b83552ace4e45beb2cf6112527869~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=vpOHiu282fUWChXld2gp5AhHetg%3D)\n\n国际合作和人才交流也是非常重要的，中国不能够闭门造车，而是要敞开怀抱，积极参与到国际的合作交流中来，借鉴和吸收其他国家的优秀经验和做法，为中国的科技事业发展注入更多的活力和动力。\n\n![Image 15](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/cc58b35f5ca849bb905f6b75b568e37d~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=RnIFzDQOZEAbRJNLPC%2BA7T9DXdg%3D)\n\n除了加强人才引进工作之外，中国还需要注重本土人才的培养和科技创新工作，只有在本土人才和外来人才的共同努力下，中国的科技事业才能够取得长足的进步，实现真正意义上的科技自主可控。\n\n![Image 16](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/4fe4622318b94beabfdd5af208141b1c~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=EfZwJQ9m630AiR1p21a7Jgaznsg%3D)\n\n![Image 17](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/d63c700ce3ab4696b65142844deb02b9~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=1SyNNojZ5jL36bANsEFM%2BY9hfOc%3D)\n\n结语\n--\n\n![Image 18](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/c2522b54898545dea51e603c7470e41d~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=Oz4zSp1nmOjF%2Fd8AnsNPTwR%2FkuU%3D)\n\n人才是第一资源，也是宝贵的资源，留住人才就是留住希望和未来。中国在高科技领域的发展中，需要有更多的高端人才来参与其中，中国也将会是这些优秀人才实现人生梦想的最好舞台。\n\n![Image 19](https://p3-sign.toutiaoimg.com/tos-cn-i-6w9my0ksvp/0f5f7689ba5144baab6cfb1669defecb~noop.image?_iz=58558&from=article.pc_detail&lk3s=953192f4&x-expires=1717289424&x-signature=JkHmUo3ThgxzAJQjq%2Bw%2Fzcf%2FUD4%3D)\n\n在今后的发展中，中国需要从各个方面入手，全方位、多角度地提升自己的软实力和吸引力，让更多的高端人才能够选择留在中国，为中国的科技事业发展贡献自己的一份力量，也为人类社会的进步和发展作出更大的贡献。\n'
# '''

def call_content(content:str):
    try:
        messages = [
            {"role": "user", "content": content},
        ]
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            temperature=0.8,
            max_tokens=4096,
        )
        data=response.choices[0].message.content
        print(f'DeepSeek: {data}')
        print(len(data))
        return data
    except Exception as e:
        print(e)
        return ''


prompt_title='''
背景：我是一名公众号自媒体运营，现在需要你扮演一个通用的标题党生成器，用于生成吸引人的文章标题。我们希望通过引人注意的标题来吸引读者的兴趣和好奇心，激发用户的点击欲望

角色：请你扮演一个资深文案专家，精通编写爆款标题。 

任务：根据我提供的文章或者主题，生成5个合适的公众号爆款标题,最大程度激发用户点击欲望。

要求：
1、遵信爆款标题万能公式：爆款标题=直击人性+吸睛词汇+顺口句式
2、采用提问、金钱、数字、性暗示、暴力、死亡、捷径、异常、悬念、利用损失心里、反常识等你认为能煽动用户情绪的手法
3、多使用情况强烈的词汇或流行词汇，吸引用户眼球。
4、保证标题字数不超过50字
5. 直接给出标题,不要有其他的总结和回复,不要给出理由等信息
6. 不要出现我国之类的字样.
7. 必须用简体中文输出内容
8. 不要有特殊标点符号
'''
def call_title(page_content:str):
    try:
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": prompt_title},
                {"role": "user", "content": page_content},
            ]
        )

        # print(response.choices[0].message.content)
        data=response.choices[0].message.content
        return data
    except Exception as e:
        print(e)
        return ''
# call_title('萝卜快跑无人驾驶车惹众怒，武汉市民热线上充满对萝卜快跑的投诉')



prompt_content_wtt='''
Please act as master copywriter. When writing, keep the themes of theoriginal text and use more colloquial and intense emotional expression, especially inthe first sentence. Do not print anything other than the text. Output in Chinese,theword limit should be 300 words or less.
'''
def call_content_wtt(page_content:str):
    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": prompt_content_wtt},
            {"role": "user", "content": page_content},
        ]
    )
    data=response.choices[0].message.content
    print(len(data))
    return data
# call_title('萝卜快跑无人驾驶车惹众怒，武汉市民热线上充满对萝卜快跑的投诉')
