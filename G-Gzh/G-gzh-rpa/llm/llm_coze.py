import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

# 通用调用,需要bot_id
def call_content_common(query:str,area:str):
    if area == '体育':
        return call_content(query=query,Bot_Id='7376939738072399888')
    elif area == '科技':
        return call_content(query=query,Bot_Id='7376939738072399888')
    elif area == '娱乐':
        return call_content(query=query,Bot_Id='7381398316795658257')#娱乐
    elif area == '社会':
        return call_content(query=query,Bot_Id='7381014964871495697')#社会法律故事
    elif area == '情感':
        return call_content(query=query,Bot_Id='7381368391229423634')
    elif area == '美女':
        return call_content(query=query,Bot_Id='7382407729308712966')
    elif area == 'NBA':
        return call_content(query=query,Bot_Id='7382761498097057797')#公众号-NBA新闻
    elif area == '文案':
        return call_content(query=query,Bot_Id='7382961117972791303')#公众号-文案
    else:
        return None

def call_content(query:str,Bot_Id:str):
    api_key=os.environ.get("COZE_API_KEY")
    url = 'https://api.coze.com/open_api/v2/chat'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'api.coze.com',
        'Connection': 'keep-alive'
    }
    data = {
        "conversation_id": "123",
        "bot_id": Bot_Id,
        "user": "29032201862555",
        "query": query,
        "stream": False
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        # 打印响应内容
        data=response.json()
        if data.get('messages') is None:
            print(data['msg'])
            return
        else:
            for i in data['messages']:
                if i['type']=='answer':
                    content=i['content']
                    print(content)
                    print(len(content))
                    return content
    else:
        print(f'Coze异常:{response.text}')
        return None
