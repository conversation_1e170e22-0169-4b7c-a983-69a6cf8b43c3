import requests
from typing import List, Optional
from flask import Flask, request, jsonify
from dotenv import load_dotenv
import os
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

def get_access_token():
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None

def send_mass_message(media_id: str, is_to_all: bool = False, tag_id: Optional[int] = None) -> dict:
    """
    Send a mass message to users based on tag or to all users.
    
    Args:
        media_id (str): The media_id of the mpnews content to be sent.
        is_to_all (bool): Whether to send to all users.
        tag_id (Optional[int]): The tag ID to filter users.
    
    Returns:
        dict: The response from the WeChat API.
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/message/mass/sendall?access_token={access_token}"
    
    data = {
        "filter": {
            "is_to_all": is_to_all,
            "tag_id": tag_id
        },
        "mpnews": {
            "media_id": media_id
        },
        "msgtype": "mpnews",
        "send_ignore_reprint": 0
    }
    
    response = requests.post(url, json=data)
    return response.json()

def delete_mass_message(msg_id: int, article_idx: Optional[int] = None) -> dict:
    """
    Delete a sent mass message.
    
    Args:
        msg_id (int): The ID of the message to delete.
        article_idx (Optional[int]): The index of the article to delete (if applicable).
    
    Returns:
        dict: The response from the WeChat API.
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/message/mass/delete?access_token={access_token}"
    
    data = {
        "msg_id": msg_id,
        "article_idx": article_idx
    }
    
    response = requests.post(url, json=data)
    return response.json()

def preview_mass_message(openid: str, media_id: str) -> dict:
    """
    Preview a mass message for a specific user.
    
    Args:
        openid (str): The OpenID of the user to send the preview to.
        media_id (str): The media_id of the mpnews content to be previewed.
    
    Returns:
        dict: The response from the WeChat API.
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/message/mass/preview?access_token={access_token}"
    
    data = {
        "touser": openid,
        "mpnews": {
            "media_id": media_id
        },
        "msgtype": "mpnews"
    }
    
    response = requests.post(url, json=data)
    return response.json()

def get_mass_message_status(msg_id: str) -> dict:
    """
    Get the status of a sent mass message.
    
    Args:
        msg_id (str): The ID of the message to check.
    
    Returns:
        dict: The response from the WeChat API.
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/message/mass/get?access_token={access_token}"
    
    data = {
        "msg_id": msg_id
    }
    
    response = requests.post(url, json=data)
    return response.json()

def get_mass_speed() -> dict:
    """
    Get the current mass messaging speed.
    
    Returns:
        dict: The response from the WeChat API containing speed information.
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/message/mass/speed/get?access_token={access_token}"
    
    response = requests.post(url)
    return response.json()

# Example usage:
if __name__ == "__main__":
    # Send mass message
    # media_id = "33OzyyRijcONGGaikCTOZAgOgMpjR8zzvYRgjEKv8vc-GoCAcQvk9rIyrCMEfdgg" #[痞浩研究所]
    media_id = "jGxKdzk8i7zSPHlKcMC98ORw_WW2bChfhGPNfxMe56qkKQ2qsjDkCzxgGDdnk3iV" # 文浩marvin
    
    # result = send_mass_message(media_id, is_to_all=False, tag_id=2)
    # result = send_mass_message(media_id, is_to_all=False, tag_id=None)
    # print("Send mass message result:", result)
    
    # # Delete mass message
    # msg_id = 30124
    # delete_result = delete_mass_message(msg_id)
    # print("Delete mass message result:", delete_result)
    
    # Preview mass message
    openid = "oQ1vp6ABwDwAYURubQGRLMt6FL-c" #文浩
    preview_result = preview_mass_message(openid, media_id)
    print("Preview mass message result:", preview_result)
    
    # # Get mass message status
    # status_result = get_mass_message_status("201053012")
    # print("Mass message status:", status_result)
    
    # # Get mass speed
    # speed_result = get_mass_speed()
    # print("Mass speed:", speed_result)
