from flask import Flask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
from dotenv import load_dotenv
import re
import json
from bs4 import BeautifulSoup

app = Flask(__name__)
# from flask import Blueprint
# app = Blueprint('gzh_api_article_publish', __name__)

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

def get_access_token():
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None

@app.route('/api/upload_cover_image', methods=['POST'])
def upload_cover_image():
    """01-上传封面图片"""
    if 'media' not in request.files:
        return jsonify({"error": "No file part"}), 400
    file = request.files['media']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
    
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image"
    
    try:
        files = {'media': (file.filename, file.read(), file.content_type)}
        response = requests.post(url, files=files)
        response.raise_for_status()
        result = response.json()
        if 'media_id' in result:
            return jsonify({"message": "Cover image uploaded successfully", "thumb_media_id": result['media_id']}), 201
        else:
            return jsonify({"error": f"Failed to upload cover image: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/upload_article_image', methods=['POST'])
def upload_article_image():
    """02-上传文章图片"""
    if 'media' not in request.files:
        return jsonify({"error": "No file part"}), 400
    file = request.files['media']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
    
    # 检查文件类型和大小
    if file.content_type not in ['image/jpeg', 'image/png']:
        return jsonify({"error": "Only JPG/PNG images are allowed"}), 400
    if file.content_length > 1024 * 1024:  # 1MB
        return jsonify({"error": "Image size must be less than 1MB"}), 400
    
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token={access_token}"
    
    try:
        files = {'media': (file.filename, file.read(), file.content_type)}
        response = requests.post(url, files=files)
        response.raise_for_status()
        result = response.json()
        if 'url' in result:
            return jsonify({"message": "Article image uploaded successfully", "url": result['url']}), 201
        else:
            return jsonify({"error": f"Failed to upload article image: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/create_draft', methods=['POST'])
def create_draft():
    """03-创建草稿内容（支持多篇文章）"""
    data = request.json
    articles = data.get('articles')

    if not articles or not isinstance(articles, list):
        return jsonify({"error": "articles must be a non-empty list"}), 400

    processed_articles = []

    for article in articles:
        title = article.get('title')
        author = article.get('author', '')
        content = article.get('content')
        content_source_url = article.get('content_source_url', '')
        thumb_media_id = article.get('thumb_media_id')
        digest = article.get('digest')

        # 验证必要参数
        if not all([title, content, thumb_media_id]):
            return jsonify({"error": f"title, content, and thumb_media_id are required for all articles"}), 400

        # 验证内容长度和大小
        if len(content.encode('utf-8')) > 20000:
            return jsonify({"error": f"Content for '{title}' must be less than 20,000 bytes"}), 400
        if len(content.encode('utf-8')) > 1000000:
            return jsonify({"error": f"Content for '{title}' must be less than 1M"}), 400

        # 移除 JavaScript
        soup = BeautifulSoup(content, 'html.parser')
        for script in soup(["script", "style"]):
            script.decompose()
        content = str(soup)

        # 验证图片 URL
        img_pattern = re.compile(r'<img[^>]+src=["\'](.*?)["\']', re.IGNORECASE)
        img_urls = img_pattern.findall(content)
        for url in img_urls:
            if not url.startswith('http://mmbiz.qpic.cn/') and not url.startswith('https://mmbiz.qpic.cn/'):
                return jsonify({"error": f"Invalid image URL in '{title}': {url}. All image URLs must be from the WeChat server."}), 400

        # 处理摘要
        if not digest:
            text_content = soup.get_text()
            digest = text_content[:120]
        else:
            digest = digest[:120]

        processed_articles.append({
            "title": title,
            "author": author,
            "digest": digest,
            "content": content,
            "content_source_url": content_source_url,
            "thumb_media_id": thumb_media_id,
            "need_open_comment": 0,
            "only_fans_can_comment": 0,
            "recommend": 1 #// 设置为1表示推荐，0表示不推荐

        })

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}"
    
    payload = {
        "articles": processed_articles
    }
    
    headers = {
        'Content-Type': 'application/json; charset=utf-8'
    }
    
    try:
        response = requests.post(
            url=url,
            headers=headers,
            data=json.dumps(payload, ensure_ascii=False).encode('utf-8')
        )
        response.raise_for_status()
        result = response.json()
        if 'media_id' in result:
            return jsonify({"message": "Draft(s) created successfully", "media_id": result['media_id']}), 201
        else:
            return jsonify({"error": f"Failed to create draft(s): {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500
    


@app.route('/api/update_draft', methods=['POST'])
def update_draft():
    """03.5-修改草稿添加多篇"""
    data = request.json
    media_id = data.get('media_id')
    articles = data.get('articles')
    
    if not media_id or not articles or len(articles) != 3:
        return jsonify({"error": "media_id and exactly 3 articles are required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/update?access_token={access_token}"
    
    success_count = 0
    errors = []

    for index, article in enumerate(articles):
        payload = {
            "media_id": media_id,
            "index": index,
            "articles": [article]
        }
        
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        try:
            response = requests.post(
                url=url,
                headers=headers,
                data=json.dumps(payload, ensure_ascii=False).encode('utf-8')
            )
            response.raise_for_status()
            result = response.json()
            if result.get('errcode') == 0:
                success_count += 1
            else:
                errors.append(f"Failed to update article {index + 1}: {result.get('errmsg', 'Unknown error')}")
        except requests.RequestException as e:
            errors.append(f"Request failed for article {index + 1}: {str(e)}")

    if success_count == 3:
        return jsonify({"message": "All 3 articles updated successfully"}), 200
    elif success_count > 0:
        return jsonify({"message": f"{success_count} articles updated successfully", "errors": errors}), 207
    else:
        return jsonify({"error": "Failed to update all articles", "errors": errors}), 400

@app.route('/api/publish', methods=['POST'])
def publish_article():
    """04-发布草稿文章"""
    data = request.json
    media_id = data.get('media_id')
    is_to_all = data.get('is_to_all', True)  # Default to True if not provided
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={access_token}"
    
    payload = {
        "media_id": media_id,
        "is_to_all": is_to_all
    }
    
    headers = {
        'Content-Type': 'application/json; charset=utf-8'
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        result = response.json()
        if 'publish_id' in result:
            return jsonify({"message": "Article submitted for publishing successfully", "publish_id": result['publish_id']}), 200
        else:
            return jsonify({"error": f"Failed to publish article: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/publish_status', methods=['GET'])
def get_publish_status():
    """
    05-获取发布状态接口
    接收 publish_id 参数，查询文章的发布状态
    """
    publish_id = request.args.get('publish_id')
    
    if not publish_id:
        return jsonify({"error": "publish_id 是必需的"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "获取 access token 失败"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/get?access_token={access_token}"
    
    payload = {
        "publish_id": publish_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        return jsonify({"message": "成功获取发布状态", "status": result}), 200
    except requests.RequestException as e:
        return jsonify({"error": f"请求失败: {str(e)}"}), 500


@app.route('/api/delete_publish', methods=['POST'])
def delete_publish():
    """删除已发布的文章"""
    data = request.json
    article_id = data.get('article_id')
    index = data.get('index', 1)
    
    if not article_id or not index:
        return jsonify({"error": "article_id and index are required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/delete?access_token={access_token}"
    data = {
        "article_id": article_id,
        "index": index
    }
    
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()
        result = response.json()
        if result.get("errcode") != 0:
            raise Exception(f"删除文章失败: {result.get('errmsg')}")
        return jsonify({"message": "Article deleted successfully"}), 200
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

if __name__ == '__main__':
    # Enable debug mode for development
    app.debug = True
    # Specify host and port
    # app.run(host='0.0.0.0', port=5001)
    app.run()