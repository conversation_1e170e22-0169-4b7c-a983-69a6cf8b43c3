import unittest
from unittest.mock import patch, MagicMock
from gzh_api_draft import app, delete_all_drafts
import json
import gzh_api_publish
def run():
    """清风侃侃账号清洗
    1. 删除所有发布的内容
    2. 优化文章生成-专注热点时事新闻
    3. 图文生成
    4. 生成草稿
    5. 校验文章排版格式

    执行说明:
    1. env环境配置API-key
    2. 
    """

    # 1. 删除发布内容- 每日限制调用十次- 内容倒序删除
    gzh_api_publish.delete_all_published()
    
    # 2. 生成文章-POE-数据源(手动输入-Notion筛选)
    contentStr="农民副县长被抓 老百姓放鞭炮庆祝"
    
    
    

    # 3. 检索图片

    # 4. 生成HTML

    # 5. 发布草稿

    # 6. 人工核对
    
"""全局配置"""
client=gzh_api.WECHATCLIENT(JIAOYU_HAOMO_WECHAT_APP_ID,JIAOYU_HAOMO_WECHAT_APP_SECRET)


if __name__ == '__main__':
    print("开始执行...")
    run()