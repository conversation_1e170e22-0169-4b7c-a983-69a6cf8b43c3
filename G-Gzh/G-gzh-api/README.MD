# 自动化逻辑:

1. AI生成对应领域内容
2. 内容保存草稿
3. 内容直接发布
4. 内容通知用户(微信认证才能调用)
5. 可以RPA操作发布草稿内容,会直接通知发布.

## 问题: 
1. 账号登录失效:
    - 每两天就需要登录一次微信
2. IP的问题:
    - api操作需要app_secret还有IP白名单
    - 相同的IP 会影响发布内容曝光情况
3. API发布:
    - API发布的内容,不会再手机端的 账号列表下显示内容,  可以再电脑端账号下看到
    - 页面点击发表就可以显示在手机端
    - 需要影刀RPA来实现发表.


## 相关文档:
- coze的自动化:
    - https://v0bko2tq65.feishu.cn/docx/FhA7d1HqYo2SqPxgz9KcLZ7un6c?PgZro4=1


- 微信开发文档:
    - https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Batch_Sends_and_Originality_Checks.html#%E5%9B%BE%E6%96%87%E6%B6%88%E6%81%AF%E7%BE%A4%E5%8F%91%E5%89%8D%E5%B0%86%E8%BF%9B%E8%A1%8C%E5%8E%9F%E5%88%9B%E6%A0%A1%E9%AA%8C

- 群发和发布去呗:
    - https://felo.ai/search/crdquNwAKXswBqCN9RDZGU
    - https://felo.ai/search/crdquNwAKXswBqCN9RDZGU
    - 发布不能被收录搜一搜




# 开发: 微信公众号api操作接口

## 准备条件:
1. 获取app_id和app_scret
2. 将本地调用的IP添加到白名单中
3. 上面操作都需要管理员账号扫码

## QA:
1. 中文内容限制长度问题
    - 编码设置正确,data入参json格式utf8
2. 创建草稿索引的问题
    - 入参articles设置多篇入参
3. 如何发布时候带上群发通知
4. 如何定时发布?


## 接口:
### 草稿接口

### 素材接口

### 发布逻辑:
1. 封面图
2. 内容图
3. 创建草稿
4. 草稿内容发布