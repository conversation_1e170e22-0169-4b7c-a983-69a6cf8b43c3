import requests
from typing import List, Optional
from flask import Flask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
from dotenv import load_dotenv

app = Flask(__name__)

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

def get_access_token():
    """
    获取微信接口调用凭证
    """
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None

def batch_tag_users(openid_list: List[str], tag_id: int) -> dict:
    """
    批量为用户打标签

    Args:
        openid_list (List[str]): 用户OpenID列表
        tag_id (int): 标签ID

    Returns:
        dict: 接口返回的JSON数据
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/tags/members/batchtagging?access_token={access_token}"
    data = {
        "openid_list": openid_list,
        "tagid": tag_id
    }
    
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"网络错误: {e}")
        return {"error": "网络错误"}
    except ValueError as e:
        print(f"JSON解码错误: {e}")
        return {"error": "无效的JSON响应"}

def get_user_list(next_openid: Optional[str] = None) -> dict:
    """
    获取关注者列表

    Args:
        next_openid (str, optional): 上一个拉取的OPENID，不填默认从头开始拉取

    Returns:
        dict: 包含用户列表信息的字典
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/user/get?access_token={access_token}"
    
    if next_openid:
        url += f"&next_openid={next_openid}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"网络错误: {e}")
        return {"error": "网络错误"}
    except ValueError as e:
        print(f"JSON解码错误: {e}")
        return {"error": "无效的JSON响应"}

def get_all_users() -> List[str]:
    """
    获取所有关注者的OpenID

    Returns:
        List[str]: 所有用户的OpenID列表
    """
    all_users = []
    next_openid = None
    
    while True:
        result = get_user_list(next_openid)
        
        if "error" in result:
            print(f"获取用户时发生错误: {result['error']}")
            break
        
        if "data" in result and "openid" in result["data"]:
            all_users.extend(result["data"]["openid"])
        
        if "next_openid" in result and result["next_openid"]:
            next_openid = result["next_openid"]
        else:
            break  # 没有更多用户了
    
    return all_users

def get_user_info(openid: str, lang: str = "zh_CN") -> dict:
    """
    获取用户基本信息

    Args:
        openid (str): 用户的OpenID
        lang (str, optional): 返回国家地区语言版本，默认为简体中文

    Returns:
        dict: 包含用户信息的字典
    """
    access_token = get_access_token()
    url = f"https://api.weixin.qq.com/cgi-bin/user/info?access_token={access_token}&openid={openid}&lang={lang}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"网络错误: {e}")
        return {"error": "网络错误"}
    except ValueError as e:
        print(f"JSON解码错误: {e}")
        return {"error": "无效的JSON响应"}

def find_users_by_nickname(keyword: str) -> List[dict]:
    """
    遍历所有用户，查找昵称中包含指定关键词的用户

    Args:
        keyword (str): 要搜索的昵称关键词

    Returns:
        List[dict]: 包含匹配用户信息的列表
    """
    matching_users = []
    all_openids = get_all_users()
    
    for openid in all_openids:
        user_info = get_user_info(openid)
        print(user_info)
        if "remark" in user_info and keyword in user_info["remark"]:
            matching_users.append(user_info)
    
    return matching_users

# 测试方法
def test_batch_tag_users():
    """测试批量为用户打标签"""
    openid_list = ["user1_openid", "user2_openid"]
    tag_id = 100
    result = batch_tag_users(openid_list, tag_id)
    print("批量打标签结果:", result)

def test_get_user_list():
    """测试获取用户列表"""
    result = get_user_list()
    print("用户列表:", result)

def test_get_all_users():
    """测试获取所有用户"""
    users = get_all_users()
    print(f"总用户数: {len(users)}")
    print("前10个用户:", users[:10])

def test_get_user_info():
    """测试获取用户信息"""
    openid = "oQ1vp6CHXwOeNDoiUHLyqzvjgLkI"
    user_info = get_user_info(openid)
    print("用户信息:", user_info)

def test_find_users_by_nickname():
    """测试查找昵称中包含特定关键词的用户"""
    keyword = "文浩Marvin"
    matching_users = find_users_by_nickname(keyword)
    print(f"找到 {len(matching_users)} 个昵称中包含 '{keyword}' 的用户:")
    for user in matching_users:
        print(f"OpenID: {user['openid']}, 昵称: {user['nickname']}")

if __name__ == '__main__':
    print("开始测试...")
    # test_batch_tag_users()
    # test_get_user_list()
    # test_get_all_users()
    # test_get_user_info()
    # test_find_users_by_nickname()
    # 文浩-oQ1vp6ABwDwAYURubQGRLMt6FL-c
    print("测试完成。")