from flask import Flask, request, jsonify
import requests
from dotenv import load_dotenv
import os
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException

app = Flask(__name__)

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

def get_access_token():
    """
    获取微信接口调用凭证
    """
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None

def decode_unicode_escape(s):
    """
    解码 Unicode 转义序列
    """
    try:
        # 如果是字节字符串，先尝试 UTF-8 解码
        if isinstance(s, bytes):
            return s.decode('utf-8')
        
        # 如果是已经解码的字符串，但包含转义序列
        if isinstance(s, str):
            # 先编码为 bytes，再用 utf-8 解码
            return s.encode('raw_unicode_escape').decode('utf-8')
            
        return s
    except Exception as e:
        print(f"解码失败: {e}, 原始字符串: {repr(s)}")
        return '未知标题'

@app.route('/api/publish', methods=['POST'])
def publish_article():
    """
    发布文章接口
    接收 media_id 参数，提交文章进行发布
    """
    data = request.json
    media_id = data.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id 是必需的"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "获取 access token 失败"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'publish_id' in result:
            return jsonify({"message": "文章已提交发布", "publish_id": result['publish_id']}), 200
        else:
            return jsonify({"error": f"提交文章失败: {result.get('errmsg', '未知错误')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"请求失败: {str(e)}"}), 500

@app.route('/api/publish_status', methods=['GET'])
def get_publish_status():
    """
    获取发布状态接口
    接收 publish_id 参数，查询文章的发布状态
    """
    publish_id = request.args.get('publish_id')
    
    if not publish_id:
        return jsonify({"error": "publish_id 是必需的"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "获取 access token 失败"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/get?access_token={access_token}"
    
    payload = {
        "publish_id": publish_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        return jsonify({"message": "成功获取发布状态", "status": result}), 200
    except requests.RequestException as e:
        return jsonify({"error": f"请求失败: {str(e)}"}), 500

@app.route('/api/delete_publish', methods=['POST'])
def delete_publish():
    """
    删除已发布文章接口
    接收 article_id 参数，删除指定的已发布文章
    """
    data = request.json
    article_id = data.get('article_id')
    
    if not article_id:
        return jsonify({"error": "article_id 是必需的"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "获取 access token 失败"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/delete?access_token={access_token}"
    
    payload = {
        "article_id": article_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if result.get('errcode') == 0:
            return jsonify({"message": "已发布文章删除成功"}), 200
        else:
            return jsonify({"error": f"删除已发布文章失败: {result.get('errmsg', '未知错误')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"请求失败: {str(e)}"}), 500

@app.route('/api/get_article', methods=['GET'])
def get_article():
    """
    获取文章详情接口
    接收 article_id 参数，获取指定文章的详细信息
    """
    article_id = request.args.get('article_id')
    
    if not article_id:
        return jsonify({"error": "article_id 是必需的"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "获取 access token 失败"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/getarticle?access_token={access_token}"
    
    payload = {
        "article_id": article_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        return jsonify({"message": "成功获取文章详情", "article": result}), 200
    except requests.RequestException as e:
        return jsonify({"error": f"请求失败: {str(e)}"}), 500

@app.route('/api/get_published_list', methods=['GET'])
def get_published_list():
    """
    获取已发布文章列表接口
    接收 offset, count, no_content 参数，获取已发布的文章列表
    """
    offset = int(request.args.get('offset', 0))
    count = int(request.args.get('count', 20))
    no_content = int(request.args.get('no_content', 1))

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "获取 access token 失败"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token={access_token}"
    
    payload = {
        "offset": offset,
        "count": count,
        "no_content": no_content
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        return jsonify({"message": "成功获取已发布文章列表", "list": result}), 200
    except requests.RequestException as e:
        return jsonify({"error": f"请求失败: {str(e)}"}), 500

def get_published_list_api():
    """
    删除所有已发布文章的函数
    遍历所有已发布文章并逐个删除
    """
    access_token = get_access_token()
    if not access_token:
        print("获取 access token 失败")
        return

    offset = 0
    count = 20
    
    while True:
        url = f"https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token={access_token}"
        payload = {
            "offset": offset,
            "count": count,
            "no_content": 1
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'item' not in result or not result['item']:
                break
            
            for item in result['item']:
                # 获取并解码标题
                article_title = item.get('content', {}).get('news_item', [{}])[0].get('title', '未知标题')
                article_title = decode_unicode_escape(article_title)
                article_url = item.get('content', {}).get('news_item', [{}])[0].get('url', '未知url')
                print(f"文章：「{article_title}」(article_id: {item['article_id']}) (article_url: {article_url})")            
            if len(result['item']) < count:
                break
            
            offset += count
        
        except requests.RequestException as e:
            print(f"请求失败: {str(e)}")
            break
    
    print("所有已发布文章处理完毕")


def delete_all_published():
    """
    删除所有已发布文章的函数
    遍历所有已发布文章并逐个删除
    """
    access_token = get_access_token()
    if not access_token:
        print("获取 access token 失败")
        return

    offset = 0
    count = 20
    
    while True:
        url = f"https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token={access_token}"
        payload = {
            "offset": offset,
            "count": count,
            "no_content": 1
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'item' not in result or not result['item']:
                break
            
            for item in result['item']:
                # 获取并解码标题
                article_title = item.get('content', {}).get('news_item', [{}])[0].get('title', '未知标题')
                article_title = decode_unicode_escape(article_title)
                
                delete_url = f"https://api.weixin.qq.com/cgi-bin/freepublish/delete?access_token={access_token}"
                delete_payload = {"article_id": item['article_id']}
                delete_response = requests.post(delete_url, json=delete_payload)
                delete_response.raise_for_status()
                delete_result = delete_response.json()
                if delete_result.get('errcode') == 0:
                    print(f"已删除文章：「{article_title}」(article_id: {item['article_id']})")
                else:
                    print(f"删除文章失败：「{article_title}」(article_id: {item['article_id']})")
                    print(delete_response.text)
            
            if len(result['item']) < count:
                break
            
            offset += count
        
        except requests.RequestException as e:
            print(f"请求失败: {str(e)}")
            break
    
    print("所有已发布文章处理完毕")

if __name__ == '__main__':
    # app.run(debug=True)
    # app.run(host='0.0.0.0', port=5001)
    # delete_all_published()
    get_published_list_api()

