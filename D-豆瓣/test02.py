import requests
from bs4 import BeautifulSoup

# Replace 'your_url' with the actual URL of the Douban Group page-娶不到数据

url = 'https://www.douban.com/group/beijingzufang/discussion?start=25&type=new'
response = requests.get(url)
soup = BeautifulSoup(response.content, 'html.parser')

# Replace 'title_class', 'author_class', 'date_class' with the actual classes or identifiers
for post in soup.find_all('div', class_='post_class'):
    title = post.find('div', class_='title_class').get_text()
    author = post.find('span', class_='author_class').get_text()
    date = post.find('span', class_='date_class').get_text()
    print(f"Title: {title}, Author: {author}, Date: {date}")
