import requests
from bs4 import BeautifulSoup

def fetch_data(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    rows = soup.find_all('tr')

    for row in rows:
        title = row.find('a', {'class': ''})['title']
        author = row.find('a', {'class': ''}).text
        time = row.find('td', {'class': 'time'}).text
        print(f"标题: {title}")
        print(f"作者: {author}")
        print(f"时间: {time}")
        print("------")
        
    # rows = soup.find_all('tr')
    # data = []
    # for row in rows:
    #     title = row.find('a').text
    #     author = row.find('td', {'nowrap': 'nowrap'}).text
    #     time = row.find('td', {'class': 'time'}).text
    #     data.append((title, author, time))
    # return data

start = 0
while True:
    url = f"https://www.douban.com/group/beijingzufang/discussion?start={start}&type=new"
    data = fetch_data(url)
    for title, author, time in data:
        print(f"标题：{title}, 发布人：{author}, 发布时间：{time}")
    start += 25  # 豆瓣每页显示25条数据