import requests
from bs4 import BeautifulSoup

base_url = "https://book.douban.com/top250"
headers = {"User-Agent": "Mozilla/5.0"}

def get_books_on_page(url):
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'lxml')

    for book in soup.find_all('div', class_='pl2'):
        book_name = book.find('a')['title']
        print(book_name)

    next_page = soup.find('span', class_='next')
    if next_page and next_page.find('a'):
        next_url = next_page.find('a')['href']
        get_books_on_page(next_url)

get_books_on_page(base_url)