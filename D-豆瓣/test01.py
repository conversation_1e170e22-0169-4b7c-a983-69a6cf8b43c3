from bs4 import BeautifulSoup

html_doc = """
<tr class="">
    <td class="title">
        <a href="https://www.douban.com/group/topic/299328322/" title="求租，北语附近12月底" class="">
           求租，北语附近12月底
        </a>
    </td>
    <td nowrap="nowrap">
        <a href="https://www.douban.com/people/252066423/" class="">22</a>
    </td>
    <td nowrap="nowrap" class="r-count "></td>
    <td nowrap="nowrap" class="time">12-17 19:36</td>
</tr>
<tr class="">
    <td class="title">
        <a href="https://www.douban.com/group/topic/299328139/" title="亦庄线 京东总部附近 次渠 步行京东 朝南主卧飘窗1999 可长可短 包物业取暖 微同步13244360317无中介服务费" class="">
           亦庄线 京东总部附近 次渠 步行京东 朝南主卧飘窗1999 可长可...
        </a>
    </td>
    <td nowrap="nowrap">
        <a href="https://www.douban.com/people/266412461/" class="">郑哈哈</a>
    </td>
    <td nowrap="nowrap" class="r-count "></td>
    <td nowrap="nowrap" class="time">12-17 19:33</td>
</tr>
"""

soup = BeautifulSoup(html_doc, 'html.parser')

rows = soup.find_all('tr')

for row in rows:
    title = row.find('a', {'class': ''})['title']
    author = row.find('a', {'class': ''}).text
    time = row.find('td', {'class': 'time'}).text
    print(f"标题: {title}")
    print(f"作者: {author}")
    print(f"时间: {time}")
    print("------")