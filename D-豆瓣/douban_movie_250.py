import requests
from bs4 import BeautifulSoup

base_url = "https://movie.douban.com/top250"
headers = {"User-Agent": "Mozilla/5.0"}
def get_movies_on_page(url):
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'lxml')

    for movie in soup.find_all('div', class_='hd'):
        movie_name = movie.find('span', class_='title').text
        print(movie_name)

    next_page = soup.find('span', class_='next').find('a')
    if next_page:
        next_url = base_url + next_page['href']
        get_movies_on_page(next_url)

get_movies_on_page(base_url)