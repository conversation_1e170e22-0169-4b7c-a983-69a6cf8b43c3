from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import re
from imgdl import download
from notion_client import Client

import datetime
class notion_client:
    def __init__(self):
        global global_notion
        global global_database_id
        global_token = "**************************************************"
        global_database_id = "41be657c8a0742dfb483f59f30dc2297"  # 推文发布-手写-Data
        global_notion = Client(auth=global_token)
        print('开始Notion自动化获取数据...')

    # 获取数据
    def query_results_by_condication(self, quanzi_param: str, start_cursor=None):
        if start_cursor:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "start_cursor": start_cursor,
                    "filter": {
                        "and": [
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "contains": '初始化'
                                }
                            }
                        ]
                    }
                }
            )
        else:
            response = global_notion.databases.query(
                **{
                    "database_id": global_database_id,
                    "filter": {
                        "and": [
                            {
                                "property": 'Tags',
                                "multi_select": {
                                    "contains": '初始化'
                                }
                            },
                            # {
                            #     "property": '发布人',
                            #     'rich_text': [
                            #         {
                            #             'text': {
                            #                 'contains': 'ClutchPoints'
                            #             }
                            #         }
                            #     ]
                            # }
                        ]
                    }
                }
            )
        # 获取结果和下一页的cursor
        results = response['results']
        next_cursor = response.get('next_cursor')
        return results, next_cursor

    def get_content_by_condication(self, params: str, start_cursor=None):
        results, next_cursor = self.query_results_by_condication(params, start_cursor=start_cursor)
        for page in results:
            # 1. 过滤
            if page["object"] == "page":
                page_tags = page["properties"]["Tags"]["multi_select"]
                tag_flag = False
                for item in page_tags:
                    if item['name'] in ['CSDN发布成功', '格式化', 'CSDN发布失败']:
                        tag_flag = True
                        break
                if tag_flag:
                    continue

                # media_type = page["properties"]["media_type"]["select"]['name']
                # page_picture=''
                # page_video=''
                # if media_type in 'Image':
                # page_picture=page["properties"]['图片文件']['files'][0]
                # elif media_type in 'Video':
                #     # todo-解析URL
                # twitter_url = page["properties"]["关联URL"]["url"]
                #     video_url=get_video_url(twitter_url)
                #     target_directory='/Users/<USER>/Downloads/temp.mp4'
                #     video_url, _ = video_url.split('?')
                #     download_mp4(str(video_url),target_directory)
                #     page_video=f'{target_directory}/video.mp4'                    

                page_id = page["id"]
                page_content = page["properties"]['正文内容']['rich_text'][0]['plain_text']
                about_url = page["properties"]["关联URL"]["url"]
                page_picture = page["properties"]['图片文件']['files'][0]
                content = {
                    "page_id": page_id,
                    "page_content": page_content,
                    'page_picture': page_picture,
                    "about_url": about_url,
                }
                return content
        # 如果有下一页数据，则继续查询
        if next_cursor:
            content = self.get_content_by_condication(params, next_cursor)
            if content:
                return content

    def update_page_content(self, page_id, properties_params):
        page = global_notion.pages.retrieve(page_id)

        data = {"multi_select": []}
        data["multi_select"].append({"name": properties_params})
        page_tags = page["properties"]["Tags"]["multi_select"]
        for item in page_tags:
            name = item['name']
            data["multi_select"].append({"name": name})
        # 更新页面的属性
        update_payload = {
            "properties": {
                "Tags": {
                    "multi_select": data["multi_select"]
                }
                # 其他属性更新
            }
        }
        # 执行更新操作
        update_page = global_notion.pages.update(page_id=page_id, **update_payload)
        print("更新状态", properties_params)


def wtt_publish(page_content: dict, port: int):
    try:
        # 1.内容输入
        print('[开始微头条发布: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
        service = Service('/usr/local/bin/chromedriver')
        options = webdriver.ChromeOptions()
        ip = f'127.0.0.1:{port}'
        options.add_experimental_option("debuggerAddress", ip)
        options.add_argument('--headless')
        driver = webdriver.Chrome(service=service, options=options)
        url = "https://blink.csdn.net/?source=hot"
        driver.get(url)

        # 2. 输入短内容
        wait = WebDriverWait(driver, 10)  # 设置最大等待时间为10秒
        # editor = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '.comment-area')))
        # editor=wait.until(EC.presence_of_element_located((By.XPATH, "//div[@id='messageText']")))
        wait.until(EC.presence_of_element_located((By.ID, 'messageText')))
        editor = wait.until(EC.element_to_be_clickable((By.ID, 'messageText')))


        editor.clear()  # 清空编辑器内容
        editor.send_keys(Keys.SPACE)
        editor.send_keys(Keys.TAB)
        

        content = page_content['page_content']
        if 'about_url' in page_content:
            about_url = page_content['about_url']
            if about_url is not None and len(about_url) > 0:
                content = f'{content}\n\n{about_url}'

        editor.send_keys(content)
        print('发布端口号为:', port)
        print('发布内容长度为:', len(content))
      

        # 2.图片视频媒体输入
        # media_type=page_content['media_type']
        # if media_type in 'Image':
        try:
            # picture_url=page_content['page_picture']['external']['url'
            picture_url = page_content['page_picture']['file']['url']
            url_array = [picture_url]
            save_path = download(url_array, store_path=f'/Users/<USER>/temp/images/{port}/', n_workers=10)
            print(save_path)
        except Exception as e:
            print('下载图片异常,', e)
            # save_path='/Users/<USER>/temp/images/ef5575a47ea1710811da726697ab4440f588402d.jpg'
            return False

        # 上传图片
        inputs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "input[type='file']")))
        second_input = inputs[1]
        second_input.send_keys(save_path)  # 替换成你本地图片的路径
        time.sleep(2)
        # 3. 发布
        element_view_publish = wait.until(EC.presence_of_element_located((By.XPATH,
                                                                          "//span[@class='text' and text()='发布']")))
        element_view_publish.click()
        time.sleep(1)
        return True
    except Exception as e:
        print("无法找到元素", e)
        return False





def yesday():
    # 获取当前时间
    current_time = datetime.datetime.now()
    # 计算前一天的时间
    yesterday = current_time - datetime.timedelta(days=5)
    formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
    print(formatted_time)
    return formatted_time


def main():
    # 1. 读取文字内容
    try:
        client = notion_client()
    except Exception as e:
        print("client初始化失败", e)
        return
    try:
        port = 9223
        yesdaytime = yesday()
        content = client.get_content_by_condication(params=yesdaytime)
        if content is None:
            return
        page_id = content['page_id']
        page_content = content['page_content']
        # 2. 发布微博
        if page_content is None or len(page_content)==0:
            print('page_content为空,跳过发布')
            return
        flag = wtt_publish(page_content=content, port=port)
        if flag:
            # 3. 更新内容状态
            client.update_page_content(page_id=page_id, properties_params="CSDN发布成功")
        else:
            client.update_page_content(page_id=page_id, properties_params="CSDN发布失败")
    except Exception as e:
        print(f'发布失败:{e.body}')
        return

        # # 自增数据
        # index=i+1


'''
微头条发布
1. 只发布科技类账号
2. 消息源-Notion-AINEWs-EN-Auto
3. 频率半小时一发布,原创微头条,添加图片
'''
if __name__ == "__main__":
    main()

