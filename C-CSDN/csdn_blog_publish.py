import requests
from requests_toolbelt import MultipartEncoder
import hmac
import hashlib
import uuid
import time
import base64
import urllib.parse
fields = {
'title':'test', #标题字段
'markdowncontent':"""# 一级标题""", #markdown字段，也就是你编辑你自己文章的内容包含
'content':'''<h1><a id="_0"></a>一级标题</h1> 
<h2><a id="_1"></a>二级标题</h2>
<h3><a id="_2"></a>三级标题</h3>''', # 文章的主体内容，也就是渲染完成markdown形成的html
'id':'',  
'private':'', 
'tags':'',    
'status':'0',
'categories':'linux', #分类
'channel': '31',
'type':'original',
'articleedittype':'1',
'Description':'', 
'csrf_token':'',  
}

m = MultipartEncoder(fields, boundary='------WebKitFormBoundarynTBa3OWoSMrcVf0F')

def generate_signature(content_type, body):
    app_key = "203803574"
    app_secret = "9znpamsyl2c7cdrr9sas0le9vbc3r6ba"
    nonce = str(uuid.uuid4())
    timestamp = str(int(time.time() * 1000))
    
    # 将body转换为字符串
    if isinstance(body, bytes):
        body = body.decode('utf-8')
    
    # 构建签名字符串
    string_to_sign = "POST\n"  # HTTP方法
    string_to_sign += f"{content_type}\n"  # 使用实际的Content-Type
    string_to_sign += f"x-ca-key:{app_key}\n"
    string_to_sign += f"x-ca-nonce:{nonce}\n"
    string_to_sign += f"x-ca-timestamp:{timestamp}\n"
    string_to_sign += body  # 添加请求体到签名字符串
    
    # 使用HMAC-SHA256计算签名
    hmac_obj = hmac.new(
        app_secret.encode('utf-8'),
        string_to_sign.encode('utf-8'),
        hashlib.sha256
    )
    signature = base64.b64encode(hmac_obj.digest()).decode('utf-8')
    
    return {
        'signature': signature,
        'nonce': nonce,
        'timestamp': timestamp
    }

# 获取请求体
request_body = m.to_string()
content_type = m.content_type

# 获取签名信息
sign_info = generate_signature(content_type, request_body)

headers = {
    'cookie': 'UN=fwh66; Hm_up_6bcd52f51e9b3dce32bec4a3997715ac=%7B%22islogin%22%3A%7B%22value%22%3A%221%22%2C%22scope%22%3A1%7D%2C%22isonline%22%3A%7B%22value%22%3A%221%22%2C%22scope%22%3A1%7D%2C%22isvip%22%3A%7B%22value%22%3A%220%22%2C%22scope%22%3A1%7D%2C%22uid_%22%3A%7B%22value%22%3A%22fwh66%22%2C%22scope%22%3A1%7D%7D; cf_clearance=Q5RpGSd_d9yr3W7JgtZdEJyjmghu3GCt1Tf5vZI39fo-**********-*******-8vrJyXlX_yGiQABxuEx.1lIZHq2xfQygYJ.EDGnxIm6jER2fmS715wInQtCERT9SWq_jsXgUu2F_ddY4Xw92IA; HMACCOUNT=E7F4809EBEDD6FB6; fid=20_75467010637-*************-400299; Hm_ct_6bcd52f51e9b3dce32bec4a3997715ac=6525*1*10_30833055600-*************-319147!5744*1*fwh66; __gads=ID=280efa5722f3e359:T=**********:RT=**********:S=ALNI_MabXQgkWFlKNbI09e2d4PObWUp0og; __gpi=UID=00000e0f983bc430:T=**********:RT=**********:S=ALNI_MbjcY2mXxbxyqq177Eptk1HqnjO8Q; FCNEC=%5B%5B%22AKsRol_3xnaqHyauA6eOjx220aSrU-5gRssrPANzhCXCqqBdpoudElzGRCxaeZsKY_kj6ix1nVmQ8qzJrI5P7kiKOtTW2m_HLl5eeiB4pONoQj6Fab4IMdnUtcjsETU_7kfDFonBlwpYKlyuhgAxlqeBkscYVhmK7g%3D%3D%22%5D%5D; p_uid=U010000; csdn_newcert_fwh66=1; dc_sid=13b77c75b0eed0640ee6bbb16bf1a617; uuid_tt_dd=10_30831570590-*************-397806; c_segment=14; c_adb=1; Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac=**********; dc_session_id=10_1739863240819.254822; c_utm_relevant_index=1; relevant_index=1; c_utm_term=%E5%85%B3%E6%B3%A8%E9%A2%86%E8%B5%84%E6%96%99%E3%80%82%E4%B8%80%E5%8C%BA%E5%8C%BB%E5%AD%A6SCI%E6%9C%9F%E5%88%8A%E7%BC%96%E8%BE%91%EF%BC%8C%E6%8E%A2%E7%B4%A2AI%E5%9C%A8%E8%AE%BA%E6%96%87%E5%86%99%E4%BD%9C%E4%B8%AD%E7%9A%84%E5%BA%94%E7%94%A8%E3%80%82; creative_btn_mp=3; referrer_search=1739863251293; fe_request_id=1739863256815_8579_4561411; c_utm_medium=distribute.pc_search_result.none-task-user-null-1-163713933-null-null.nonecase; _clck=8oyczs%7C2%7Cftj%7C0%7C1805; SESSION=37bf3aca-f1ae-4a3d-ac61-496d43e8e94f; UserName=fwh66; UserInfo=c3033995be774046b97e18efbdb77850; UserToken=c3033995be774046b97e18efbdb77850; UserNick=%E6%96%87%E6%B5%A9Marvin; AU=514; BT=1739863413984; c_ins_prid=-; c_ins_rid=1739863484279_199990; c_ins_fref=https://mp.csdn.net/mp_blog/manage/article; c_ins_fpage=/index.html; c_ins_um=-; c_utm_source=636161750; utm_source=636161750; ins_first_time=1739863484530; x_inscode_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjcmVkZW50aWFsIjoiIiwiY3NkblVzZXJuYW1lIjoiZndoNjYiLCJ1c2VySWQiOiI2M2E3YzE3ZDZmN2UxNDUzZTVjNjJjMDAiLCJ1c2VybmFtZSI6ImZ3aDY2In0.unOjU7tYE-BFT5QP3LUMXOcHgceSHHh9Ipehcfba5oI; creativeSetApiNew=%7B%22toolbarImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20230921102607.png%22%2C%22publishSuccessImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20240229024608.png%22%2C%22articleNum%22%3A78%2C%22type%22%3A2%2C%22oldUser%22%3Atrue%2C%22useSeven%22%3Afalse%2C%22oldFullVersion%22%3Atrue%2C%22userName%22%3A%22fwh66%22%7D; c_pref=https%3A//editor.csdn.net/; c_ref=https%3A//www.google.com/; c_first_ref=www.google.com; c_first_page=https%3A//blog.csdn.net/chouzhou9701/article/details/89048519; _gid=GA1.2.769182283.1739863571; c_dsid=11_1739863621924.546589; c_page_id=default; log_Id_pv=24; Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac=1739863622; _ga_7W1N0GEY1P=GS1.1.1739863571.49.1.1739863622.9.0.0; _ga=GA1.2.798131037.1733754218; _clsk=tnh1nk%7C1739863658303%7C2%7C0%7Cj.clarity.ms%2Fcollect; log_Id_click=34; dc_tos=srvbfn; log_Id_view=749',
    'Content-Type': content_type,
    'origin': 'https://editor.csdn.net',
    'referer': 'https://editor.csdn.net/',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
    'x-ca-key': '203803574',
    'x-ca-nonce': sign_info['nonce'],
    'x-ca-timestamp': sign_info['timestamp'],
    'x-ca-signature': sign_info['signature'],
    'x-ca-signature-headers': 'x-ca-key,x-ca-nonce,x-ca-timestamp'
}

# url = 'https://mp.csdn.net/mdeditor/saveArticle'
url = 'https://bizapi.csdn.net/blog-console-api/v3/mdeditor/saveArticle'

res = requests.post(url, headers=headers, data=m.to_string())
print(res.text)

