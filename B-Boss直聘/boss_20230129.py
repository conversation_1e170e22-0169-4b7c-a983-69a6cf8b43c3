import time

from selenium import webdriver  # 从selenium导入webdriver
from selenium.webdriver.common.by import By  # 高版本语法使用
import csv
import datetime
# 新建浏览器
# driver = webdriver.Chrome
driver = webdriver.Chrome(executable_path='/usr/local/bin/chromedriver')  # 声明调用Chrome
dt = datetime.datetime.now()
day = dt.strftime('%Y%m%d')

with open(day.__str__()+'boss.csv', mode='a', encoding='utf-8', newline='') as f:
# with open('boss2023-02-16.csv', mode='a', encoding='utf-8', newline='') as f:
    csv_write = csv.writer(f)
    csv_write.writerow(
        ['job_name', 'job_area', 'job_salary', 'company', 'info', 'tags', 'desc', 'job_exp', 'people', 'href'])

'''
     通过css selector定位有如下三种常规方式：
　  find_element_by_selector("#kw")     （#表示通过id定位）
      find_element_by_selector(".s_ipt")     (. 表示通过class定位）
      find_element_by_selector("标签名“”)   其实单纯通过标签名来定位元素，是有很大局限性的，因为一个页面中，非常大可能的
      存在标签名的重复，因此无法很精确的定位。
      
      注意：
      find_elements 和 find_element  多个元素加s
'''


def get_data(page_num):
    url = "https://www.zhipin.com/web/geek/job?query=Java&city=*********&page=" + str(page_num)
    # 创建链接
    driver.get(url)
    # 等待两秒中
    driver.implicitly_wait(8)

    # 加点标签选择器
    lis = driver.find_elements(By.CSS_SELECTOR, ".job-card-wrapper")
    # lis = driver.find_elements_by_css_selector(".job-card-wrapper")
    try:
        for li in lis:
            job_name = li.find_element(By.CSS_SELECTOR, ".job-name").text
            job_area = li.find_element(By.CSS_SELECTOR, ".job-area").text  # 公司地址
            job_salary = li.find_element(By.CSS_SELECTOR, ".salary").text  # 工资水平
            company = li.find_element(By.CSS_SELECTOR, ".company-name a").text  # 公司名称
            info = li.find_element(By.CSS_SELECTOR, ".company-tag-list li").text  # 行业
            tags = li.find_element(By.CSS_SELECTOR, ".tag-list").text  # 年限和学历
            # tags = li.find_element(By.CSS_SELECTOR, ".tags").text  # 技术标签
            desc = li.find_element(By.CSS_SELECTOR, ".info-desc").text  # 福利描述
            href = li.find_element(By.CSS_SELECTOR, ".job-card-left").get_attribute("href")  # 页面地址
            job_exp = li.find_element(By.CSS_SELECTOR, ".tag-list").text  # 学历-经验
            # people = li.find_element(By.CSS_SELECTOR, ".company-tag-list li+li+li").text  # 公司人数
            people = li.find_element(By.CSS_SELECTOR, ".company-tag-list").text  # 公司人数

            print(job_name, job_area, job_salary, company, info, tags, desc, href, job_exp, people)
            # 保存数据
            with open(day.__str__()+'boss.csv', mode='a', encoding='utf-8', newline='') as f:
                csv_write = csv.writer(f)
                csv_write.writerow([job_name, job_area, job_salary, company, info, tags, desc, job_exp, people, href])
    except Exception as e:
        print("报错数据异常", e)
        pass


if __name__ == '__main__':
    for page in range(1, 11):
        try:
            get_data(page)
            time.sleep(2)
            # driver.find_element_by_css_selector(".next").click()
            # driver.find_element(By.CSS_SELECTOR, ".next").click()
        except:
            print("exception……")
            driver.quit()
    print("end……")
    driver.quit()
