from selenium import webdriver  # 从selenium导入webdriver
import csv

url = "https://www.zhipin.com/job_detail/?query=Java&city=*********&industry=&position="

# 新建浏览器
# driver = webdriver.Chrome
driver = webdriver.Chrome(executable_path='/usr/local/bin/chromedriver')  # 声明调用Chrome
# 创建链接
driver.get(url)
# 等待两秒中
driver.implicitly_wait(8)

with open('boss.csv', mode='a', encoding='utf-8', newline='')as f:
    csv_write = csv.writer(f)
    csv_write.writerow(
        ['job_name', 'job_area', 'job_salary', 'company', 'info', 'tags', 'desc', 'job_exp', 'people', 'href'])


def get_data():
    # 加点标签选择器
    lis = driver.find_elements_by_css_selector(".job-primary")
    for li in lis:
        job_name = li.find_element_by_css_selector(".job-name").text  # 职位名称
        job_area = li.find_element_by_css_selector(".job-area").text  # 公司地址
        job_salary = li.find_element_by_css_selector(".red").text  # 工资水平
        company = li.find_element_by_css_selector(".company-text .name a").text  # 公司名称
        info = li.find_element_by_css_selector(".company-text p a").text  # 行业
        tags = li.find_element_by_css_selector(".tags").text  # 技术标签
        desc = li.find_element_by_css_selector(".info-desc").text  # 福利描述
        href = li.find_element_by_css_selector(".primary-box").get_attribute("href")  # 页面地址
        job_exp = li.find_element_by_css_selector(".job-limit.clearfix p").text  # 学历-经验
        people = li.find_element_by_css_selector(".company-text p ").text  # 公司人数

        print(job_name, job_area, job_salary, company, info, tags, desc, href, job_exp, people)
        # 保存数据

        with open('boss.csv', mode='a', encoding='utf-8', newline='')as f:
            csv_write = csv.writer(f)
            csv_write.writerow([job_name, job_area, job_salary, company, info, tags, desc, job_exp, people, href])


if __name__ == '__main__':
    for page in (1, 11):
        get_data()
        try:
            driver.find_element_by_css_selector(".next").click()
        except:
            print("exception……")
            driver.quit()
    print("end……")
    driver.quit()
