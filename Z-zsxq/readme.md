# 介绍:
1. 每天指定时间采集生财有数知识星球的动态信息,存储到Notion.  知识星球-Data

## 备注:
1. 目前项目已经拆分到spider-zsxq

## TODO:
1. 功能:下载知识星球的视频
2. 实现: 微信扫码获取Cookie,解析网页地址,得到m3u8,下载视频,ffmpeg下载合并
3. 考虑: Chrome插件,自动解析下载,已经登录态知识星球的视频,到本地.

# prompt分析：
````
调用mcp 获取spider_db数据库中zxsq表的最近一个月的数据，尤其是风向标的， 挖掘需求，分析哪些可以开发web网站解决需求盈利的。注意目的是盈利，需要挖掘数据中的需求。 将内容汇总到md文档中  @/Users/<USER>/fuwenhao/github/spider/Z-zsxq/ 

调用mcp 获取spider_db数据库中zxsq表的最近六个月的数据，尤其是风向标的， 挖掘需求，分析哪些可以开发web网站解决需求盈利的。注意目的是盈利，需要挖掘数据中的需求。 将内容汇总到md文档中  @/Users/<USER>/fuwenhao/github/spider/Z-zsxq/ 

AI内容创作工具平台,  查询相关的zxsq帖子数据，可以是近期一年的数据，是如何描述需要这个需求的，最好有深度的分析，带有原帖子的数据信息url等。 将内容总结到新的md文档中 @/Users/<USER>/fuwenhao/github/spider/Z-zsxq/ 
````