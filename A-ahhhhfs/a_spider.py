import requests
from bs4 import BeautifulSoup
import os
import re
import concurrent.futures
import time
import urllib3
import util_image

headers = {
    'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.20 SP-engine/2.16.0 baiduboxapp/11.20.2.3 (Bai<PERSON>; P1 9)'
}
def get_directory():
    for page in range(1, 2):
        print(f'page:{page}')
        url = f'https://www.ahhhhfs.com/recourse/page/{page}/'
        try:
            response = requests.get(url, headers=headers, timeout=30)
            soup = BeautifulSoup(response.text, 'html.parser')
            article_list = soup.find_all('article', class_='post-item item-list')
            
            for p_tag in article_list:
                a_tag = p_tag.find('a')
                if a_tag:
                    href = a_tag.get('href')
                    title = a_tag.get('title')
                    img_bg = a_tag.get('data-bg')
                    
                    path = href.rstrip('/')
                    index = path.rfind('/')
                    id = path[index + 1:]

                    # 图片下载到本地
                    util_image.image_download(img_bg, f'/Volumes/文章存档/Images/ahhhhfs/{id}/')
                    print(f"href: {href}, title: {title}, id:{id}, img_bg:{img_bg}")

                    
                    


        except Exception as e:
            print(f"Error in get_book_list for page {page}: {str(e)}")


def get_detail(url):
    try:
        response = requests.get(url, headers=headers, timeout=30)
        soup = BeautifulSoup(response.text, 'html.parser')
        # 查找第一个 article 标签
        article_tag = soup.find('article')
        # 获取 article 标签中的文本内容
        article_text = article_tag.get_text(strip=True)
        print(article_text)
        
        # 查找所有 p 标签
        p_tags = soup.find_all('p')
        # 遍历所有 p 标签，查找包含“下载地址”文本的 p 标签
        for p_tag in p_tags:
            if '下载地址' in p_tag.get_text():
                # 查找 p 标签中的 a 标签
                a_tag = p_tag.find('a')
                if a_tag:
                    # 获取 a 标签的 href 属性
                    if '夸克网盘'==a_tag.text.strip():
                        href_link = a_tag['href']
                        print(href_link)
    except Exception as e:
        print(f"Error in get_detail for {url}: {str(e)}")


if __name__ == '__main__':
    get_directory()
    get_detail('https://www.ahhhhfs.com/62299')