import requests
import json
from config import APP_ID, APP_SECRET  # 需要创建config.py存储这些信息

class FeishuDoc:
    def __init__(self):
        self.app_id = APP_ID
        self.app_secret = APP_SECRET
        self.access_token = self.get_access_token()
    
    def get_access_token(self):
        """获取飞书访问令牌"""
        try:
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }
            
            response = requests.post(url, headers=headers, json=payload)
            response_json = response.json()
            
            if response_json.get("code") == 0 and "tenant_access_token" in response_json:
                return response_json["tenant_access_token"]
            else:
                print(f"获取token返回错误: {response_json}")
                raise Exception(f"获取访问令牌失败: {response_json}")
        except Exception as e:
            print(f"获取访问令牌时发生错误: {e}")
            raise

    def create_document(self, title, folder_token=None):
        """
        创建飞书文档
        
        Args:
            title (str): 文档标题
            folder_token (str, optional): 文件夹token。如果不指定，将在根目录创建文档
        
        Returns:
            dict: API响应结果
        """
        url = "https://open.feishu.cn/open-apis/docx/v1/documents"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}"
        }
        
        payload = {
            "title": title
        }
        
        if folder_token:
            payload["folder_token"] = folder_token
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            if response.status_code != 200:
                print(f"错误响应: {response.text}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"创建文档时发生错误: {e}")
            print(f"请求URL: {url}")
            print(f"请求头: {headers}")
            print(f"请求数据: {payload}")
            return None

    def get_document_info(self, document_id):
        """
        获取文档基本信息
        
        Args:
            document_id (str): 文档ID
        
        Returns:
            dict: 文档信息，获取失败返回None
        """
        url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}"
        
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        try:
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                print(f"错误响应: {response.text}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取文档信息时发生错误: {e}")
            print(f"请求URL: {url}")
            print(f"请求头: {headers}")
            return None

def main():
    # 测试创建文档       "document_id": "Aam1d99QfoLtQKxS8ewcCDtcnYf",
    try:
        feishu_doc = FeishuDoc()
        title = "测试文档"
        folder_token = None  # 可选，如果需要指定文件夹可以设置
        
        result = feishu_doc.create_document(title, folder_token)
        if result:
            print("文档创建成功！")
            print(f"文档信息: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print("文档创建失败。")

        # 获取创建的文档信息
        # document_id = result.get("data", {}).get("document_id")
        document_id = "Aam1d99QfoLtQKxS8ewcCDtcnYf"
        if document_id:
            doc_info = feishu_doc.get_document_info(document_id)
            if doc_info:
                print("\n获取文档信息成功：")
                print(json.dumps(doc_info, ensure_ascii=False, indent=2))
            else:
                print("获取文档信息失败。")
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()
