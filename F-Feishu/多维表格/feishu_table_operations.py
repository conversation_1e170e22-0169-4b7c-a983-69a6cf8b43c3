import json
import logging
import random
from datetime import datetime

import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
from lark_oapi.api.bitable.v1.model.app_table_create_header import AppTableCreateHeader
from lark_oapi.api.bitable.v1.model.req_table import ReqTable
from lark_oapi.api.bitable.v1.model.create_app_table_request import CreateAppTableRequest
from lark_oapi.api.bitable.v1.model.create_app_table_request_body import CreateAppTableRequestBody
from lark_oapi.api.bitable.v1.model.list_app_table_request import ListAppTableRequest
from lark_oapi.api.bitable.v1.model.app_table_record import AppTableRecord
from lark_oapi.api.bitable.v1.model.batch_create_app_table_record_request import BatchCreateAppTableRecordRequest
from lark_oapi.api.bitable.v1.model.batch_create_app_table_record_request_body import BatchCreateAppTableRecordRequestBody
from lark_oapi.api.bitable.v1.model.list_app_table_record_request import ListAppTableRecordRequest
from lark_oapi.api.bitable.v1.model.get_app_table_view_request import GetAppTableViewRequest
from lark_oapi.api.bitable.v1.model.list_app_table_view_request import ListAppTableViewRequest

# Global Constants
APP_ID = "cli_a6cf676d42fb9013"
APP_SECRET = "1R07r37QZHwgiTzhdXxeRlIR7oO4Ynju"
APP_TOKEN = "DkNzb2kftaOXwRs68Z4cepL2nfg"

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
)
logger = logging.getLogger(__name__)

class FeishuBitableAPI:
    def __init__(self):
        self.app_id = APP_ID
        self.app_secret = APP_SECRET
        self.app_token = APP_TOKEN
        self.client = self._init_client()
        
    def _init_client(self):
        """初始化飞书客户端"""
        try:
            client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .log_level(lark.LogLevel.DEBUG) \
                .build()
            logger.info("成功初始化飞书客户端")
            return client
        except Exception as e:
            logger.error(f"初始化飞书客户端失败: {str(e)}", exc_info=True)
            raise
            
    def get_app_info(self):
        """获取多维表格应用信息"""
        try:
            request = GetAppRequest.builder() \
                .app_token(self.app_token) \
                .build()
                
            response = self.client.bitable.v1.app.get(request)
            
            if not response.success():
                logger.error(
                    f"获取应用信息失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            logger.info("成功获取应用信息")
            return response.data
            
        except Exception as e:
            logger.error(f"获取应用信息时发生错误: {str(e)}", exc_info=True)
            return None
            
    def get_app_tables(self):
        """获取多维表格中的所有数据表"""
        try:
            request = ListAppTableRequest.builder() \
                .app_token(self.app_token) \
                .page_size(100) \
                .build()
                
            response = self.client.bitable.v1.app_table.list(request)
            
            if not response.success():
                logger.error(
                    f"获取数据表列表失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            items = response.data.items
            logger.info(f"成功获取 {len(items)} 个数据表")
            return items
            
        except Exception as e:
            logger.error(f"获取数据表列表时发生错误: {str(e)}", exc_info=True)
            return None
            
    def create_table(self, table_name, fields):
        """
        创建新的数据表
        
        Args:
            table_name (str): 数据表名称
            fields (list): 字段定义列表，例如：
                [
                    {"field_name": "名称", "type": "text"},
                    {"field_name": "数量", "type": "number"}
                ]
        """
        try:
            # 构建字段列表
            field_list = []
            for field in fields:
                field_obj = AppTableCreateHeader.builder() \
                    .field_name(field["field_name"]) \
                    .type(self._get_field_type(field["type"])) \
                    .build()
                field_list.append(field_obj)
                
            # 构建创建表格请求
            request = CreateAppTableRequest.builder() \
                .app_token(self.app_token) \
                .request_body(CreateAppTableRequestBody.builder()
                    .table(ReqTable.builder()
                        .name(table_name)
                        .default_view_name("默认视图")
                        .fields(field_list)
                        .build())
                    .build()) \
                .build()
                
            logger.debug(f"创建表格请求数据: {request}")
            response = self.client.bitable.v1.app_table.create(request)
            
            if not response.success():
                logger.error(
                    f"创建数据表失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            logger.info(f"成功创建数据表: {table_name}")
            return response.data.table
            
        except Exception as e:
            logger.error(f"创建数据表时发生错误: {str(e)}", exc_info=True)
            return None
            
    def _get_field_type(self, type_str):
        """将字符串类型转换为飞书多维表格的字段类型值"""
        type_mapping = {
            "text": 1,      # 文本
            "number": 2,    # 数字
            "select": 3,    # 单选
            "multiselect": 4, # 多选
            "datetime": 5,  # 日期
            "checkbox": 7,  # 复选框
            "user": 11,    # 人员
            "link": 15,    # 超链接
            "attachment": 17, # 附件
        }
        return type_mapping.get(type_str.lower(), 1)  # 默认返回文本类型
            
    def add_records(self, table_id, records):
        """
        添加记录到数据表
        
        Args:
            table_id (str): 数据表ID
            records (list): 记录列表，例如：
                [
                    {"fields": {"名称": "商品1", "数量": 100}},
                    {"fields": {"名称": "商品2", "数量": 200}}
                ]
        """
        try:
            # 构建记录列表
            record_list = []
            for record in records:
                record_obj = AppTableRecord.builder() \
                    .fields(record["fields"]) \
                    .build()
                record_list.append(record_obj)
                
            # 构建批量添加记录请求
            request = BatchCreateAppTableRecordRequest.builder() \
                .app_token(self.app_token) \
                .table_id(table_id) \
                .request_body(BatchCreateAppTableRecordRequestBody.builder()
                    .records(record_list)
                    .build()) \
                .build()
                
            logger.debug(f"添加记录请求数据: {request}")
            response = self.client.bitable.v1.app_table_record.batch_create(request)
            
            if not response.success():
                logger.error(
                    f"添加记录失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            logger.info(f"成功添加 {len(record_list)} 条记录")
            return response.data.records
            
        except Exception as e:
            logger.error(f"添加记录时发生错误: {str(e)}", exc_info=True)
            return None
            
    def get_records(self, table_id, page_size=100, page_token=None):
        """获取数据表中的记录"""
        try:
            request = ListAppTableRecordRequest.builder() \
                .app_token(self.app_token) \
                .table_id(table_id) \
                .page_size(page_size) \
                .page_token(page_token) \
                .build()
                
            response = self.client.bitable.v1.app_table_record.list(request)
            
            if not response.success():
                logger.error(
                    f"获取记录失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            logger.info(f"成功获取记录")
            return {
                "records": response.data.items,
                "has_more": response.data.has_more,
                "page_token": response.data.page_token
            }
            
        except Exception as e:
            logger.error(f"获取记录时发生错误: {str(e)}", exc_info=True)
            return None
            
    def get_table_view(self, table_id, view_id):
        """获取数据表视图信息"""
        try:
            request = GetAppTableViewRequest.builder() \
                .app_token(self.app_token) \
                .table_id(table_id) \
                .view_id(view_id) \
                .build()
                
            response = self.client.bitable.v1.app_table_view.get(request)
            
            if not response.success():
                logger.error(
                    f"获取视图失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            logger.info(f"成功获取视图信息")
            return response.data
            
        except Exception as e:
            logger.error(f"获取视图时发生错误: {str(e)}", exc_info=True)
            return None
            
    def get_table_views(self, table_id):
        """获取数据表的所有视图"""
        try:
            request = ListAppTableViewRequest.builder() \
                .app_token(self.app_token) \
                .table_id(table_id) \
                .page_size(100) \
                .build()
                
            response = self.client.bitable.v1.app_table_view.list(request)
            
            if not response.success():
                logger.error(
                    f"获取视图列表失败, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None
                
            items = response.data.items
            logger.info(f"成功获取 {len(items)} 个视图")
            return items
            
        except Exception as e:
            logger.error(f"获取视图列表时发生错误: {str(e)}", exc_info=True)
            return None

def main():
    try:
        logger.info("=" * 50)
        logger.info("开始测试飞书多维表格API")
        logger.info("=" * 50)
        
        api = FeishuBitableAPI()
        
        # 1. 获取指定表格的视图信息
        logger.info("\n1. 获取指定表格的视图信息:")
        table_id = "tblQ5xLLhHHDfggX"
        view_id = "vewGHbAp6v"
        
        view_info = api.get_table_view(table_id, view_id)
        if view_info:
            logger.info(f"视图信息: {view_info}")
            
        # 2. 获取表的所有视图
        logger.info("\n2. 获取表的所有视图:")
        views = api.get_table_views(table_id)
        if views:
            logger.info(f"成功获取 {len(views)} 个视图")
            for view in views:
                logger.info(f"视图ID: {view.view_id}")
                logger.info(f"视图类型: {view.view_type}")
                logger.info("-" * 30)
                
        # 3. 获取视图中的记录
        logger.info("\n3. 获取视图中的记录:")
        result = api.get_records(table_id)
        if result:
            records = result["records"]
            logger.info(f"读取到 {len(records)} 条记录:")
            for record in records:
                logger.debug(f"记录ID: {record.record_id}")
                logger.debug(f"字段值: {record.fields}")
                
        return {
            "view_info": view_info,
            "views": views,
            "records": result["records"] if result else None
        }
        
    except Exception as e:
        logger.error(f"主程序执行出错: {str(e)}", exc_info=True)
        return None

if __name__ == "__main__":
    result = main()
    if result:
        logger.info("程序执行成功")
        logger.info("=" * 50)
    else:
        logger.error("程序执行失败")
        logger.info("=" * 50)
