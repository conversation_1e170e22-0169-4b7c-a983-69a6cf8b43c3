import json

import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *


# SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
# 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
def main():
    # 创建client
    # 使用 user_access_token 需开启 token 配置, 并在 request_option 中配置 token
    client = lark.Client.builder() \
        .app_id("cli_a6cf676d42fb9013") \
        .app_secret("1R07r37QZHwgiTzhdXxeRlIR7oO4Ynju") \
        .log_level(lark.LogLevel.DEBUG) \
        .build()

    # 构造请求对象
    request: CreateAppTableRequest = CreateAppTableRequest.builder() \
        .app_token("DkNzb2kftaOXwRs68Z4cepL2nfg") \
        .request_body(CreateAppTableRequestBody.builder()
            .table(ReqTable.builder()
                .name("数据表名称")
                .default_view_name("默认的表格视图")
                .fields([AppTableCreateHeader.builder()
                    .field_name("索引字段")
                    .type(1)
                    .build(), 
                    AppTableCreateHeader.builder()
                    .field_name("单选")
                    .type(3)
                    .ui_type("SingleSelect")
                    .property(AppTableFieldProperty.builder()
                        .options([AppTableFieldPropertyOption.builder()
                            .name("Enabled")
                            .color(0)
                            .build(), 
                            AppTableFieldPropertyOption.builder()
                            .name("Disabled")
                            .color(1)
                            .build(), 
                            AppTableFieldPropertyOption.builder()
                            .name("Draft")
                            .color(2)
                            .build()
                            ])
                        .build())
                    .build()
                    ])
                .build())
            .build()) \
        .build()

    # 发起请求
    option = lark.RequestOption.builder().user_access_token("**********************************************").build()
    response: CreateAppTableResponse = client.bitable.v1.app_table.create(request, option)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.bitable.v1.app_table.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return

    # 处理业务结果
    lark.logger.info(lark.JSON.marshal(response.data, indent=4))


if __name__ == "__main__":
    main()