import requests
import json
import time
from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Retry

# Helper function to send requests with retries
def send_request(url, headers=None, data=None, n_retries=4, backoff_factor=0.9, status_codes=[504, 503, 502, 500, 429]):
    sess = requests.Session()
    retries = Retry(connect=n_retries, backoff_factor=backoff_factor, status_forcelist=status_codes)
    sess.mount("https://", HTTPAdapter(max_retries=retries))
    sess.mount("http://", HTTPAdapter(max_retries=retries))
    if data:
        response = sess.post(url, headers=headers, data=data) if headers else sess.post(url, data=data)
    else:
        response = sess.get(url, headers=headers) if headers else sess.get(url)
    return response

# Function to convert column number to name
def excel_column_number_to_name(column_number):
    string = ""
    while column_number > 0:
        column_number, remainder = divmod(column_number - 1, 26)
        string = chr(65 + remainder) + string
    return string

class FeishuAPI:
    def __init__(self):
        self.app_id = "cli_a6cf676d42fb9013"
        self.app_secret = "1R07r37QZHwgiTzhdXxeRlIR7oO4Ynju"
        self.sheet_token = "FyHxszviuhs7HBtx0sLc0hnwnHf"
        self.access_token = self.get_access_token()

    def get_access_token(self):
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
        payload = json.dumps({"app_id": self.app_id, "app_secret": self.app_secret})
        response = send_request(url, data=payload)
        response_json = response.json()
        if "tenant_access_token" in response_json:
            return response_json["tenant_access_token"]
        else:
            raise Exception("Failed to get access token: {}".format(response_json))

    # ... (other methods should be implemented similarly to get_access_token)
    
def main(account_name, status, area, name):
    try:
        feishu_api = FeishuAPI()
        idx = feishu_api.get_newidx()
        idx = idx + 1
        feishu_api.to_send_(account_name, status, area, name, idx)
    except Exception as e:
        print(f'发送异常{e}')
# Call the main function if the script is run directly
if __name__ == "__main__":
    account_name = "example_account"
    status = "example_status"
    area = "example_area"
    name = "example_name"
    main(account_name, status, area, name)