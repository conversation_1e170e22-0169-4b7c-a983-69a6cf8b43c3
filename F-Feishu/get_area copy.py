import requests
import json
from requests.adapters import HTT<PERSON>dapter, Retry
import time


# Constants
APP_ID = "cli_a6cf676d42fb9013"
APP_SECRET = "1R07r37QZHwgiTzhdXxeRlIR7oO4Ynju"
SHEET_TOKEN = "FyHxszviuhs7HBtx0sLc0hnwnHf"

# Helper function to send requests with retries
def send_request(url, headers=None, data=None, n_retries=4, backoff_factor=0.9, status_codes=[504, 503, 502, 500, 429]):
    sess = requests.Session()
    retries = Retry(connect=n_retries, backoff_factor=backoff_factor, status_forcelist=status_codes)
    sess.mount("https://", HTTPAdapter(max_retries=retries))
    sess.mount("http://", HTTPAdapter(max_retries=retries))
    try_count = 0
    max_tries = 3  # 最大重试次数
    
    while try_count < max_tries:
        try:
            if data:
                response = sess.post(url, headers=headers, data=data) if headers else sess.post(url, data=data)
            else:
                response = sess.get(url, headers=headers) if headers else sess.get(url)
            
            if response.status_code == 200:
                return response
            elif response.status_code == 403:
                print(f"请求被拒绝(403)，正在重试... 第{try_count + 1}次")
                time.sleep(backoff_factor * (2 ** try_count))  # 指数退避
            else:
                print(f"请求失败，状态码: {response.status_code}")
            
            try_count += 1
            
        except Exception as e:
            print(f"请求发生错误: {e}")
            try_count += 1
            time.sleep(backoff_factor * (2 ** try_count))
            
    raise Exception(f"请求失败，已重试{max_tries}次")

class FeishuAPI:
    def __init__(self):
        self.app_id = APP_ID
        self.app_secret = APP_SECRET
        self.sheet_token = SHEET_TOKEN
        self.access_token = self.get_access_token()

    def get_access_token(self):
        try:
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            payload = json.dumps({
                "app_id": self.app_id,
                "app_secret": self.app_secret
            })
            response = send_request(url, headers=headers, data=payload)
            response_json = response.json()
            if response_json.get("code") == 0 and "tenant_access_token" in response_json:
                return response_json["tenant_access_token"]
            else:
                print(f"获取token返回错误: {response_json}")
                raise Exception(f"获取访问令牌失败: {response_json}")
        except Exception as e:
            print(f"获取访问令牌时发生错误: {e}")
            raise

    def get_area_todo_accounts(self, area):
        try:
            curr_time = time.strftime("%Y/%m/%d", time.localtime())
            range = "nieatn!A1:G5000"
            url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{self.sheet_token}/values_batch_get?ranges={range}&valueRenderOption=FormattedValue"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            response = send_request(url, headers=headers)
            response_json = response.json()
            
            if response_json.get("code") != 0:
                print(f"API返回错误: {response_json}")
                # 如果是token过期，尝试重新获取token
                if response_json.get("code") == ********:
                    self.access_token = self.get_access_token()
                    headers["Authorization"] = f"Bearer {self.access_token}"
                    response = send_request(url, headers=headers)
                    response_json = response.json()
            
            responselist = [i[0:7] for i in response_json['data']['valueRanges'][0]['values'] if i[0] is not None]
            accountsList = [account[0] for account in responselist if account[2] == area and account[6] != curr_time and account[6] is not None]
            accountsIdxList = [idx + 1 for idx, account in enumerate(responselist) if account[2] == area and account[6] != curr_time and account[6] is not None]
            return accountsList, accountsIdxList
        except Exception as e:
            print(f"获取待办账号时发生错误: {e}")
            raise

    def get_path(self, area, path="lrq15X"):
        range = f"{path}!A1:K500"
        url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{self.sheet_token}/values_batch_get?ranges={range}"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = send_request(url, headers=headers)
        response_json = response.json()
        cate1 = [i[8] for i in response_json['data']['valueRanges'][0]['values'] if i[0] == area][0]
        cate2 = [i[9] for i in response_json['data']['valueRanges'][0]['values'] if i[0] == area][0]
        return cate1, cate2

    def get_area_list(self, path="lrq15X"):
        range = f"{path}!A1:I500"
        url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{self.sheet_token}/values_batch_get?ranges={range}"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = send_request(url, headers=headers)
        response_json = response.json()
        responseRPAingPath = list(set([i[0] for i in response_json['data']['valueRanges'][0]['values'] if i[0] is not None and i[0] != "field name"]))
        return responseRPAingPath

def main():
    feishu_api = FeishuAPI()
    cate1 = feishu_api.get_area_list()
    print(cate1)
    return cate1

# Call the main function if the script is run directly
if __name__ == "__main__":
    main()