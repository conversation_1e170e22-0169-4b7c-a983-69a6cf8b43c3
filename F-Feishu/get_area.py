import requests
import json
from requests.adapters import HTT<PERSON>dapter, Retry
import time


# Constants
APP_ID = "cli_a6cf676d42fb9013"
APP_SECRET = "1R07r37QZHwgiTzhdXxeRlIR7oO4Ynju"
SHEET_TOKEN = "FyHxszviuhs7HBtx0sLc0hnwnHf"
from requests.exceptions import ProxyError, ConnectionError

proxy = {
    "http": "http://127.0.0.1:7890",
    "https": "http://127.0.0.1:7890",
}
def is_proxy_available(proxy):
    """检查代理是否可用"""
    try:
        # 尝试使用代理进行简单的GET请求
        response = requests.get('http://httpbin.org/ip', proxies=proxy, timeout=5)
        return response.status_code == 200
    except (ProxyError, ConnectionError):
        return False

# Helper function to send requests with retries
def send_request(url, headers=None, data=None, n_retries=4, backoff_factor=0.9, status_codes=[504, 503, 502, 500, 429]):
    sess = requests.Session()
    retries = Retry(connect=n_retries, backoff_factor=backoff_factor, status_forcelist=status_codes)
    sess.mount("https://", HTTPAdapter(max_retries=retries))
    sess.mount("http://", HTTPAdapter(max_retries=retries))
    while True:
        try:
            if data:
                if proxy and is_proxy_available(proxy):
                    print("使用代理发送请求")
                    response = sess.post(url, headers=headers, data=data,proxies=proxy) if headers else sess.post(url, data=data,proxies=proxy)
                else:
                    print("不使用代理发送请求")
                    response = sess.post(url, headers=headers, data=data) if headers else sess.post(url, data=data)
            else:
                if proxy and is_proxy_available(proxy):
                    print("使用代理发送请求")
                    response = sess.get(url, headers=headers,proxies=proxy) if headers else sess.get(url,proxies=proxy)
                else:
                    print("不使用代理发送请求")
                    response = sess.get(url, headers=headers) if headers else sess.get(url)
        except Exception as e:
            print(f"Request failed: {e}")
            continue
        if response.status_code == 200:
            return response

class FeishuAPI:
    def __init__(self):
        self.app_id = APP_ID
        self.app_secret = APP_SECRET
        self.sheet_token = SHEET_TOKEN
        self.access_token = self.get_access_token()

    def get_access_token(self):
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
        payload = json.dumps({"app_id": self.app_id, "app_secret": self.app_secret})
        response = send_request(url, data=payload)
        response_json = response.json()
        if "tenant_access_token" in response_json:
            return response_json["tenant_access_token"]
        else:
            raise Exception(f"Failed to get access token: {response_json}")

    def get_area_todo_accounts(self, area):
        curr_time = time.strftime("%Y/%m/%d", time.localtime())
        range = "nieatn!A1:G5000"
        url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{self.sheet_token}/values_batch_get?ranges={range}&valueRenderOption=FormattedValue"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = send_request(url, headers=headers)
        response_json = response.json()
        responselist = [i[0:7] for i in response_json['data']['valueRanges'][0]['values'] if i[0] is not None]
        accountsList = [account[0] for account in responselist if account[2] == area and account[6] != curr_time and account[6] is not None]
        accountsIdxList = [idx + 1 for idx, account in enumerate(responselist) if account[2] == area and account[6] != curr_time and account[6] is not None]
        return accountsList, accountsIdxList

    def get_path(self, area, path="lrq15X"):
        range = f"{path}!A1:K500"
        url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{self.sheet_token}/values_batch_get?ranges={range}"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = send_request(url, headers=headers)
        response_json = response.json()
        cate1 = [i[8] for i in response_json['data']['valueRanges'][0]['values'] if i[0] == area][0]
        cate2 = [i[9] for i in response_json['data']['valueRanges'][0]['values'] if i[0] == area][0]
        return cate1, cate2

    def get_area_list(self, path="lrq15X"):
        range = f"{path}!A1:I500"
        url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{self.sheet_token}/values_batch_get?ranges={range}"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = send_request(url, headers=headers)
        response_json = response.json()
        responseRPAingPath = list(set([i[0] for i in response_json['data']['valueRanges'][0]['values'] if i[0] is not None and i[0] != "field name"]))
        return responseRPAingPath

def main():
    feishu_api = FeishuAPI()
    cate1 = feishu_api.get_area_list()
    print(cate1)
    return cate1

# Call the main function if the script is run directly
if __name__ == "__main__":
    main()