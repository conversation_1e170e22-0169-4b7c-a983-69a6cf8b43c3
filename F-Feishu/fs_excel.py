import requests
import json
from requests.adapters import HTT<PERSON>dapter, Retry
import time
import logging

# Global Constants
APP_ID = "cli_a6cf676d42fb9013"
APP_SECRET = "1R07r37QZHwgiTzhdXxeRlIR7oO4Ynju"
SHEET_TOKEN = "UrAgsVWWihxDg9tS1oYcwiyKnUh"


# API Constants
BASE_URL = "https://open.feishu.cn/open-apis"
AUTH_URL = f"{BASE_URL}/auth/v3/tenant_access_token/internal/"
SHEETS_URL = f"{BASE_URL}/sheets/v2/spreadsheets"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Helper function to send requests with retries
def send_request(url, headers=None, data=None, n_retries=4, backoff_factor=0.9, status_codes=[504, 503, 502, 500, 429]):
    sess = requests.Session()
    retries = Retry(connect=n_retries, backoff_factor=backoff_factor, status_forcelist=status_codes)
    sess.mount("https://", HTTPAdapter(max_retries=retries))
    sess.mount("http://", HTTPAdapter(max_retries=retries))
    try_count = 0
    max_tries = 3  # 最大重试次数
    
    while try_count < max_tries:
        try:
            if data:
                response = sess.post(url, headers=headers, data=data) if headers else sess.post(url, data=data)
            else:
                response = sess.get(url, headers=headers) if headers else sess.get(url)
            
            if response.status_code == 200:
                return response
            elif response.status_code == 403:
                print(f"请求被拒绝(403)，正在重试... 第{try_count + 1}次")
                time.sleep(backoff_factor * (2 ** try_count))  # 指数退避
            else:
                print(f"请求失败，状态码: {response.status_code}")
            
            try_count += 1
            
        except Exception as e:
            print(f"请求发生错误: {e}")
            try_count += 1
            time.sleep(backoff_factor * (2 ** try_count))
            
    raise Exception(f"请求失败，已重试{max_tries}次")

class FeishuAPI:
    def __init__(self):
        self.app_id = APP_ID
        self.app_secret = APP_SECRET
        self.sheet_token = SHEET_TOKEN
        self.access_token = self.get_access_token()
        
    def get_access_token(self):
        try:
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            payload = json.dumps({
                "app_id": self.app_id,
                "app_secret": self.app_secret
            })
            response = send_request(url, headers=headers, data=payload)
            response_json = response.json()
            if response_json.get("code") == 0 and "tenant_access_token" in response_json:
                return response_json["tenant_access_token"]
            else:
                print(f"获取token返回错误: {response_json}")
                raise Exception(f"获取访问令牌失败: {response_json}")
        except Exception as e:
            print(f"获取访问令牌时发生错误: {e}")
            raise

    def get_spreadsheet_info(self):
        """
        获取电子表格的基息 (v3 API)
        
        Returns:
            dict: 包含电子表格信息的字典，包括：
                - title: 表格标题
                - owner: 所有者信息
                - create_time: 创建时间
                - modify_time: 修改时间
                - sheets: 子表信息
        """
        try:
            # 使用v3版本的API endpoint
            url = f"{BASE_URL}/sheets/v3/spreadsheets/{self.sheet_token}"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            response = send_request(url, headers=headers)
            response_json = response.json()
            
            if response_json.get("code") != 0:
                logger.error(f"获取电子表格信息失败: {response_json}")
                return None
                
            spreadsheet = response_json.get('data', {}).get('spreadsheet', {})
            
            # 提取v3 API返回的关键信息
            info = {
                "title": spreadsheet.get("title"),
                "owner": {
                    "id": spreadsheet.get("owner", {}).get("id"),
                    "name": spreadsheet.get("owner", {}).get("name")
                },
                "create_time": spreadsheet.get("create_time"),
                "modify_time": spreadsheet.get("modify_time"),
                "sheets": [{
                    "title": sheet.get("title"),
                    "sheet_id": sheet.get("sheet_id"),
                    "index": sheet.get("index")
                } for sheet in spreadsheet.get("sheets", [])]
            }
            
            logger.info(f"成功获取电子表格信息: {info['title']}")
            logger.info(f"创建时间: {info['create_time']}")
            logger.info(f"最后修改时间: {info['modify_time']}")
            logger.info(f"包含 {len(info['sheets'])} 个子表")
            
            return info
            
        except Exception as e:
            logger.error(f"获取电子表格信息时发生错误: {e}")
            return None

    def get_sheets_info(self):
        """
        获取电子表格中所有工作表的详细信息 (v3 API)
        
        Returns:
            list: 包含所有工作表信息的列表，每个工作表包含：
                - sheet_id: 工作表ID
                - title: 工作表标题
                - index: 工作表索引
                - row_count: 行数
                - column_count: 列数
                - frozen_row_count: 冻结行数
                - frozen_column_count: 冻结列数
                等其他元数据
        """
        try:
            url = f"{BASE_URL}/sheets/v3/spreadsheets/{self.sheet_token}/sheets/query"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            response = send_request(url, headers=headers)
            response_json = response.json()
            
            if response_json.get("code") != 0:
                logger.error(f"获取工作表信息失败: {response_json}")
                return None
                
            sheets = response_json.get('data', {}).get('sheets', [])
            
            # 处理每个工作表的信息
            sheets_info = []
            for sheet in sheets:
                sheet_info = {
                    "sheet_id": sheet.get("sheet_id"),
                    "title": sheet.get("title"),
                    "index": sheet.get("index"),
                    "hidden": sheet.get("hidden", False),
                    "grid_properties": {
                        "row_count": sheet.get("row_count", 0),
                        "column_count": sheet.get("column_count", 0),
                        "frozen_row_count": sheet.get("frozen_row_count", 0),
                        "frozen_column_count": sheet.get("frozen_column_count", 0)
                    },
                    "protection": sheet.get("protection", {}),
                    "status": sheet.get("status", "NORMAL")
                }
                sheets_info.append(sheet_info)
                
                # 记录每个工作表的基本信息
                logger.info(f"工作表: {sheet_info['title']}")
                logger.info(f"  - 行数: {sheet_info['grid_properties']['row_count']}")
                logger.info(f"  - 列数: {sheet_info['grid_properties']['column_count']}")
            # 打印每个工作表的sheet_id信息
            for sheet_info in sheets_info:
                logger.info(f"  - Sheet ID: {sheet_info['sheet_id']}")
            logger.info(f"成功获取 {len(sheets_info)} 个工作表的信息")
            return sheets_info
            
        except Exception as e:
            logger.error(f"获取工作表信息时发生错误: {e}")
            return None

    def get_ranges_values(self, ranges):
        """
        批量获取多个范围的值
        
        Args:
            ranges (list): 要获取的范围列表，例如 ["Sheet1!A1:B2", "Sheet2!C3:D4"]
            
        Returns:
            dict: 包含所有范围数据的字典，格式为：
                {
                    "range1": [[value1, value2], [value3, value4]],
                    "range2": [[value5, value6], [value7, value8]]
                }
        """
        try:
            # 将范围列表转换为URL参数格式
            ranges_param = "&ranges=".join(ranges)
            url = f"{SHEETS_URL}/{self.sheet_token}/values_batch_get?ranges={ranges_param}"
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            response = send_request(url, headers=headers)
            response_json = response.json()
            
            if response_json.get("code") != 0:
                if response_json.get("code") == 99991663:  # token过期
                    self.access_token = self.get_access_token()
                    headers["Authorization"] = f"Bearer {self.access_token}"
                    response = send_request(url, headers=headers)
                    response_json = response.json()
                else:
                    logger.error(f"获取范围值失败: {response_json}")
                    return None
            
            # 处理返回的数据
            values_ranges = response_json.get('data', {}).get('valueRanges', [])
            result = {}
            
            for value_range in values_ranges:
                range_name = value_range.get('range', '')
                values = value_range.get('values', [])
                result[range_name] = values
                
                # 记录每个范围的数据统计
                row_count = len(values)
                col_count = len(values[0]) if values and len(values) > 0 else 0
                logger.info(f"范围 {range_name} 获取到 {row_count} 行 {col_count} 列的数据")
            
            return result
            
        except Exception as e:
            logger.error(f"批量获取范围值时发生错误: {e}")
            return None

    def get_range_values(self, sheet_id, range_address, value_render_option="ToString", datetime_render_option="FormattedString"):
        """
        获取单个范围的值
        
        Args:
            sheet_id (str): 工作表ID，例如 "Sheet1"
            range_address (str): 范围地址，例如 "A1:B2"
            value_render_option (str): 值渲染选项，可选值：
                - "ToString": 将所有值转换为字符串
                - "FormattedValue": 返回格式化后的值
                - "UnformattedValue": 返回未格式化的值
            datetime_render_option (str): 日期时间渲染选项，可选值：
                - "FormattedString": 返回格式化的字符串
                - "Serial": 返回序列号
        
        Returns:
            list: 包含范围数据的二维列表
        """
        try:
            # 构建完整的范围标识符
            full_range = f"{sheet_id}!{range_address}"
            
            # 构建URL和查询参数
            params = {
                "valueRenderOption": value_render_option,
                "dateTimeRenderOption": datetime_render_option
            }
            param_str = "&".join([f"{k}={v}" for k, v in params.items()])
            url = f"{SHEETS_URL}/{self.sheet_token}/values/{full_range}?{param_str}"
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            response = send_request(url, headers=headers)
            response_json = response.json()
            
            if response_json.get("code") != 0:
                if response_json.get("code") == 99991663:  # token过期
                    self.access_token = self.get_access_token()
                    headers["Authorization"] = f"Bearer {self.access_token}"
                    response = send_request(url, headers=headers)
                    response_json = response.json()
                else:
                    logger.error(f"获取范围值失败: {response_json}")
                    return None
            
            # 获取值
            values = response_json.get('data', {}).get('valueRange', {}).get('values', [])
            
            # 记录数据统计
            row_count = len(values)
            col_count = len(values[0]) if values and len(values) > 0 else 0
            logger.info(f"范围 {full_range} 获取到 {row_count} 行 {col_count} 列的数据")
            
            return values
            
        except Exception as e:
            logger.error(f"获取范围值时发生错误: {e}")
            return None

    def update_range_values(self, sheet_id, range_address, values):
        """
        向单个范围写入数据
        
        Args:
            sheet_id (str): 工作表ID，例如 "Sheet1"
            range_address (str): 范围地址，例如 "A1:B2"
            values (list): 要写入的二维数据列表，例如 [["Hello", 1], ["World", 1]]
        
        Returns:
            dict: 写入结果信息，包含更新的范围和更新的行数等信息
        """
        try:
            # 构建完整的范围标识符
            full_range = f"{sheet_id}!{range_address}"
            
            url = f"{SHEETS_URL}/{self.sheet_token}/values"
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            # 构建请求体
            payload = {
                "valueRange": {
                    "range": full_range,
                    "values": values
                }
            }
            
            # 发送PUT请求
            response = requests.put(url, headers=headers, json=payload)
            response_json = response.json()
            
            if response_json.get("code") != 0:
                if response_json.get("code") == 99991663:  # token过期
                    self.access_token = self.get_access_token()
                    headers["Authorization"] = f"Bearer {self.access_token}"
                    response = requests.put(url, headers=headers, json=payload)
                    response_json = response.json()
                else:
                    logger.error(f"写入数据失败: {response_json}")
                    return None
            
            # 处理成功响应
            update_info = {
                "updated_range": full_range,
                "updated_rows": len(values),
                "updated_columns": len(values[0]) if values and len(values) > 0 else 0,
                "updated_cells": len(values) * (len(values[0]) if values and len(values) > 0 else 0)
            }
            
            logger.info(f"成功写入数据到范围 {full_range}")
            logger.info(f"更新了 {update_info['updated_rows']} 行 {update_info['updated_columns']} 列")
            
            return update_info
            
        except Exception as e:
            logger.error(f"写入范围值时发生错误: {e}")
            return None

def main():
    try:
        feishu_api = FeishuAPI()
        
        # 获取电子表格信息
        logger.info("正在获取电子表格信息...")
        spreadsheet_info = feishu_api.get_spreadsheet_info()
        if spreadsheet_info:
            logger.info(f"电子表格标题: {spreadsheet_info['title']}")
            logger.info(f"包含 {len(spreadsheet_info['sheets'])} 个子表")
        
        # 获取工作表详细信息
        logger.info("正在获取工作表详细信息...")
        sheets_info = feishu_api.get_sheets_info()
        if sheets_info:
            logger.info(f"成功获取工作表详细信息")
        
        
        # 构建测试范围列表
        test_ranges = []
        for sheet_info in sheets_info:
            sheet_id = sheet_info.get('sheet_id', '')
            if sheet_id:
                test_ranges.append(f"{sheet_id}!A1:G10")  # 使用实际的sheet_id构建范围

        # 测试批量获取多个范围的值
        logger.info("正在批量获取多个范围的值...")
        ranges_values = feishu_api.get_ranges_values(test_ranges)
        if ranges_values:
            logger.info(f"成功获取 {len(ranges_values)} 个范围的数据")
            
        # 测试获取单个范围的值
        logger.info("正在获取单个范围的值...")
        if sheets_info and len(sheets_info) > 0:
            test_sheet_id = sheets_info[0].get('sheet_id')  # 获取第一个sheet的ID
            test_range = "A1:D10"  # 测试范围
            
            # 测试不同的渲染选项
            # 1. 默认选项
            values_default = feishu_api.get_range_values(test_sheet_id, test_range)
            logger.info("默认渲染选项的结果获取完成")
            
            # 2. 使用FormattedValue选项
            values_formatted = feishu_api.get_range_values(
                test_sheet_id, 
                test_range,
                value_render_option="FormattedValue"
            )
            logger.info("格式化值渲染选项的结果获取完成")
        
        # 测试写入数据到单个范围
        logger.info("正在测试写入数据...")
        if sheets_info and len(sheets_info) > 0:
            test_sheet_id = sheets_info[0].get('sheet_id')  # 获取第一个sheet的ID
            test_range = "A1:B2"  # 测试范围
            test_values = [
                ["Hello", 1],
                ["World", 2]
            ]
            
            # 写入测试数据
            update_result = feishu_api.update_range_values(
                test_sheet_id,
                test_range,
                test_values
            )
            
            if update_result:
                logger.info("数据写入成功")
                
                # 验证写入的数据
                verification_values = feishu_api.get_range_values(
                    test_sheet_id,
                    test_range,
                    value_render_option="FormattedValue"
                )
                if verification_values:
                    logger.info("数据验证完成")
        
        return {
            "spreadsheet_info": spreadsheet_info,
            "sheets_info": sheets_info,
            "ranges_values": ranges_values,
            "single_range_values": {
                "default": values_default,
                "formatted": values_formatted
            },
            "update_result": update_result,
            "verification_values": verification_values
        }
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        return None

if __name__ == "__main__":
    result = main()
    if result:
        logger.info("程序执行成功")
    else:
        logger.error("程序执行失败")