# 列出模型
import PIL.Image
import google.generativeai as genai
def demo_models():
    genai.configure(api_key="AIzaSyCeKS2pgRzEmfhhLprx0YNaHIkjiNFxL44")
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            print(m.name)  


def demo_duihua():
    genai.configure(api_key="AIzaSyCeKS2pgRzEmfhhLprx0YNaHIkjiNFxL44")

    context = """你是一个30岁左右的程序员，下面的乙这个角色，正在跟朋友聊天，
    请根据已有的聊天内容，产生对应的对话内容，要求：
    1. 字数不要超过30个字；
    2. 如果没有合适的回复内容，给上一条回复给出点赞的话语；
    3. 产生对话内容，只要内容，不用出现'乙：'这样的说明作者的部分。

    下面三个波浪线包含的部分是聊天历史内容：
    ~~~
    甲：你好！最近怎么样？
    乙：我很好，谢谢。你呢？
    甲：我也不错。最近在忙些什么？
    乙：我在学习新的编程语言，挺有趣的。你呢？
    甲：我最近在准备一个旅行计划。
    乙：哦，听起来很棒！你打算去哪里？
    甲：我打算去日本。一直很想去那里看樱花。
    ~~~
    """

    model = genai.GenerativeModel('gemini-pro')
    response = model.generate_content(context)

    print(response.text)




def demo_picture():
    genai.configure(api_key="AIzaSyCeKS2pgRzEmfhhLprx0YNaHIkjiNFxL44")


    img = PIL.Image.open('/Users/<USER>/Downloads/01.png')

    context = """请给这张图，以资深解说员的角度，产生一段解说评论，要足够生动、吸引人。"""

    model = genai.GenerativeModel('gemini-pro-vision')
    response = model.generate_content([context,img])

    print(response.text)
