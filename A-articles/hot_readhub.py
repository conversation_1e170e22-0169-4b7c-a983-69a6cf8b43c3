import requests
from bs4 import BeautifulSoup
from datetime import datetime,date
import json
import os
import re

from lxml import etree
import requests
from util import notion_util
from util import ids_util
from dotenv import load_dotenv
load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_MRRS = os.environ.get("NOTION_DATABASE_MRRS")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_MRRS)



def hot_topics():
    # 定义请求的 URL
    url = "https://readhub.cn/hot"

    # 设置请求头以模拟浏览器行为
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}

    # 发送 GET 请求
    response = requests.get(url, headers=headers)

    # 检查请求是否成功
    if response.status_code == 200:
        # 获取 HTML 内容
        html_content = response.text
        
        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找包含热点的 <a> 元素
        hot_topics_div = soup.findAll('a', {'class': 'style_link__cAS7L style_hover__GhYRw'})
        hot_topics_span = soup.findAll('span', {'class': 'style_t__Y5xTJ'})
        
        if hot_topics_div:
            datas=[]
            for i, item in enumerate(hot_topics_div):
                try:
                    title = item['title']
                    href = item['href']
                    time = hot_topics_span[i].text.strip() if i < len(hot_topics_span) else "N/A"
                    print(f"Title: {title}\nHref: {href}\nTime: {time}\n")
                    if i==29: break  #前三十个 24小时热榜,后面是一周热榜
                    data={
                        "title":title,
                        "href":href,
                        "time":time,
                        'url':'https://readhub.cn'+href,
                        'hot':i+1
                    }
                    datas.append(data)
                except Exception as e:
                    print(f"Error processing item: {e}")
            return datas
        else:
            print("未找到包含热点的 <a> 元素")
    else:
        print(f"请求失败，状态码: {response.status_code}")


def article_detail():
    url = "https://readhub.cn/topic/8cEtXEUzEDp"

    # 设置请求头以模拟浏览器行为
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}

    # 发送 GET 请求
    response = requests.get(url, headers=headers)
      # 检查请求是否成功
    if response.status_code == 200:
        # 获取 HTML 内容
        html_content = response.text
        
        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        html_content = soup.find('p', {'class': 'style_p__UvOGE'})
        print(html_content.text)

        # 链接
        a_links = soup.findAll('a', {'class': 'style_name__RY79Y'})
        for a_link in a_links:
            print(a_link['href'])
    else:
        print(f"请求失败，状态码: {response.status_code}")
        

def saveNotion(rank):
    today = date.today()
    ids=ids_util.get_exist_ids('hot-news.json')
    for data in rank:
        id=data['href']
        if id not in ids:
            title = data['title']
            hotValue=data['hot']
            url=data['url']
            page = {
                'id': id,
                "title": title,
                'datasource': 'ReadHub',
                'hmcturl': url,
                'hot': hotValue,
                'published_at': str(today),
            }
            newPage = notion.create_page(page=page)
            page_id = newPage['id']
            ids_util.save_ids(title, id, 'success','hot-news.json')
            print(f'Save Notion: ID:{id},pageId: {page_id},title: {title}')

def main():
    datas=hot_topics()
    saveNotion(datas)

if __name__ == '__main__':
    main()
    # datas=hot_topics()
    # saveNotion(datas)
# article_detail()