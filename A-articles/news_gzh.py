import re
import requests,time,json
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta
import logging
logger = logging.getLogger(__name__)

def get_weixin_content(url):
    """
    Retrieves the title, timestamp, text content, and images from a WeChat article.

    Args:
        url (str): The URL of the WeChat article.

    Returns:
        tuple: A tuple containing the title (str), timestamp (str), text content (str), and a list of image URLs.
    """
    response = requests.get(url)
    soup = BeautifulSoup(response.text, "html.parser")

    # Get the title
    title_elem = soup.find('h1')
    if title_elem is None:
        print(f'Error: Unable to open {url}')
        return None, None, None, None
    weixin_title = title_elem.string.strip()

    # Get the timestamp
    time_pattern = r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}'
    weixin_time = re.findall(time_pattern, response.text)
    weixin_time = weixin_time[0] if weixin_time else None

    # Get the content HTML and extract text and images
    content_elem = soup.find(id='js_content')
    if content_elem:
        content_soup = BeautifulSoup(str(content_elem), "html.parser")
        content_soup.div['style'] = 'visibility: visible;'
        html = str(content_soup)
        text_content = content_elem.get_text()
        image_pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
        image_urls = re.findall(image_pattern, html)
    else:
        text_content = ''
        image_urls = []

    print(f'Title: {weixin_title}')
    print(f'Time: {weixin_time}')
    print(f'Content: {text_content}')
    print(f'Image URLs: {image_urls}')
    return weixin_title, weixin_time, text_content, image_urls



class Spider:
    def timestamp_to_datetime(self,timestamp):
        """
        将时间戳转换为年月日时分秒格式

        :param timestamp: 时间戳（以秒为单位）
        :return: 格式化后的日期时间字符串
        """
        dt_object = datetime.fromtimestamp(timestamp)
        formatted_date = dt_object.strftime('%Y-%m-%d %H:%M:%S')
        return formatted_date
        
    def parse_url_mid(self,url:str):
        # 解析URL
        parsed_url = urlparse(url)

        # 解析查询字符串
        query_params = parse_qs(parsed_url.query)

        # 获取mid参数的值
        mid_value = query_params.get('mid', [''])[0]
        return mid_value

    def _get_articles_byWeRead(self, bookId: str, accessToken: str, offset: None):
        # time.sleep(10)
        count = 20
        if offset is None: offset = 0
        url = f'https://i.weread.qq.com/book/articles?offset={offset}&count={count}&bookId={bookId}'
        headers = {
            # 微信用户登录信息
            'accessToken': accessToken,
            'vid': '27656795'
        }
        res = requests.get(url=url, headers=headers,timeout=120)  #
        if res.status_code == 200:
            data = json.loads(res.text)
            reviews = data.get('reviews', [])  # 使用 get 方法提供默认值 []
            results = []
            for review in reviews:
                review_info = review['review']
                # 提取mpInfo字段
                mp_info = review_info['mpInfo']
                mp_info['time']
                # 时间戳转变年月日时分秒
                publish_date = self.timestamp_to_datetime(mp_info['time'])
                result = {
                    'link': mp_info['doc_url'],
                    'title': mp_info['title'],
                    'author': mp_info['mp_name'],
                    'avatar': mp_info['avatar'],
                    'create_time': publish_date,
                    'content': mp_info['content'],
                    'originalId': mp_info['originalId'],
                    'pic_url': mp_info['pic_url'],
                }
                results.append(result)
            # 保存到数据库
            return results
        else:
            logger.error(f'微信阅读获取文章-调用失败:{res.text}')
        return None

    def _get_author_bookId(self, gzh_name: str, accessToken: str):
        """获取公众号bookid"""
        count = 5
        url = f'https://i.weread.qq.com/store/search?offset=0&count={count}&scope=2&keyword={gzh_name}'
        headers = {
            # 微信用户登录信息
            'accessToken': accessToken,
            'vid': '27656795'
        }
        res = requests.get(url=url, headers=headers)  #
        if res.status_code == 200:
            data = json.loads(res.text)
            books = data['books']
            for book in books:
                book_info = book['bookInfo']
                title = book_info['title']
                bookId = book_info['bookId']
                print("Book ID:", book_info['bookId'])
                print("Title:", book_info['title'])
                print("Author:", book_info['author'])
                print("Cover:", book_info['cover'])
                if '公众号' == book_info['author'] and book_info['title'] == gzh_name:
                    logger.info(f'公众号:{title},bookid:{bookId}')
                    return bookId
        elif res.status_code == 401:
            return 401
        return None

    def _weiXin_read_login(self):
        """阅读登录获取token"""
        url = 'https://i.weread.qq.com/login'  # 请将此URL替换为实际的API端点
        # 定义请求体
        data = {
            "deviceId": "3355953252939592923552792896",
            "deviceName": "other",
            "inBackground": 0,
            "kickType": 1,
            "random": 525,
            "refCgi": "",
            "refreshToken": "onb3MjoXW-oc_LJmXHblnqCkTdnk@WgerrJa5QHKB0zyFcvMQVQAA",
            "signature": "cd70ce1ce2318c770c51a67806169e1029946a707aaaf1672dfdc46e2e80bd33",
            "timestamp": 1721296479751,
            "trackId": "",
            "virtualChannelId": "",
            "wxToken": 0
        }
        response = requests.post(url, json=data)
        if response.status_code == 401:
            data = json.loads(response.text)
            print(data['errmsg'])
            if '鉴权失败'==data['errmsg']:
                return None
            accessToken = data['accessToken']
            return accessToken
        else:
            return None

    def _get_articles(self,accessToken,global_gzh_name):
        # accessToken = self._weiXin_read_login()
        book_id = self._get_author_bookId(gzh_name=global_gzh_name, accessToken=accessToken)  # 获取作者bookid
        if book_id is None: return
        articleList = self._get_articles_byWeRead(bookId=book_id, accessToken=accessToken,offset=None)
        urls=[]
        for item in articleList:
            link = item['link']
            title = item['title']
            create_time=item['create_time']
            originalId = item['originalId']
            mid = self.parse_url_mid(item['link'])
            url = f'https://mp.weixin.qq.com/s/{originalId}'
            print(url)
            urls.append(url)
        return urls




def main():
    #todo: 用户主页文章列表
    spider =Spider()
    accessToken=spider._weiXin_read_login()
    if accessToken is None:
        accessToken = 'Vr1if1v7' # 需要手动扫码登录,获取accessToken 或者skey 
    urls=spider._get_articles(accessToken,'量子位')

    for url in urls:
        print(get_weixin_content(url))
        # print(get_weixin_content('https://mp.weixin.qq.com/s/m-swZvyOJFzA9tod55Mqqg'))
        
if __name__ == '__main__':
    # main()
    get_weixin_content('https://mp.weixin.qq.com/s?__biz=MzkyOTY4NjMxMA%3D%3D&mid=2247488644&idx=1&sn=87fd3383477869343795e481bfd37969&chksm=c3e0db77bb02bb0bfbbc5f6d7fd0df59a566950c8f9642a9ca847b8a8e6c21facd4b1bac6539&scene=126&sessionid=0#rd')

