from datetime import datetime
import re

# 获取当前日期
current_date = datetime.now().date()

# 定义正则表达式模式
year_pattern = re.compile(r'\d{4}-\d{2}-\d{2}')
no_year_pattern = re.compile(r'\d{2}-\d{2}')

# 检查日期字符串是否包含年份
def has_year(date_str):
    return bool(year_pattern.match(date_str))

# 解析日期字符串
def parse_date(date_str):
    if has_year(date_str):
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    else:
        date_obj = datetime.strptime(date_str, '%m-%d').date()
        return date_obj.replace(year=current_date.year)

# 判断日期是否小于当前日期
def is_past_date(date_str):
    try:
        date_obj = parse_date(date_str)
        return date_obj < current_date
    except ValueError:
        return False

# # 测试日期字符串
# date_str1 = '08-30'
# date_str2 = '2022-12-24'

# # 判断并输出结果
# if is_past_date(date_str1):
#     print(f'{date_str1} 小于当前日期')
# else:
#     print(f'{date_str1} 不小于当前日期')

# if is_past_date(date_str2):
#     print(f'{date_str2} 小于当前日期')
# else:
#     print(f'{date_str2} 不小于当前日期')