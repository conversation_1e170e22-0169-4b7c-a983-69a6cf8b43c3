import json
import os
def save_ids(title: str, hmctdocid: str, reseaon: str,file_path_json:str):
    file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', file_path_json)
    with open(file_path, 'a', encoding='utf-8') as f:
        try:
            insert_notion_data = {'id': hmctdocid, 'title': title, 'msg': reseaon}
            json.dump(insert_notion_data, f, ensure_ascii=False)
            f.write('\n')
            f.close()
        except Exception as e:
            print(f'保存Notion异常:{e}')
            f.close()

def get_exist_ids(file_path_json:str):
    # 根据ID校验是否已经保存过
    message_ids = set()
    file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', file_path_json)
    if os.path.exists(file_path) is False:
        print(f'文件不存在:{file_path}')
        return message_ids
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                json_data = json.loads(line)
                message_ids.add(json_data['id'])
            f.close()
    except Exception as e:
        print(f'更新异常:{e}')
        pass
    print(f'已保存{len(message_ids)}条数据')
    return message_ids
