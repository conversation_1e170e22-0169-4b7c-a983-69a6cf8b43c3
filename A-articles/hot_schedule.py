import schedule
import time
from hot_baidu import main as baidu_main
from hot_readhub import main as readhub_main
from hot_tth import main as tth_main
from hot_weibo import main as weibo_main
from hot_zhihu import main as zhihu_main
from hot_tengxun import main as tengxun_main
def job():
    baidu_main()
    readhub_main()
    tth_main()
    weibo_main()
    zhihu_main()
    tengxun_main()
    print('[热搜新闻采集程序结束时间: %s]' % time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


def schedule_job():
    schedule.every(3).hours.do(job)  # 一天六次
    # schedule.every().day.at("06:00").do(job)
    # schedule.every().day.at("12:00").do(job)
    # schedule.every().day.at("18:00").do(job)
    # schedule.every().day.at("00:00").do(job)

    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    print('[程序启动...热搜新闻采集]')
    schedule_job()
