"""头条热点榜"""
'''
1. 头条热榜-查询
'''
import requests
import pandas as pd
import re
import datetime

from datetime import datetime,date
import json
import os
import re

from lxml import etree
import requests
from util import notion_util
from util import ids_util
from dotenv import load_dotenv
load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_MRRS = os.environ.get("NOTION_DATABASE_MRRS")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_MRRS)



def get_hot_events():
    # 请求头
    h1 = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15',
    }
    url = 'https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc'
    # 发送请求
    r = requests.get(url, headers=h1)
    # 查看响应码
    print(r.status_code)
    # 接收返回数据
    json_data = r.json()


    id_list = []  # id列表
    title_list = []  # 热榜标题
    value_list = []  # 热度值
    url_list = []  # 热榜链接
    category_list = []  # 热榜分类

    for data in json_data['data']:
        # TODO: 财经和健康需要资质,不能发布. 政治新闻不能随便乱发. 
        # 唯一ID
        clusterId=data['ClusterId']
        print('唯一ID：', clusterId)
        id_list.append(clusterId)
        # 热榜标题
        title = data['Title']
        print('热榜标题：', title)
        title_list.append(title)
        # 热榜热度值
        hotValue=data['HotValue']
        print('热度值：', hotValue)
        value_list.append(hotValue)
        # URL 
        Url=data['Url']
        print('URL：', Url)
        url_list.append(Url)
        # 兴趣
        if 'InterestCategory' in data:
            InterestCategory=data['InterestCategory'][0]
            print('兴趣：', InterestCategory)
            category_list.append(InterestCategory)
        else:
            category_list.append('default')

    # 把列表数据组装成Dataframe数据
    data_num = len(json_data['data'])
    df = pd.DataFrame(
        {
            '热榜排名': range(1, data_num + 1),  # 一共50条
            'TopId': id_list,
            '热榜标题': title_list,
            '热度值': value_list,
            '热榜分类': category_list,
            '热榜链接': url_list,
        }
    )
    result_file='/Users/<USER>/Downloads/toutiao_hot_event-'+datetime.datetime.now().strftime('%Y-%m-%d-%H-%m')+'.csv'
    # 保存到csv文件
    df.to_csv(result_file, header=True, index=False, encoding='utf_8_sig')
    print('保存成功: ',result_file)
    return result_file
        
# 过滤数据
def get_hot_events_filter():
    # 请求头
    h1 = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15',
    }
    url = 'https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc'
    # 发送请求
    r = requests.get(url, headers=h1)
    # 查看响应码
    print(r.status_code)
    # 接收返回数据
    json_data = r.json()


    id_list = []  # id列表
    title_list = []  # 热榜标题
    value_list = []  # 热度值
    url_list = []  # 热榜链接
    category_list = []  # 热榜分类

    for data in json_data['data']:
        # 不要第一个-多为政治
        if data==json_data['data'][0]:
            continue

        # TODO: 财经和健康需要资质,不能发布. 政治新闻不能随便乱发. 
        # 唯一ID
        clusterId=data['ClusterId']
        print('唯一ID：', clusterId)
        id_list.append(clusterId)
        # 热榜标题
        title = data['Title']
        print('热榜标题：', title)
        # title_list.append(title)
        # # 热榜热度值
        # hotValue=data['HotValue']
        # print('热度值：', hotValue)
        # value_list.append(hotValue)
        # # URL 
        # Url=data['Url']
        # print('URL：', Url)
        # url_list.append(Url)
        # # 兴趣
        # if 'InterestCategory' in data:
        #     InterestCategory=data['InterestCategory'][0]
        #     print('兴趣：', InterestCategory)
        #     category_list.append(InterestCategory)
        # else:
        #     category_list.append('default')

        #过滤条件: 数据发布过, 数据是金融或健康
        return title
    

def request_data():
    # 请求头
    h1 = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15',
    }
    url = 'https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc'
    # 发送请求
    r = requests.get(url, headers=h1)
    # 查看响应码
    print(r.status_code)
    # 接收返回数据
    json_data = r.json()
    return json_data['data']

def saveNotion(rank):
    today = date.today()
    ids=ids_util.get_exist_ids('hot-news.json')
    for data in rank[3:]:
        clusterId=data['ClusterId']
        if clusterId not in ids:
            title = data['Title']
            hotValue=data['HotValue']
            url=data['Url']
            img_url=data['Image']['url']
            # 兴趣
            if 'InterestCategory' in data:
                InterestCategory=data['InterestCategory'][0]
                print('兴趣：', InterestCategory)
            
            page = {
                'id': clusterId,
                "title": title,
                'datasource': '头条',
                'hmcturl': url,
                'hot': hotValue,
                'published_at': str(today),
            }
            # newPage = notion.create_page(page=page)
            # page_id = newPage['id']
            # ids_util.save_ids(title, clusterId, 'success','hot-news.json')
            # print(f'Save Notion: ID:{clusterId},pageId: {page_id},title: {title}')

def main():
    data = request_data()
    saveNotion(data)

if __name__ == '__main__':
    main()