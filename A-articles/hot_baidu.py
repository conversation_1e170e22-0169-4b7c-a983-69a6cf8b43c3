"""百度热搜"""
import requests
import os
import pandas as pd
from datetime import datetime, timedelta
from datetime import date
import json
import re
from lxml import etree
from dotenv import load_dotenv
from util import notion_util, ids_util

# 加载环境变量
load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_MRRS = os.environ.get("NOTION_DATABASE_MRRS")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_MRRS)

def get_baidu_hot():
    """获取百度热搜数据并保存为CSV和HTML文件"""
    url = 'https://top.baidu.com/api/board?platform=wise&tab=realtime'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Mobile Safari/537.36',
        'Host': 'top.baidu.com',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://top.baidu.com/board?tab=novel',
    }

    response = requests.get(url, headers=headers)
    json_data = response.json()

    top_content_list = json_data['data']['cards'][0]['topContent']
    content_list = json_data['data']['cards'][0]['content']

    title_list, order_list, score_list, descr_list, url_list, img_list = [], [], [], [], [], []

    for item in top_content_list:
        title_list.append(item['word'])
        order_list.append(item['index'])
        score_list.append(item['hotScore'])
        descr_list.append(item['desc'])
        url_list.append(item['url'])
        img_list.append(item['img'])

    for item in content_list:
        title_list.append(item['word'])
        order_list.append(item['index'] + len(top_content_list))
        score_list.append(item['hotScore'])
        descr_list.append(item['desc'])
        url_list.append(item['url'])
        img_list.append(item['img'])

    beijing_time = datetime.utcnow() + timedelta(hours=8)
    year = beijing_time.strftime('%Y')
    month_day = beijing_time.strftime('%m%d')
    minute = beijing_time.hour * 60 + beijing_time.minute
    path = os.path.join('/Users/<USER>/Downloads', year, month_day)

    os.makedirs(path, exist_ok=True)

    df = pd.DataFrame({
        '热搜排名': order_list,
        '热搜标题': title_list,
        '描述': descr_list,
        '热搜指数': score_list,
        '链接地址': url_list,
        '封图链接': img_list
    })

    df.to_csv(os.path.join(path, f'百度热搜榜.{minute}.csv'), index=False)
    df.to_html(os.path.join(path, f'百度热搜榜.{minute}.html'), index=False)

def save_notion(rank):
    """将热搜数据保存到Notion"""
    today = date.today()
    ids = ids_util.get_exist_ids('hot-news.json')
    for item in rank:
        item_id = item['query']
        if item_id not in ids:
            title = item['word']
            page = {
                'id': item_id,
                "title": title,
                'datasource': '百度',
                'hmcturl': item['url'],
                'hot': item['hotScore'],
                'published_at': str(today),
            }
            new_page = notion.create_page(page=page)
            page_id = new_page['id']
            ids_util.save_ids(title, item_id, 'success', 'hot-news.json')
            print(f'Save Notion: ID:{item_id}, pageId: {page_id}, title: {title}')

def request_data():
    """请求百度热搜数据"""
    url = 'https://top.baidu.com/api/board?platform=wise&tab=realtime'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Mobile Safari/537.36',
        'Host': 'top.baidu.com',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://top.baidu.com/board?tab=novel',
    }

    response = requests.get(url, headers=headers)
    json_data = response.json()
    return json_data['data']['cards'][0]['content']

def main():
    """主函数"""
    content_list = request_data()
    save_notion(content_list)

if __name__ == '__main__':
    main()