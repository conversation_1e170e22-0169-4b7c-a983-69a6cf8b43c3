import requests,json
from bs4 import <PERSON><PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>
from base64 import b64decode
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

AES_KEY = b"www.sohu.com6666"

def decrypt_aes(encrypted_data: bytes, key: bytes) -> str:
    """
    Decrypts the given AES-encrypted data using the provided key.

    Args:
        encrypted_data (bytes): The encrypted data to be decrypted.
        key (bytes): The AES key used for decryption.

    Returns:
        str: The decrypted data as a string.
    """
    cipher = AES.new(key, AES.MODE_ECB)
    decrypted_padded_bytes = cipher.decrypt(encrypted_data)
    decrypted_bytes = unpad(decrypted_padded_bytes, AES.block_size)
    return decrypted_bytes.decode('utf-8')

def toutiao_parse_content_images(url: str) -> <PERSON><PERSON>[list, str]:
    """
    Parses the content and extracts images from a Toutiao article URL.

    Args:
        url (str): The URL of the Toutiao article.

    Returns:
        Tuple[list, str]: A tuple containing a list of image URLs and the article text.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    with requests.get(url, headers=headers) as response:
        soup = BeautifulSoup(response.text, 'lxml')
        article_content_div = soup.select_one('.article')
        div_text = article_content_div.get_text()
        image_urls = []
        for img_tag in article_content_div.find_all('img'):
            if 'data-src' in img_tag.attrs:
                encrypted_url = img_tag['data-src']
                img_url = decrypt_aes(b64decode(encrypted_url), AES_KEY)
                image_urls.append(img_url)

    return image_urls, div_text

def author_home_list(page:int,size:int):
    """
    1. pvId,pageId,mkey,每个用户的不同参数
    2. 可以只变更 page数值,获取下一页内容, size控制返回数量
    """
    url = "https://odin.sohu.com/odin/api/blockdata"
    headers = {
        'Content-Type': 'application/json'
    }

    data = {
        "pvId": "1724403358527_7wDZ0Lp", #量子位
        "pageId": "1724403546972_24051621055_C4J",
        "mainContent": {
            "productType": "13",
            "productId": "324",
            "secureScore": "5",
            "categoryId": "47",
            "adTags": "11111111",
            "authorId": 121135924
        },
        "resourceList": [
            {
                "tplCompKey": "FeedSlideloadAuthor_2_0_pc_1655965929143_data2",
                "isServerRender": False,
                "isSingleAd": False,
                "configSource": "mp",
                "content": {
                    "productId": "325",
                    "productType": "13",
                    "size": 50,
                    "pro": "0,1,3,4,5",
                    "feedType": "XTOPIC_SYNTHETICAL",
                    "view": "operateFeedMode",
                    "innerTag": "work",
                    "spm": "smpc.channel_248.block3_308_hHsK47_2_fd",
                    "page": 1,
                    "requestId": "1724403358898lAnqmi6_324"
                },
                "adInfo": {},
                "context": {
                    "mkey": "610300"
                }
            }
        ]
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        print("请求成功:", response.text)
        data = json.loads(response.text)
        data_list = data['data']['FeedSlideloadAuthor_2_0_pc_1655965929143_data2']['list']
        return data_list
    else:
        print("请求失败:", response.status_code)
    return None


if __name__ == '__main__':
    """Get the content and images from a Toutiao article"""

    data_list=author_home_list(page=1,size=20)
    for item in data_list:
        url=item['url']
        print(item['title'])
        print(item['url'])
        print(item['id'])
        print(item['cover'])
        print(item['extraInfoList'][0]['text'])

        """根据主页获取列表-再获取文章详情内容"""
        article_url=f'https://www.sohu.com{url}'
        # article_url = 'https://www.sohu.com/a/802321462_260616?scm=10001.325_13-109000.0.10140.5_32'
        picture_urls, div_text = toutiao_parse_content_images(article_url)
        print(f"Picture URLs: {picture_urls}")
        print(f"Article Text: {div_text}")