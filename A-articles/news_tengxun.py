
import requests
import json
import re
from bs4 import BeautifulSoup

def get_news_list(url):
    """获取腾讯主页新闻列表"""
    response = requests.get(url)
    data = json.loads(response.text)
    news_list = []
    
    # 解析JSON数据,提取id、url和标题
    if 'newslist' in data:
        for item in data['newslist']:
            news_item = {
                'id': item.get('id'),
                'title': item.get('title'),
                'url': item.get('url'),
                'comments': item.get('comments'),
                'time': item.get('time')
            }
            news_list.append(news_item)
    offsetInfo=data['offsetInfo']
    return news_list,offsetInfo

def get_article_content(url):
    """获取文章详细内容"""
    response = requests.get(url)
    script_content=response.text
    # 使用正则表达式提取JSON内容
    match = re.search(r'window\.DATA\s*=\s*(\{.*?\});', script_content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            data = json.loads(json_str)
            text_content = data['originContent']['text']
            # 使用BeautifulSoup解析HTML内容
            soup = BeautifulSoup(text_content, 'html.parser')
            all_text = soup.get_text(separator='\n')
            print(all_text)

            # 提取originAttribute中的列表-图片列表
            origin_attribute_dict = data.get('originAttribute', [])
            origurl_objects = []
            for key, value in origin_attribute_dict.items():
                if 'origUrl' in value:
                    origurl_objects.append(value['origUrl'])
            
            print(origurl_objects)

            return {
                # 'title': title_text,
                'text': all_text,
                'images': origurl_objects,
                'links': url
            }

        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
    else:
        print("未找到JSON内容")
    return None




def main():
    """获取腾讯用户主页新闻列表"""
    """根据主页获取列表-再获取文章详情内容-TODO-下一页查询"""
    author='8QIf3n9d6oIcsDja5gM%3D' #大秦壁虎
    offset_info=''
    for i in range(1, 10):
        print(f'Page {i}')
    
        url = f"https://i.news.qq.com/getSubNewsMixedList?offset_info={offset_info}&guestSuid={author}&tabId=om_article&caller=1&from_scene=103"
        news_list,offset_info = get_news_list(url)
        print(f'Total {len(news_list)} items')

        for news in news_list:
            content = get_article_content(news['url'])
            print(content)

  
main()