import requests
from bs4 import BeautifulSoup
from datetime import datetime,date
import json
import os
import re

from lxml import etree
import requests
from util import notion_util
from util import ids_util
from dotenv import load_dotenv
load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_MRRS = os.environ.get("NOTION_DATABASE_MRRS")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_MRRS)


def get_hot_events():
    # 请求头
    h1 = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15',
    }
    url = 'https://r.inews.qq.com/gw/event/hot_ranking_list?page_size=50'
    # 发送请求
    response = requests.get(url)
    json_data = response.json()
    return json_data['idlist'][0]['newslist']
    

def saveNotion(rank):
    ids=ids_util.get_exist_ids('hot-news.json')
    for i,value in enumerate(rank):
        if i<2:continue
        id=value['id']
        if id not in ids:
            title=value['title']
            published_at=datetime.strptime(value['time'], "%Y-%m-%d %H:%M:%S").date().__str__()
            page = {
                'id': id,
                "title": title,
                'datasource': '腾讯',
                'hmcturl': value['url'],
                'hot': value['hotEvent']['hotScore'],
                'published_at': published_at, #带时间,Notion自增八小时
            }
            newPage = notion.create_page(page=page)
            page_id = newPage['id']
            ids_util.save_ids(title, id, 'success','hot-news.json')
            print(f'Save Notion: ID:{id},pageId: {page_id},title: {title}')
        

def main():
    rank=get_hot_events()
    saveNotion(rank)

if __name__ == '__main__':
    main()
