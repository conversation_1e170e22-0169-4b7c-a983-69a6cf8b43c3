import requests
from bs4 import <PERSON><PERSON>ou<PERSON>
from typing import <PERSON><PERSON>, List, Dict, Any
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By

def parse_cookie_string(cookie_string: str) -> Dict[str, str]:
    """
    Parses a cookie string into a dictionary.

    Args:
        cookie_string (str): The cookie string.

    Returns:
        Dict[str, str]: A dictionary of cookie key-value pairs.
    """
    cookie_dict = {}
    for cookie_item in cookie_string.split(';'):
        if '=' in cookie_item:
            key, value = cookie_item.strip().split('=', 1)
            cookie_dict[key] = value
    return cookie_dict

def toutiao_parse_content_images(url: str) -> <PERSON><PERSON>[List[str], str]:
    """
    Parses the content and extracts images from a Toutiao article URL.

    Args:
        url (str): The URL of the Toutiao article.

    Returns:
        Tuple[List[str], str]: A tuple containing a list of image URLs and the article text.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    cookie_string = 'your_cookie_string_here'
    cookie_dict = parse_cookie_string(cookie_string)

    with requests.get(url, headers=headers, cookies=cookie_dict) as response:
        soup = BeautifulSoup(response.text, 'lxml')
        article_content_div = soup.select_one('.article-content')
        if article_content_div:
            div_text = article_content_div.get_text()
            picture_urls = [img.get('src') for img in article_content_div.find_all('img')]
        else:
            div_text = ''
            picture_urls = []

    return picture_urls, div_text

def get_article_url(author_token_value: str, max_behot_time: str) -> Tuple[List[Dict[str, Any]], str]:
    """
    Fetches the article URLs from the user's homepage.

    Args:
        author_token_value (str): The token value of the author.
        max_behot_time (str): The max_behot_time for pagination.

    Returns:
        Tuple[List[Dict[str, Any]], str]: A tuple containing a list of article data and the next max_behot_time.
    """
    try:
        service = Service('/usr/local/bin/chromedriver')
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        driver = webdriver.Chrome(service=service, options=options)

        driver.get('file:////Users/<USER>/fuwenhao/Github/spider/A-articles/data/common.html')
        driver.execute_script(f"window.token = '{author_token_value}';")
        time.sleep(20)

        sig_tag = driver.find_element(By.ID, "sigUrl")
        url = sig_tag.text

        if author_token_value not in url:
            time.sleep(20)
            sig_tag = driver.find_element(By.ID, "sigUrl")
            url = sig_tag.text

        data = get_data(url, cookie_string)
        data_list = data.get('data', [])

        if not data_list:
            print('No data')
            return [], max_behot_time

        max_behot_time = data['next']['max_behot_time']
        return data_list, max_behot_time

    except Exception as e:
        print(f"Error: {e}")
        return [], max_behot_time

def get_data(url: str, cookie_string: str) -> Dict[str, Any]:
    """
    Fetches data from the given URL using the provided cookie string.

    Args:
        url (str): The URL to fetch data from.
        cookie_string (str): The cookie string to use for the request.

    Returns:
        Dict[str, Any]: The JSON data parsed from the response.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    cookie_dict = parse_cookie_string(cookie_string)
    response = requests.get(url, headers=headers, cookies=cookie_dict)
    data = json.loads(response.text)
    return data

cookie_string = 'tt_webid=7368796357774575142; PIXIEL_RATIO=1; FRM=new; WIN_WH=1183_995; _tea_utm_cache_24={%22utm_medium%22:%22search_ignore%22}; s_v_web_id=verify_lztiiyfc_9ReGKVL4_mRDf_4Yaa_BCZ6_zqFNyoSOJch2; _tea_utm_cache_2018={%22utm_medium%22:%22search_ignore%22}; _ga=GA1.1.207924519.1723619465; local_city_cache=%E6%B2%A7%E5%B7%9E; csrftoken=1521db02874f63210fb369cdc28a92e8; odin_tt=ec179d5a3b437a3a8ca4dcf2e99b49d23ce6df57d629f0db6957fac2d410a9bcdb1343bad8b51c5a5026db23686f3eca; tt_anti_token=jsQmV8T81qpoX-9bbb04a513c0e05ac2009e3815892d9417810791d053729bd03c7877e158c48d; gfkadpd=24,6457; ttwid=1%7CCwirifcRn06d9oBvsutfw_PupIYzpcbuBviRiaK0_Z0%7C1724397231%7C5601e2e62c2f92739a33fa213b4d7bb2328af59851bed22db9f572da6e63fe5c; tt_scid=u25ByRUw-pwZK-6XxVf.ygJfJ0CbQYpq4uoQvgCE00SNyfNyp91Fq2AJkJ-gk9Qn6c62; _ga_QEHZPBE5HH=GS1.1.1724397210.6.1.1724397274.0.0.0'
if __name__ == '__main__':
    author_token_value = "MS4wLjABAAAAv_8iX87mTwGArnXFds96Yme-PDnuzsjXdMPckEFnIO8"
    max_behot_time = '0'

    for i in range(1, 10):
        print(f'Page {i}')
        data_list, max_behot_time = get_article_url(author_token_value, max_behot_time)
        print(f'Total {len(data_list)} items')

        # Example usage of toutiao_parse_content_images
        # for item in data_list:
        #     title = item.get('title', 'No title')
        #     article_id = item.get('id', 'No id')
        #     article_type = item.get('article_type', 'No article type')
        #     article_url = item.get('article_url', 'No article_url')
        #     tth_article_url = f'https://www.toutiao.com/article/{article_id}'

        #     print(title, article_id, tth_article_url, article_type, article_url)

        #     # article_url = 'https://www.toutiao.com/article/7382851930441155072'
        #     picture_urls, div_text = toutiao_parse_content_images(article_url)
        #     print(f"Picture URLs: {picture_urls}")
        #     print(f"Article Text: {div_text}")