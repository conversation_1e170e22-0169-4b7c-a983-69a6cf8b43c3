
import requests
import json
import re
from bs4 import BeautifulSoup
from lxml import etree
from typing import Tuple, List, Dict, Any
from util import time_util
import requests
import pandas as pd
import re
import time

from datetime import datetime,date
import json
import os
import re

from lxml import etree
import requests
from util import notion_util
from util import ids_util
from dotenv import load_dotenv
load_dotenv()
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA)


def parse_cookie_string(cookie_string: str) -> Dict[str, str]:
    """
    Parses a cookie string into a dictionary.

    Args:
        cookie_string (str): The cookie string.

    Returns:
        Dict[str, str]: A dictionary of cookie key-value pairs.
    """
    cookie_dict = {}
    for cookie_item in cookie_string.split(';'):
        if '=' in cookie_item:
            key, value = cookie_item.strip().split('=', 1)
            cookie_dict[key] = value
    return cookie_dict

def get_content_details(url):
    """获取豆瓣帖子详情"""
    cookie_str='bid=WIZ71hzQ7-I; douban-fav-remind=1; _pk_id.100001.8cb4=75979773c519e131.1725005058.; _pk_ses.100001.8cb4=1; ap_v=0,6.0; __utma=30149280.91831285.1725005059.1725005059.1725005059.1; __utmb=30149280.6.5.1725005059; __utmc=30149280; __utmz=30149280.1725005059.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmt=1'
    cookies=parse_cookie_string(cookie_str)
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    response = requests.get(url=url,headers=headers,cookies=cookies)
    html_content=response.text

    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找<script type="application/ld+json">标签
    script_tag = soup.find('script', type='application/ld+json')

    # 提取标签中的JSON数据
    json_ld_data = script_tag.string

    # 清理JSON字符串，去除不必要的换行符和空格
    cleaned_json_ld_data = json_ld_data.strip()
    cleaned_json_ld_data = cleaned_json_ld_data.replace('\r', '').replace('\n', '')


    # 解析JSON数据
    data = json.loads(cleaned_json_ld_data)

    # 提取text字段的内容
    text_content = data['text']
    url = data['url']
    commentCount = data['commentCount']
    #格式化年月日-避免Notion时区问题
    date_obj = datetime.fromisoformat(data['dateCreated'])
    dateCreated = date_obj.strftime('%Y-%m-%d')
    # dateCreated = data['dateCreated']
    name = data['name']
    content={
        'text':text_content,
        'url':url,
        'commentCount':commentCount,
        'dateCreated':dateCreated,
        'name':name
    }
    return content
    

def get_news_list(url):
    """获取豆瓣小组列表"""
    cookie_str='bid=WIZ71hzQ7-I; douban-fav-remind=1; _pk_id.100001.8cb4=75979773c519e131.1725005058.; _pk_ses.100001.8cb4=1; ap_v=0,6.0; __utma=30149280.91831285.1725005059.1725005059.1725005059.1; __utmb=30149280.6.5.1725005059; __utmc=30149280; __utmz=30149280.1725005059.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmt=1'
    cookies=parse_cookie_string(cookie_str)
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    response = requests.get(url=url,headers=headers,cookies=cookies)
    html_content=response.text

    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找所有的<tr>标签
    datas=[]
    tr_tags = soup.find_all('tr')
    for tr_tag in tr_tags:
        tds = tr_tag.find_all('td')
        data = {}
        for td in tds:
            if 'class' in td.attrs:
                class_name = td.attrs['class'][0]
                if class_name == 'title':
                    a_tag = td.find('a')
                    if a_tag:
                        data['title'] = a_tag.get_text(strip=True)
                        data['href'] = a_tag.get('href')
                elif class_name == 'time':
                    data['time'] = td.get_text(strip=True)
                elif class_name == 'r-count':
                    data['r-count'] = td.get_text(strip=True)
            else:
                a_tag = td.find('a')
                if a_tag:
                    data['author'] = a_tag.get_text(strip=True)
                    data['author_href'] = a_tag.get('href')
        print(data)
        if 'title' in data:
            #判断时间只要当天的
            if time_util.is_past_date(data['time']): continue 
            if data['r-count'] is not None and len(data['r-count'])>0 and int(data['r-count']) > 10:
                match = re.search(r'/topic/(\d+)/', data['href'])
                if match:
                    topic_id = match.group(1)
                    print(topic_id)
                    data['topic_id'] = topic_id
                datas.append(data)
    return datas



def saveNotion(rank):
    """存储的NBA数据库"""
    ids=ids_util.get_exist_ids('hot-news.json')
    for data in rank:
        id = data['topic_id']
        if id not in ids:
            title =data['title']
            text =data['text']
            if len(text)<50:continue
            page = {
                "title": title,
                'chinese_title': text,
                'prompt': title,
                'id': id,
                'area': '职场',
                'datasource': '豆瓣',
                'hmcturl': data['url'],
                'published_at': data['dateCreated'],
                # 'picture_url': image_url
            }
            newPage = notion.create_page_nba(page=page)
            page_id = newPage['id']
            ids_util.save_ids(title, id, 'success','hot-news.json')
            print(f'Save Notion: ID:{id},pageId: {page_id},title: {title}')


def main():
    # 小组:上班这件事
    datas=get_news_list('https://www.douban.com/group/myjob/discussion?start=0&type=new')
    for data in datas:
        time.sleep(2)
        contentDict=get_content_details(data['href'])
        data['text']=contentDict['text']
        data['url']=contentDict['url']
        data['commentCount']=contentDict['commentCount']
        data['dateCreated']=contentDict['dateCreated']
        data['name']=contentDict['name']
    try:
        saveNotion(datas)
    except Exception as e:
        print(f'异常:{e}')
  
main()