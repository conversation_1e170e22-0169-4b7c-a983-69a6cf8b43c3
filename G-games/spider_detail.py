# https://www.onlinegames.io/survival-island/

import requests
from bs4 import BeautifulSoup
import json
import re

def get_game_data(url):
    """
    Extract iframe URL, images, and text content from a game page on onlinegames.io
    """
    # Initialize data structure
    data = {
        "iframe_url": "",
        "images": [],
        "description": "",
        "tags": []
    }
    
    # Make request to the webpage
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        # Parse HTML content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract text content from post__entry class
        post_entry = soup.find('div', class_='post__entry')
        if post_entry:
            data['description'] = post_entry.get_text(strip=True)
        
        # Find iframe URL
        iframe = soup.find('iframe', id='gameFrame')
        if iframe and 'src' in iframe.attrs:
            data['iframe_url'] = iframe['src']
        
        # Find all images within elements with class="post"
        post_elements = soup.find_all(class_="post")
        for post in post_elements:
            for img in post.find_all('img'):
                if 'src' in img.attrs:
                    # Extract the image source
                    src = img['src']
                    if not src.endswith('.svg') and not re.match(r'.*icon.*', src, re.IGNORECASE):
                        # Make sure URLs are absolute
                        if src.startswith('/'):
                            src = f"https://www.onlinegames.io{src}"
                        data['images'].append({
                            'url': src,
                            'alt': img.get('alt', ''),
                            'width': img.get('width', ''),
                            'height': img.get('height', '')
                        })

        # 提取标签数据 - 查找 <ul class="post__tag"> 元素内的所有标签
        try:
            post_tag_ul = soup.find('ul', class_='post__tag')
            if post_tag_ul:
                # 提取ul元素内所有的标签文本内容
                tag_elements = post_tag_ul.find_all('li') or post_tag_ul.find_all('a')
                for tag_element in tag_elements:
                    tag_text = tag_element.get_text(strip=True)
                    if tag_text:  # 确保标签文本不为空
                        data['tags'].append(tag_text)
                print(f"成功提取到 {len(data['tags'])} 个标签")
            else:
                print("未找到 post__tag 元素")
        except Exception as e:
            print(f"提取标签时出错: {e}")
            # 即使出错也继续执行，保持tags为空列表

        return data
    
    except Exception as e:
        print(f"Error fetching data: {e}")
        return data


def main(url,csv_file):
    data = get_game_data(url)
    
    # Print results
    print("\n===== GAME DATA =====\n")
    print(f"IFRAME URL: {data['iframe_url']}\n")
    
    print(f"IMAGES ({len(data['images'])}):\n")
    for i, img in enumerate(data['images'], 1):
        print(f"{i}. {img['url']}")
        if img['alt']:
            print(f"   Alt text: {img['alt']}")
        if img['width'] and img['height']:
            print(f"   Dimensions: {img['width']}x{img['height']}")
        print()
    
    print(f"DESCRIPTION:\n{data['description']}\n")

    print(f"TAGS ({len(data['tags'])}):")
    if data['tags']:
        for i, tag in enumerate(data['tags'], 1):
            print(f"{i}. {tag}")
    else:
        print("No tags found.")
    print()
    if len(data["images"]) == 0:
        print("No images found. Skipping this game.")
        return
    image_url=data["images"][-1]["url"]
    prompt=f'很好。 继续增加新的游戏。 内容参考链接： @Web  @{url}\n嵌入iframe是：	 @{data["iframe_url"]}\n背景图片是：	 @{image_url}'
    print(prompt)
    
    # Save to CSV file
    import csv
    import os
    
    csv_file = os.path.join(os.path.dirname(__file__),csv_file)
    file_exists = os.path.isfile(csv_file)
    
    with open(csv_file, 'a', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        
        # Write header if file doesn't exist
        if not file_exists:
            writer.writerow(['URL', 'Prompt','Image URL','Iframe URL','Description','Tags'])

        # Write data row
        tags_str = ', '.join(data['tags']) if data['tags'] else ''
        writer.writerow([url, prompt,image_url,data["iframe_url"],data["description"],tags_str])
    
    print(f"\nData saved to {csv_file}")

    print("\n===== END =====\n")
# Main execution
if __name__ == "__main__":
    url = "https://www.onlinegames.io/survival-island/"
    main(url,"game_data_test.csv")
