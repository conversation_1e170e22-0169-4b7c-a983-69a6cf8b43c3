import requests
def test():
    url='https://36kr.com/feed-article'
    response = requests.get(url)
    # print(response.text)
    return response.text


import xml.etree.ElementTree as ET
import json

def xml2json(node):
    if not isinstance(node, ET.Element):
        raise Exception("node format error.")

    if len(node) == 0:
        return node.tag, node.text

    data = {}
    temp = None
    for child in node:
        key, val = xml2json(child)
        if key in data:
            if type(data[key]) == list:
                data[key].append(val)
            else:
                temp = data[key]
                data[key] = [temp, val]
        else:
            data[key] = val

    return node.tag, data

import xml.etree.ElementTree as ET
def xml_to_dict(element):
    """将XML元素转换为字典"""
    if len(element.attrib) > 0:
        result = {element.tag: {**element.attrib, **xml_to_dict(element)}}
    elif element.text:
        result = {element.tag: element.text.strip()}
    else:
        children = [xml_to_dict(child) for child in element]
        if len(children) == 1 and list(children[0])[0] == element.tag:
            # 如果只有一个子节点且其标签与父节点相同，则展开列表
            result = {element.tag: children[0][element.tag]}
        else:
            result = {element.tag: children}
    return result
def xml_to_json(xml_string):
    """将XML字符串转换为JSON字符串"""
    root = ET.fromstring(xml_string)
    data_dict = xml_to_dict(root)
    return json.dumps(data_dict, indent=2)
def parse_subscribed_xml(xml_data):
    """解析订阅的XML数据并转换为JSON"""
    root = ET.fromstring(xml_data)
    data_dict = xml_to_dict(root)
    return json.dumps(data_dict, indent=2)
def main():
    path = "./temp.xml"
    xml_data=test()
    result=parse_subscribed_xml(xml_data)
    print(result)
    
    tree = ET.parse(path)
    node = tree.getroot()
    tag, data = xml2json(node)

    f = open("./temp.json", "w", encoding="utf-8")
    f.write(json.dumps(data, ensure_ascii=False, indent=4))
    f.close()

main()