# Import necessary libraries
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
import time


# 单个
class apple():
    try:
        # Create a new instance of the Firefox driver
        driver = webdriver.Chrome(executable_path='/usr/local/bin/chromedriver')  # 声明调用Chrome

        driver.get("https://appleid.apple.com/account#!&page=create")

        # todo-fwh-无法获取输入框的元素-无法赋值

        # Find the registration form elements and fill them in
        elements = driver.find_element_by_class_name("form-element")
        print(elements)

        first_name = driver.find_element_by_id("firstName")
        # first_name = driver.find_element_by_id("input-*************-1")
        first_name.send_keys("<PERSON>")

        last_name = driver.find_element_by_id("lastName")
        last_name.send_keys("Doe")

        email = driver.find_element_by_id("email")
        email.send_keys("<EMAIL>")

        password = driver.find_element_by_id("password")
        password.send_keys("password123")

        confirm_password = driver.find_element_by_id("confirmPassword")
        confirm_password.send_keys("password123")

        # Select the security question and answer
        security_question = Select(driver.find_element_by_id("question"))
        security_question.select_by_visible_text("What was your first pet's name?")

        security_answer = driver.find_element_by_id("answer")
        security_answer.send_keys("Fluffy")

        # Submit the form
        submit_button = driver.find_element_by_id("submitButton")
        submit_button.click()

        # Wait for the registration to complete
        time.sleep(10)

        # Close the webdriver
        driver.quit()
    finally:
        driver.quit()


# 批量
class batchApple():
    # Set up the webdriver
    # driver = webdriver.Chrome()
    driver = webdriver.Chrome(executable_path='/usr/local/bin/chromedriver')  # 声明调用Chrome
    # Define a list of account details to register
    account_details = [
        {"first_name": "John", "last_name": "Doe", "email": "<EMAIL>", "password": "password123",
         "security_question": "What was your first pet's name?", "security_answer": "Fluffy"},
        {"first_name": "Jane", "last_name": "Doe", "email": "<EMAIL>", "password": "password123",
         "security_question": "What was your first car?", "security_answer": "Toyota"},
        {"first_name": "Bob", "last_name": "Smith", "email": "<EMAIL>", "password": "password123",
         "security_question": "What is your favorite color?", "security_answer": "Blue"}
    ]

    # Loop through the account details and register each account
    for account in account_details:
        driver.get("https://appleid.apple.com/account#!&page=create")

        first_name = driver.find_element_by_id("firstName")
        first_name.send_keys(account["first_name"])

        last_name = driver.find_element_by_id("lastName")
        last_name.send_keys(account["last_name"])

        email = driver.find_element_by_id("email")
        email.send_keys(account["email"])

        password = driver.find_element_by_id("password")
        password.send_keys(account["password"])

        confirm_password = driver.find_element_by_id("confirmPassword")
        confirm_password.send_keys(account["password"])

        security_question = Select(driver.find_element_by_id("question"))
        security_question.select_by_visible_text(account["security_question"])

        security_answer = driver.find_element_by_id("answer")
        security_answer.send_keys(account["security_answer"])

        submit_button = driver.find_element_by_id("submitButton")
        submit_button.click()

        time.sleep(10)

    # Close the webdriver
    driver.quit()


if __name__ == '__main__':
    apple()
    # batchApple()
