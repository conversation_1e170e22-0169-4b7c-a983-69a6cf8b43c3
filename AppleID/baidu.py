# Import necessary libraries
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class apple():
    # Create a new instance of the Firefox driver
    # driver = webdriver.Firefox()
    driver = webdriver.Chrome(executable_path='/usr/local/bin/chromedriver')  # 声明调用Chrome

    # Navigate to the Baidu website
    driver.get("https://www.baidu.com/")

    # Find the search box element
    search_box = driver.find_element_by_name("wd")

    # Enter a search query
    search_box.send_keys("data cursor")

    # Submit the search query
    search_box.submit()

    # Wait for the search results to load
    search_results = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "content_left"))
    )

    # Click the first search result
    first_result = search_results.find_element_by_tag_name("h3").find_element_by_tag_name("a")
    first_result.click()


if __name__ == '__main__':
    apple()
